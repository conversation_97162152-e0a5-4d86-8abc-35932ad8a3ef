---
creation_date: 2025-04-05
modification_date: 2025-04-21
type: project
status: active
priority: medium
deadline: 2025-05-15
project_owner: Jordan
project_client: Family
completion_percentage: 60
estimated_hours: 30
tags: [para/projects, media, streaming, server, jellyfin, zerotier, vpn, docker, linux, pop-os, networking, entertainment]
area: Network-Administration
start_date: 2025-04-05
# Custom fields
server_ip: "*************"
server_port: 8096
operating_system: "Pop-OS"
software: ["Je<PERSON><PERSON>", "ZeroTier One", "Docker", "UFW"]
---

# Media Streaming Server Setup

## Overview
Project to set up a media streaming server on a VPN (ZeroTier) network, allowing access to media content from anywhere while connected to the VPN.

## System Configuration
- **Operating System**: Pop-OS
- **Media Server Software**: Jellyfin
- **VPN Solution**: ZeroTier One
- **Remote Access**: SSH and ThinLinc for remote desktop
#### **UFW Firewall**
**ufw**
	- rules as of 29/04/25 are found here [[popserver or laptop server pre april (before wipe and installation of enhance control panel)]]:
	- user rules configuration also here [[popserver or laptop server pre april (before wipe and installation of enhance control panel)]]

## Objectives
- Set up Jellyfin server on Pop-OS
- Configure ZeroTier for secure remote access
- Enable streaming from anywhere while on the VPN
- Configure Jellyfin apps on client devices
- Ensure optimal performance and security

## Tasks
- [ ] Install and configure Jellyfin on Pop-OS
- [ ] Set up ZeroTier network for secure access
- [ ] Configure firewall rules to allow Jellyfin traffic
- [ ] Test streaming performance over VPN
- [ ] Set up Jellyfin apps on client devices
- [ ] Document network configuration for future reference
- [ ] Implement backup solution for media library

## Technical Notes
```
# Jellyfin server setup
sudo apt update
sudo apt install jellyfin

# ZeroTier configuration
sudo apt install zerotier-one
sudo zerotier-cli join [network-id]

# Firewall configuration
sudo ufw allow from [zerotier-subnet] to any port 8096 proto tcp
```

## Client Setup
- Install Jellyfin app on client devices
- Configure app to connect to server via ZeroTier IP
- Test streaming on different network conditions

## Related
- [[Family/Network Architecture Overview]]
- [[1-Projects]]
- [[Bad Media Influences]]
