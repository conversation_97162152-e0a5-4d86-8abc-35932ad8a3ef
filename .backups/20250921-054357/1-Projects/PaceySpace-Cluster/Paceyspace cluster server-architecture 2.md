# Reverse Proxy Cluster Architecture Plan

## Hardware Analysis

**Server 1 (ASUS TUF A16):**
- AMD Ryzen 7 4800H (8C/16T, 2.9-4.2GHz)
- 16GB RAM, NVIDIA GTX 1650Ti
- Currently: Very low utilization (~2GB RAM used)
- Best for: Compute-intensive tasks, virtualization

**Server 2 (Desktop):**
- Intel i7-7700 (4C/8T, 3.6-4.2GHz)
- 16GB RAM
- Currently: Running extensive Docker containers
- Best for: Container orchestration, web services

## Recommended Architecture

### Option 1: Hybrid Cloud Architecture (Recommended)

```
Internet
    ↓
VPS/Cloud (Entry Point)
    ├── Nginx Proxy Manager
    ├── Cloudflare Tunnel (backup)
    └── SSL Termination
    ↓
Home Network
    ├── Server 1 (Proxmox Host)
    │   ├── Management VMs
    │   ├── Mail Server VM
    │   ├── Document Management VM
    │   └── Monitoring Stack
    └── Server 2 (Docker Host)
        ├── Portainer
        ├── WordPress Sites
        ├── Enhance Control Panel
        └── Database Services
```

### Option 2: Pure Self-Hosted Architecture

```
Internet
    ↓
Server 1 (Proxmox + Reverse Proxy)
    ├── Reverse Proxy VM (Nginx/Traefik)
    ├── Mail Server VM
    ├── Control Panel VM
    └── Management Services
    ↓
Server 2 (Docker Cluster)
    ├── WordPress Sites
    ├── Portainer
    ├── Document Management
    └── Application Services
```

## Detailed Implementation Plan

### Phase 1: Core Infrastructure

**Server 1 Setup (Proxmox Host):**
```bash
# Install Proxmox VE
wget https://enterprise.proxmox.com/iso/proxmox-ve_8.1-2.iso
# Create VMs for:
# - Reverse Proxy (4GB RAM, 2 CPU)
# - Mail Server (8GB RAM, 4 CPU)
# - Management Services (4GB RAM, 2 CPU)
```

**Server 2 Setup (Docker Host):**
```bash
# Keep existing Ubuntu 24.04
# Install Docker Compose
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# Install Portainer
docker volume create portainer_data
docker run -d -p 8000:8000 -p 9443:9443 \
    --name portainer --restart=always \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v portainer_data:/data \
    portainer/portainer-ee:latest
```

### Phase 2: Reverse Proxy Configuration

**Option A: Nginx Proxy Manager (Easier)**
```yaml
# docker-compose.yml for NPM
version: '3.8'
services:
  nginx-proxy-manager:
    image: 'jc21/nginx-proxy-manager:latest'
    restart: unless-stopped
    ports:
      - '80:80'
      - '81:81'
      - '443:443'
    volumes:
      - ./data:/data
      - ./letsencrypt:/etc/letsencrypt
```

**Option B: Traefik (Advanced)**
```yaml
# docker-compose.yml for Traefik
version: '3.8'
services:
  traefik:
    image: traefik:v3.0
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./traefik.yml:/traefik.yml
      - ./acme.json:/acme.json
```

### Phase 3: Service Distribution

**Server 1 (Proxmox VMs):**

1. **Reverse Proxy VM** (2GB RAM, 2 CPU)
   - Nginx Proxy Manager or Traefik
   - SSL certificate management
   - Rate limiting and security

2. **Mail Server VM** (4GB RAM, 2 CPU)
   - Mail-in-a-Box or iRedMail
   - SMTP, IMAP, webmail
   - Anti-spam and security

3. **Management VM** (2GB RAM, 2 CPU)
   - Monitoring (Prometheus + Grafana)
   - Backup services
   - VPN server (WireGuard)

**Server 2 (Docker Services):**

1. **WordPress Stack**
   ```yaml
   services:
     wordpress:
       image: wordpress:latest
       environment:
         WORDPRESS_DB_HOST: mariadb
         WORDPRESS_DB_PASSWORD: password
     
     mariadb:
       image: mariadb:latest
       environment:
         MYSQL_ROOT_PASSWORD: password
   ```

2. **Enhance Control Panel**
   - Dedicated container for WordPress management
   - Database management tools
   - File management

3. **Document Management** (Future)
   - NextCloud or Paperless-ngx
   - Dedicated storage volume

### Phase 4: External Services Integration

**VPS/Cloud Services (Optional but Recommended):**

1. **Entry Point VPS** ($5-10/month)
   - Nginx reverse proxy
   - DDoS protection
   - Global CDN integration
   - Uptime monitoring

2. **Benefits:**
   - Static IP address
   - Better uptime SLA
   - DDoS protection
   - Easier SSL management
   - ISP-independent

## Network Security Configuration

### Firewall Rules
```bash
# Server 1 (Proxmox)
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow 8006/tcp  # Proxmox Web UI
ufw deny incoming
ufw allow outgoing

# Server 2 (Docker)
ufw allow 22/tcp       # SSH
ufw allow 9443/tcp     # Portainer
ufw allow from SERVER1_IP  # Internal communication
```

### Monitoring Stack
```yaml
# monitoring/docker-compose.yml
services:
  prometheus:
    image: prom/prometheus
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
  
  grafana:
    image: grafana/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    ports:
      - "3000:3000"
  
  node-exporter:
    image: prom/node-exporter
    network_mode: host
```

## Backup Strategy

### Automated Backups
```bash
# Proxmox VM backups
vzdump --mode snapshot --storage local --compress lzo

# Docker volume backups
docker run --rm -v wordpress_data:/data -v $(pwd):/backup \
    ubuntu tar czf /backup/wordpress_backup.tar.gz -C /data .
```

## Implementation Timeline

**Week 1-2: Foundation**
- Install Proxmox on Server 1
- Set up basic networking
- Create first VMs

**Week 3-4: Core Services**
- Configure reverse proxy
- Set up Docker on Server 2
- Deploy Portainer

**Week 5-6: WordPress & Control Panel**
- Deploy WordPress sites
- Configure Enhance control panel
- Set up SSL certificates

**Week 7-8: Advanced Services**
- Mail server setup
- Monitoring implementation
- Security hardening

## Cost Considerations

**Pure Self-Hosted:** $0/month
- Pros: Full control, no recurring costs
- Cons: Single point of failure, ISP dependencies

**Hybrid with VPS:** $5-15/month
- Pros: Better reliability, professional setup
- Cons: Additional cost, complexity

**Recommended VPS Providers:**
- DigitalOcean: $6/month (1GB RAM, 25GB SSD)
- Vultr: $6/month (1GB RAM, 25GB SSD)
- Linode: $5/month (1GB RAM, 25GB SSD)

## Future Expansion Path

1. **Document Management:** NextCloud or Paperless-ngx
2. **Mail Server:** Mail-in-a-Box on dedicated VM
3. **Media Server:** Plex/Jellyfin on Server 1
4. **CI/CD Pipeline:** GitLab or Jenkins
5. **Database Cluster:** MySQL/PostgreSQL clustering

This architecture provides excellent scalability, reliability, and separation of concerns while maximizing your hardware utilization.