# Python MCP Server Workflow

## Overview
The Vector Store uses #qdrant hosting a #multi #tenanted and #multilayered and #embedding structure. Scripps for using the embedding model for various locations reside in the #MCP project folder in iCloud Drive:

## Objectives for Standard Operating Procedures
  1. embed_documents.py - Interactive embedding script with
  Qwen3-8B support (4096 dimensions)
  2. qdrant_strategy.py - Multi-tenant collection strategy with
  20 collections across 4 strategies
  3. shared_context_manager.py - Cross-agent context management
  and conflict resolution
  4. run_embedding.py - Direct embedding script for automated
  processing
  5. EMBEDDING_USAGE.md - Comprehensive usage documentation

## Success Criteria
- MCP server successfully processes requests from ALl sources
- Context is maintained across different development environments
- Custom tools work correctly and improve productivity
- Documentation conversion tools successfully process GitHub repositories
- System is stable and maintainable

## Tasks

## Timeline
- **Start Date**: [[2025-09-18]] 10:49
- [ ] **Deadline**: [[2025-09-13]] 12:00 
- **Milestones**:
  - [ ] Research and Planning - [[2024-05-15]]
  - [ ] Basic MCP Server Implementation - [[2024-05-17]]
  - [ ] Advanced Tools Development - [[2024-05-20]]
  - [ ] Integration and Testing - [[2024-05-22]]
  - [ ] Documentation and Completion - [[2024-05-24]]

## Resources
- [MCP Server Setup and Utilization Guide](notes-uca-obsidian/3-Resources/Prompt Engineering/MCP Server Setup and Utilization.md)
- [Detailed Plan for Setting Up a Personalized MCP Server](notes-uca-obsidian/3-Resources/Prompt Engineering/Detailed Plan for Setting Up a Personalized MCP Server.md)
- [MCP Setup Guide](notes-uca-obsidian/3-Resources/Prompt Engineering/MCP Setup Guide.md)
- [MCP Customization](notes-uca-obsidian/3-Resources/Prompt Engineering/MCP Customization.md)
- [Developer Profile](notes-uca-obsidian/3-Resources/Prompt Engineering/Developer Profile.md)
- Official MCP SDK Documentation (to be added)
- Cursor AI Documentation (to be added)
- Augment AI Documentation (to be added)

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(file.name, "Software-Development") OR contains(file.name, "Automation")
```

## Related Resources
```dataview
LIST
FROM "3-Resources/Prompt Engineering"
WHERE contains(tags, "mcp") OR contains(tags, "prompt-engineering") OR contains(tags, "workflow")
LIMIT 5
```

## Meeting Notes
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  date as "Date",
  participants as "Participants"
FROM #meeting
WHERE contains(related, "Python MCP Server Workflow") OR contains(file.name, "MCP Server")
SORT date DESC
```

## Progress Updates
### 2025-04-16 - Initial Setup
- Project created
- Initial planning started
- Research on MCP SDK begun

## Create Related Notes
- [[MCP Server Meeting 2025-04-16|Create Meeting Note]]
- [[MCP Server Resource|Create Resource Note]]
- [[MCP Server Documentation|Create Documentation]]

## Related
- [[1-Projects]]
- [[Projects TOC]]
- [[Software-Development Overview]]
- [[MCP Server Setup and Utilization]]
- [[Automation Overview]]