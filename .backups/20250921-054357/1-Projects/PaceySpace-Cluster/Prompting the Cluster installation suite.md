```
I want to setup a robust, minimal maintainence and secure email server. my current cluster setup is as follows: [node1(orion) => 192.168.0.92] | [*thisserver*2(sirius) => 192.168.0.133] this bare Metal server is running proxmox so we should make a new proxy virtual environment for the mail server and use docker for convenience. We should install <PERSON> so we can manage all these docker containers. I'm going to be deploying a whole heap of docker containers shortly.

so, we should install portianer first. I'm not sure how we should do that with proxmox, please refer to the documentation for that. 
```