---
creation_date: 2025-06-24
modification_date: 2025-06-24
type: project
status: active
priority: medium
deadline: 2025-07-24
project_owner: Jordan
project_client: Personal
completion_percentage: 0
estimated_hours: 10
tags: [para/projects, business software]
area: enterprise
start_date: 2025-06-24
---

# Self Hosted Installation Roadmap

## Overview
<!-- Brief description of the project -->
**Here’s a curated list of the most comprehensive open-source, self-hostable solutions for small teams with an emphasis on:**
- Personal information management
- Fast note capture
- Automation & reporting
- Visual time tracking & budgeting
- Collaboration & shared understanding
- Assistance with prioritisation & deadlines

---
### **🔷** 
### **Top All-in-One Recommendation**

#### **OpenProject**

**Why it stands out:**
- ✅ Gantt charts, task tracking, timelines, agile boards
- ✅ Built-in **time tracking**, **budgeting**, and **cost reporting**
- ✅ **Wikis and meeting notes** (with exportable PDFs)
- ✅ Prioritisation tools and work breakdown
- ✅ Powerful role-based **collaboration**
- ✅ **Self-hostable** with Docker or packages
    

**Downsides:**
- Somewhat heavier UI — takes getting used to
- Can feel more “enterprise-grade” than lightweight tools
    

**Best for:** Teams that need a well-structured, multi-functional workspace with strong project discipline.

🔗 [https://www.openproject.org/](https://www.openproject.org/)

---

### **🧠** 

### **For Notes + Organization + Automation**
#### **Joplin Server + Plugins**

**Why it’s great:**
- 📝 Encrypted markdown note-taking with fast keyboard input
- ✅ **Web clipper**, to-do lists, notebooks, tags
- 🔄 Plugins for **automation** and **daily notes**
- 📊 Integrate with Joplin Plugin like “Note Overview” for mini dashboards
- 🌐 Host your own Joplin Server for sync across devices

**Downsides:**

- Not a full project manager on its own — but excellent for personal information management
- Needs other tools for time tracking or task collaboration
    

**Best for:** Daily journaling, capturing fleeting thoughts, automating structure.

🔗 [https://joplinapp.org](https://joplinapp.org)

---
### **🧩** 
### **Modular Workspace**

#### **AppFlowy**

**Why it’s special:**
- A true open-source Notion alternative
- 🚀 Quick capture, drag/drop layout, visual databases
- ✅ Task boards, note blocks, daily journaling
- 🌿 Native desktop app, performant even self-hosted
- API and plugin system growing for future automation
    

**Downsides:**
- Still maturing — not as fully featured as Notion yet
- Limited time/budget tracking integrations (for now)

**Best for:** Visual thinkers who want to combine structured info + freeform notes.

🔗 [https://appflowy.io](https://appflowy.io)

---

### **⏳** 

### **Time Tracking + Budget + Prioritisation**
#### **Kimai**

**Why it’s excellent:**
- 📅 Timesheets with tags, projects, clients, activities
- 📈 Exports to PDF, Excel, and CSV
- 🧾 Built-in **invoicing** and **budget tracking**
- 🧠 Visualise what you’re spending time on
- 🔗 Can be linked to project tools like OpenProject or JIRA

**Downsides:**
- It’s focused on time — not task or note management

**Best for:** When you need reliable, visual time and finance tracking.
🔗 [https://www.kimai.org](https://www.kimai.org)

---
### **🌐** 

### **Integrated Collaboration Suite**

#### **Nextcloud + Deck + Notes + Forms + Talk**

**Why it’s a full ecosystem:**
- 🧠 Notes app for quick capture
- 📋 Deck = Kanban task boards
- 🗂️ Files and calendars
- 🗣️ Talk for team chat and video
- 📊 Forms for data collection
- 🔄 Syncs across devices, access control per user/team
    

**Downsides:**
- More setup involved, especially to get plugins right
- Not as tailored for budgets or time reporting as OpenProject
    
**Best for:** Teams that need flexible collaboration and centralised tools with privacy.
🔗 [https://nextcloud.com](https://nextcloud.com)

---
### **🧠 Your Ideal Stack Might Be:**

| **Function**               | **Tool**                                        |
| -------------------------- | ----------------------------------------------- |
| **Project Management**     | OpenProject or Taiga                            |
| **Quick Note Capture**     | Joplin or AppFlowy                              |
| **Time & Budget Tracking** | Kimai                                           |
| **Team Collaboration**     | Nextcloud or integrate tools via Docker/Traefik |
| **Visual Boards**          | Wekan (Kanban) or Deck (Nextcloud)              |

---
If you want, I can help you plan:
- An install roadmap (Docker-compose or bare metal)
- How to link tools together
- How to create workflows for capturing → organising → reporting
---

## Objectives
<!-- What are you trying to achieve? -->
-

## Success Criteria
<!-- How will you know when the project is successful? -->
-
 
## Tasks
<!-- List of tasks to complete -->
- [ ] complete the [[#**🧭 Self-Hosted Productivity & Project Management Suite**]] guide below for installation of the business software:
# **🧭 Self-Hosted Productivity & Project Management Suite**

  

## **📋 Overview**

  

This guide outlines how to install and manage a comprehensive, open-source, self-hosted software suite suitable for small teams using **Ubuntu Server 24.04** with the **Enhance Control Panel**.

  

You’ll be setting up:

- **OpenProject** for project planning, time tracking, and reporting
    
- **Joplin Server** for fast note capture and personal information management
    
- **Kimai** for visual time and budget tracking
    
- **Nextcloud** (with Deck, Notes, Talk) for collaboration and cloud storage
    

  

We will use **Docker Compose** for deployment, and optionally virtualize components later using **Proxmox** if your infrastructure scales.

---

## **🛠️ 0. Prerequisites**

- ✅ Ubuntu Server 24.04 with root/sudo access
    
- ✅ Enhance Control Panel installed and operational
    
- ✅ Domain(s) or subdomains ready to assign
    
- ✅ At least 8GB RAM, 100GB+ storage recommended
    
- ✅ Access to DNS records (e.g. Cloudflare, Enhance DNS)
    

---

## **🧩 1. Use Enhance Control Panel?**

  

Enhance is great for web hosting and email management, but **not ideal for app orchestration** with containers. We recommend:

- ✔️ Use Enhance for DNS, SSL, and Web UI access for simpler domains
    
- ❌ Avoid using it to directly install or manage Docker apps
    
- ✔️ Host container stacks under a subdomain (e.g., apps.yourdomain.com) or wildcard
    

  

> **TIP:** Consider setting up a dedicated subdomain or VPS not managed by Enhance for your Docker services.

---

## **🐳 2. Set Up Docker + Docker Compose**

```
sudo apt update && sudo apt install -y docker.io docker-compose
sudo systemctl enable --now docker
sudo usermod -aG docker $USER  # Log out and back in
```

Verify:

```
docker --version
docker-compose --version
```

---

## **📂 3. Directory Structure for Your Vault**

```
/opt/selfhosted
├── openproject
├── joplin
├── kimai
├── nextcloud
├── proxy (e.g., Nginx Proxy Manager)
```

> Each folder will contain docker-compose.yml, .env, and config files

---

## **🧱 4. Deploy Reverse Proxy with SSL (Optional but Recommended)**

  

Use **Nginx Proxy Manager** for HTTPS and domain routing:

```
mkdir -p /opt/selfhosted/proxy && cd $_
curl -L https://raw.githubusercontent.com/NginxProxyManager/nginx-proxy-manager/develop/docker-compose.yml -o docker-compose.yml
docker-compose up -d
```

Assign apps like openproject.yourdomain.com etc.

---

## **📌 5. Install OpenProject (via Docker)**

```
mkdir -p /opt/selfhosted/openproject && cd $_
```

**docker-compose.yml:**

```
version: '3'
services:
  openproject:
    image: openproject/community:latest
    restart: always
    ports:
      - "8080:80"
    volumes:
      - openproject_data:/var/openproject
volumes:
  openproject_data:
```

Launch:

```
docker-compose up -d
```

Visit http://yourserverip:8080 or route it via proxy.

---

## **🧠 6. Install Joplin Server**

```
mkdir -p /opt/selfhosted/joplin && cd $_
```

**docker-compose.yml:**

```
version: '3'
services:
  db:
    image: postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: joplinpass
      POSTGRES_USER: joplin
      POSTGRES_DB: joplin
    volumes:
      - joplin_postgres:/var/lib/postgresql/data

  app:
    image: joplin/server:latest
    restart: always
    ports:
      - "22300:22300"
    environment:
      APP_BASE_URL: "https://joplin.yourdomain.com"
      DB_CLIENT: pg
      POSTGRES_PASSWORD: joplinpass
      POSTGRES_DATABASE: joplin
      POSTGRES_USER: joplin
      POSTGRES_PORT: 5432
      POSTGRES_HOST: db
volumes:
  joplin_postgres:
```

Launch:

```
docker-compose up -d
```

---

## **⏱️ 7. Install Kimai (Time Tracker)**

```
mkdir -p /opt/selfhosted/kimai && cd $_
```

**docker-compose.yml:**

```
version: '3'
services:
  kimai:
    image: kimai/kimai2:apache
    restart: always
    environment:
      - ADMINMAIL=<EMAIL>
      - ADMINPASS=yourstrongpassword
    ports:
      - "8081:8001"
```

Launch:

```
docker-compose up -d
```

---

## **☁️ 8. Install Nextcloud + Deck + Notes**

```
mkdir -p /opt/selfhosted/nextcloud && cd $_
```

Use the official Docker Compose setup: https://github.com/nextcloud/docker

  

Or use:

```
docker run -d -p 8082:80 --name nextcloud \
  -v nextcloud:/var/www/html \
  -v nextcloud_data:/var/www/html/data \
  nextcloud
```

Add Deck + Notes apps from the web UI under apps.

---

## **🚀 9. (Optional) Scale with Proxmox Later**

  

You don’t need Proxmox right away — only consider it if:

- You want to **run multiple VMs for isolation**
    
- You’re planning **HA or backup** across nodes
    
- You want to snapshot and roll back apps
    

  

🧠 For now: Stick with Docker and maybe backup the /opt/selfhosted directory with rsync or Borg.

---

## **📓 10. Notes & Next Steps**

- 🔄 Automate daily note capture in Joplin
- 🧾 Use Kimai to tag time entries and export CSV reports
- ✅ Link OpenProject → Git / Kanban workflow
- 🔄 Sync calendar between OpenProject ↔ Nextcloud
- 🔐 Enable HTTPS + basic auth everywhere
    

---

## **📦 Backup & Maintenance**

- Use docker-compose down to safely shut down apps
    
- Backup volumes under /var/lib/docker/volumes/ or use bind mounts
    
- Automate with cron, rsync, or restic
    

```Suggestion
Let me know if you’d like a Docker Compose bundle or Obsidian template for all of this!
```
---
---
## Timeline
- **Start Date**: 2025-06-24
- **Deadline**: 2025-07-24
- **Milestones**:
  - [ ] Initial Planning - 2025-07-01
  - [ ] Development - 2025-07-08
  - [ ] Testing - 2025-07-15
  - [ ] Completion - 2025-07-24

## Resources
<!-- Links to relevant resources -->
-

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(file.name, "Self Hosted Installation Roadmap") OR area = "Personal"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(file.content, "[[Self Hosted Installation Roadmap]]") OR contains(tags, "personal")
LIMIT 5
```

## Meeting Notes
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  date as "Date",
  participants as "Participants"
FROM #meeting
WHERE contains(related, "Self Hosted Installation Roadmap") OR contains(file.name, "Self Hosted Installation Roadmap")
SORT date DESC
```

## Progress Updates
<!-- Regular updates on project progress -->
### 2025-06-24 - Initial Setup
- Project created
- Initial planning started

## Quick Links
- [[Self Hosted Installation Roadmap Meeting|New Meeting]]
- [[Self Hosted Installation Roadmap Resource|New Resource]]
- [[1-Projects|All Projects]]
