---
title: "tensorzero/tensorzero: TensorZero is an open-source stack for industrial-grade LLM applications. It unifies an LLM gateway, observability, optimization, evaluation, and experimentation."
source: "https://github.com/tensorzero/tensorzero"
author:
  - "[[Aaron1011]]"
published:
created: 2025-09-07
description: "TensorZero is an open-source stack for industrial-grade LLM applications. It unifies an LLM gateway, observability, optimization, evaluation, and experimentation. - tensorzero/tensorzero"
tags:
  - "clippings"
---
**[tensorzero](https://github.com/tensorzero/tensorzero)** Public

TensorZero is an open-source stack for industrial-grade LLM applications. It unifies an LLM gateway, observability, optimization, evaluation, and experimentation.

[tensorzero.com](https://tensorzero.com/ "https://tensorzero.com")

[Apache-2.0 license](https://github.com/tensorzero/tensorzero/blob/main/LICENSE)

[Open in github.dev](https://github.dev/) [Open in a new github.dev tab](https://github.dev/) [Open in codespace](https://github.com/codespaces/new/tensorzero/tensorzero?resume=1)

<table><thead><tr><th colspan="2"><span>Name</span></th><th colspan="1"><span>Name</span></th><th><p><span>Last commit message</span></p></th><th colspan="1"><p><span>Last commit date</span></p></th></tr></thead><tbody><tr><td colspan="3"><p><span><a href="https://github.com/tensorzero/tensorzero/commit/d936e414e546b5d1403da3ce592ba5497b871cba">Gate ResolvedInput 'Serialized' impl behind 'pyo3' feature (</a><a href="https://github.com/tensorzero/tensorzero/pull/3429">#3429</a><a href="https://github.com/tensorzero/tensorzero/commit/d936e414e546b5d1403da3ce592ba5497b871cba">)</a></span></p><p><span><a href="https://github.com/tensorzero/tensorzero/commit/d936e414e546b5d1403da3ce592ba5497b871cba">d936e41</a> ·</span></p><p><a href="https://github.com/tensorzero/tensorzero/commits/main/"><span><span><span>1,827 Commits</span></span></span></a></p></td></tr><tr><td colspan="2"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/.buildkite">.buildkite</a></p></td><td colspan="1"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/.buildkite">.buildkite</a></p></td><td><p><a href="https://github.com/tensorzero/tensorzero/commit/9ef0cd8ff7bf7de3ae9986afe7fdbdcd99eaa67e">Also test against fast release channel for buildkite + ClickHouse clo…</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/.cargo">.cargo</a></p></td><td colspan="1"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/.cargo">.cargo</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/.config">.config</a></p></td><td colspan="1"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/.config">.config</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/.github">.github</a></p></td><td colspan="1"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/.github">.github</a></p></td><td><p><a href="https://github.com/tensorzero/tensorzero/commit/73e9c40dfcb165d91b7c8456dffcb0f049bdd8fa">Post a message to Slack when daily optimization run fails (</a><a href="https://github.com/tensorzero/tensorzero/pull/3418">#3418</a><a href="https://github.com/tensorzero/tensorzero/commit/73e9c40dfcb165d91b7c8456dffcb0f049bdd8fa">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/ci">ci</a></p></td><td colspan="1"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/ci">ci</a></p></td><td><p><a href="https://github.com/tensorzero/tensorzero/commit/4050ee4bac9b561793b230a21138434a14c2d3a2">rationalized image tags in docker CI (</a><a href="https://github.com/tensorzero/tensorzero/pull/3428">#3428</a><a href="https://github.com/tensorzero/tensorzero/commit/4050ee4bac9b561793b230a21138434a14c2d3a2">)</a></p></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/clients">clients</a></p></td><td colspan="1"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/clients">clients</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/docs">docs</a></p></td><td colspan="1"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/docs">docs</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/evaluations">evaluations</a></p></td><td colspan="1"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/evaluations">evaluations</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/examples">examples</a></p></td><td colspan="1"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/examples">examples</a></p></td><td></td><td></td></tr><tr><td colspan="2"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/gateway">gateway</a></p></td><td colspan="1"><p><a href="https://github.com/tensorzero/tensorzero/tree/main/gateway">gateway</a></p></td><td></td><td></td></tr><tr><td colspan="3"></td></tr></tbody></table>

![TensorZero Logo](https://github.com/user-attachments/assets/47d67430-386d-4675-82ad-d4734d3262d9)

## TensorZero

![#1 Repository Of The Day](https://camo.githubusercontent.com/a90ca4d26c37435072ab77d4bdee91e8db5c392c828d34bfad526111679a8bf4/68747470733a2f2f7777772e74656e736f727a65726f2e636f6d2f6769746875622d7472656e64696e672d62616467652e737667)

**TensorZero is an open-source stack for *industrial-grade LLM applications*:**

- **Gateway:** access every LLM provider through a unified API, built for performance (<1ms p99 latency)
- **Observability:** store inferences and feedback in your database, available programmatically or in the UI
- **Optimization:** collect metrics and human feedback to optimize prompts, models, and inference strategies
- **Evaluation:** benchmark individual inferences or end-to-end workflows using heuristics, LLM judges, etc.
- **Experimentation:** ship with confidence with built-in A/B testing, routing, fallbacks, retries, etc.

Take what you need, adopt incrementally, and complement with other tools.

tensorzero-demo.mp4<video src="https://github.com/user-attachments/assets/04a8466e-27d8-4189-b305-e7cecb6881ee" controls="controls"></video>

---

**[Website](https://www.tensorzero.com/)** · **[Docs](https://www.tensorzero.com/docs)** · **[Twitter](https://www.x.com/tensorzero)** · **[Slack](https://www.tensorzero.com/slack)** · **[Discord](https://www.tensorzero.com/discord)**  
  
**[Quick Start (5min)](https://www.tensorzero.com/docs/quickstart)** · **[Deployment Guide](https://www.tensorzero.com/docs/gateway/deployment)** · **[API Reference](https://www.tensorzero.com/docs/gateway/api-reference)** · **[Configuration Reference](https://www.tensorzero.com/docs/gateway/deployment)**

---

| **What is TensorZero?** | TensorZero is an open-source stack for industrial-grade LLM applications. It unifies an LLM gateway, observability, optimization, evaluation, and experimentation. |
| --- | --- |
| **How is TensorZero different from other LLM frameworks?** | 1\. TensorZero enables you to optimize complex LLM applications based on production metrics and human feedback.   2\. TensorZero supports the needs of industrial-grade LLM applications: low latency, high throughput, type safety, self-hosted, GitOps, customizability, etc.   3\. TensorZero unifies the entire LLMOps stack, creating compounding benefits. For example, LLM evaluations can be used for fine-tuning models alongside AI judges. |
| **Can I use TensorZero with \_\_\_?** | Yes. Every major programming language is supported. You can use TensorZero with our Python client, any OpenAI SDK or OpenAI-compatible client, or our HTTP API. |
| **Is TensorZero production-ready?** | Yes. Here's a case study: **[Automating Code Changelogs at a Large Bank with LLMs](https://www.tensorzero.com/blog/case-study-automating-code-changelogs-at-a-large-bank-with-llms)** |
| **How much does TensorZero cost?** | Nothing. TensorZero is 100% self-hosted and open-source. There are no paid features. |
| **Who is building TensorZero?** | Our technical team includes a former Rust compiler maintainer, machine learning researchers (Stanford, CMU, Oxford, Columbia) with thousands of citations, and the chief product officer of a decacorn startup. We're backed by the same investors as leading open-source projects (e.g. ClickHouse, CockroachDB) and AI labs (e.g. OpenAI, Anthropic). See our **[$7.3M seed round announcement](https://www.tensorzero.com/blog/tensorzero-raises-7-3m-seed-round-to-build-an-open-source-stack-for-industrial-grade-llm-applications/)** and **[coverage from VentureBeat](https://venturebeat.com/ai/tensorzero-nabs-7-3m-seed-to-solve-the-messy-world-of-enterprise-llm-development/)**. We're **[hiring in NYC](https://www.tensorzero.com/jobs)**. |
| **How do I get started?** | You can adopt TensorZero incrementally. Our **[Quick Start](https://www.tensorzero.com/docs/quickstart)** goes from a vanilla OpenAI wrapper to a production-ready LLM application with observability and fine-tuning in just 5 minutes. |

---

## Features

> **Integrate with TensorZero once and access every major LLM provider.**

- Access every major LLM provider (API or self-hosted) through a single unified API
- Infer with streaming, tool use, structured generation (JSON mode), batch, embeddings, multimodal (VLMs), file inputs, caching, etc.
- Define prompt templates and schemas to enforce a consistent, typed interface between your application and the LLMs
- Satisfy extreme throughput and latency needs, thanks to 🦀 Rust: <1ms p99 latency overhead at 10k+ QPS
- Integrate using our Python client, any OpenAI SDK or OpenAI-compatible client, or our HTTP API (use any programming language)
- Ensure high availability with routing, retries, fallbacks, load balancing, granular timeouts, etc.
- Soon: rate limits, spend tracking and budgeting, service accounts

| **Model Providers** | **Features** |
| --- | --- |
| The TensorZero Gateway natively supports:  - **[Anthropic](https://www.tensorzero.com/docs/gateway/guides/providers/anthropic)** - **[AWS Bedrock](https://www.tensorzero.com/docs/gateway/guides/providers/aws-bedrock)** - **[AWS SageMaker](https://www.tensorzero.com/docs/gateway/guides/providers/aws-sagemaker)** - **[Azure OpenAI Service](https://www.tensorzero.com/docs/gateway/guides/providers/azure)** - **[DeepSeek](https://www.tensorzero.com/docs/gateway/guides/providers/deepseek)** - **[Fireworks](https://www.tensorzero.com/docs/gateway/guides/providers/fireworks)** - **[GCP Vertex AI Anthropic](https://www.tensorzero.com/docs/gateway/guides/providers/gcp-vertex-ai-anthropic)** - **[GCP Vertex AI Gemini](https://www.tensorzero.com/docs/gateway/guides/providers/gcp-vertex-ai-gemini)** - **[Google AI Studio (Gemini API)](https://www.tensorzero.com/docs/gateway/guides/providers/google-ai-studio-gemini)** - **[Groq](https://www.tensorzero.com/docs/gateway/guides/providers/groq)** - **[Hyperbolic](https://www.tensorzero.com/docs/gateway/guides/providers/hyperbolic)** - **[Mistral](https://www.tensorzero.com/docs/gateway/guides/providers/mistral)** - **[OpenAI](https://www.tensorzero.com/docs/gateway/guides/providers/openai)** - **[OpenRouter](https://www.tensorzero.com/docs/gateway/guides/providers/openrouter)** - **[SGLang](https://www.tensorzero.com/docs/gateway/guides/providers/sglang)** - **[TGI](https://www.tensorzero.com/docs/gateway/guides/providers/tgi)** - **[Together AI](https://www.tensorzero.com/docs/gateway/guides/providers/together)** - **[vLLM](https://www.tensorzero.com/docs/gateway/guides/providers/vllm)** - **[xAI (Grok)](https://www.tensorzero.com/docs/gateway/guides/providers/xai)**  *Need something else? Your provider is most likely supported because TensorZero integrates with **[any OpenAI-compatible API (e.g. Ollama)](https://www.tensorzero.com/docs/gateway/guides/providers/openai-compatible)**.* | The TensorZero Gateway supports advanced features like:  - **[Retries & Fallbacks](https://www.tensorzero.com/docs/gateway/guides/retries-fallbacks)** - **[Inference-Time Optimizations](https://www.tensorzero.com/docs/gateway/guides/inference-time-optimizations)** - **[Prompt Templates & Schemas](https://www.tensorzero.com/docs/gateway/guides/prompt-templates-schemas)** - **[Experimentation (A/B Testing)](https://www.tensorzero.com/docs/gateway/guides/experimentation/)** - **[Configuration-as-Code (GitOps)](https://www.tensorzero.com/docs/gateway/configuration-reference)** - **[Batch Inference](https://www.tensorzero.com/docs/gateway/guides/batch-inference)** - **[Multimodal Inference (VLMs)](https://www.tensorzero.com/docs/gateway/guides/multimodal-inference)** - **[Inference Caching](https://www.tensorzero.com/docs/gateway/guides/inference-caching)** - **[Metrics & Feedback](https://www.tensorzero.com/docs/gateway/guides/metrics-feedback)** - **[Multi-Step LLM Workflows (Episodes)](https://www.tensorzero.com/docs/gateway/guides/episodes)** - *& a lot more...*  The TensorZero Gateway is written in Rust 🦀 with **performance** in mind (<1ms p99 latency overhead @ 10k QPS). See **[Benchmarks](https://www.tensorzero.com/docs/gateway/benchmarks)**.    You can run inference using the **TensorZero client** (recommended), the **OpenAI client**, or the **HTTP API**. |

  
**Usage: Python — TensorZero Client (Recommended)**

You can access any provider using the TensorZero Python client.

1. `pip install tensorzero`
2. Optional: Set up the TensorZero configuration.
3. Run inference:
```
from tensorzero import TensorZeroGateway  # or AsyncTensorZeroGateway

with TensorZeroGateway.build_embedded(clickhouse_url="...", config_file="...") as client:
    response = client.inference(
        model_name="openai::gpt-4o-mini",
        # Try other providers easily: "anthropic::claude-3-7-sonnet-20250219"
        input={
            "messages": [
                {
                    "role": "user",
                    "content": "Write a haiku about artificial intelligence.",
                }
            ]
        },
    )
```

See **[Quick Start](https://www.tensorzero.com/docs/quickstart)** for more information.

**Usage: Python — OpenAI Client**

You can access any provider using the OpenAI Python client with TensorZero.

1. `pip install tensorzero`
2. Optional: Set up the TensorZero configuration.
3. Run inference:
```
from openai import OpenAI  # or AsyncOpenAI
from tensorzero import patch_openai_client

client = OpenAI()

patch_openai_client(
    client,
    clickhouse_url="***************************************/tensorzero",
    config_file="config/tensorzero.toml",
    async_setup=False,
)

response = client.chat.completions.create(
    model="tensorzero::model_name::openai::gpt-4o-mini",
    # Try other providers easily: "tensorzero::model_name::anthropic::claude-3-7-sonnet-20250219"
    messages=[
        {
            "role": "user",
            "content": "Write a haiku about artificial intelligence.",
        }
    ],
)
```

See **[Quick Start](https://www.tensorzero.com/docs/quickstart)** for more information.

**Usage: JavaScript / TypeScript (Node) — OpenAI Client**

You can access any provider using the OpenAI Node client with TensorZero.

1. Deploy `tensorzero/gateway` using Docker.**[Detailed instructions →](https://www.tensorzero.com/docs/gateway/deployment)**
2. Set up the TensorZero configuration.
3. Run inference:
```
import OpenAI from "openai";

const client = new OpenAI({
  baseURL: "http://localhost:3000/openai/v1",
});

const response = await client.chat.completions.create({
  model: "tensorzero::model_name::openai::gpt-4o-mini",
  // Try other providers easily: "tensorzero::model_name::anthropic::claude-3-7-sonnet-20250219"
  messages: [
    {
      role: "user",
      content: "Write a haiku about artificial intelligence.",
    },
  ],
});
```

See **[Quick Start](https://www.tensorzero.com/docs/quickstart)** for more information.

**Usage: Other Languages & Platforms — HTTP API**

TensorZero supports virtually any programming language or platform via its HTTP API.

1. Deploy `tensorzero/gateway` using Docker.**[Detailed instructions →](https://www.tensorzero.com/docs/gateway/deployment)**
2. Optional: Set up the TensorZero configuration.
3. Run inference:
```
curl -X POST "http://localhost:3000/inference" \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "openai::gpt-4o-mini",
    "input": {
      "messages": [
        {
          "role": "user",
          "content": "Write a haiku about artificial intelligence."
        }
      ]
    }
  }'
```

See **[Quick Start](https://www.tensorzero.com/docs/quickstart)** for more information.

  

> **Zoom in to debug individual API calls, or zoom out to monitor metrics across models and prompts over time — all using the open-source TensorZero UI.**

- Store inferences and feedback (metrics, human edits, etc.) in your own database
- Dive into individual inferences or high-level aggregate patterns using the TensorZero UI or programmatically
- Build datasets for optimization, evaluation, and other workflows
- Replay historical inferences with new prompts, models, inference strategies, etc.
- Export OpenTelemetry (OTLP) traces to your favorite general-purpose observability tool
- Soon: AI-assisted debugging and root cause analysis; AI-assisted data labeling

| **Observability » UI** | **Observability » Programmatic** |
| --- | --- |
| tensorzero-ui-observability.mp4<video src="https://private-user-images.githubusercontent.com/1275491/*********-a23e4c95-18fa-482c-8423-6078fb4cf285.mp4?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQ3NTY1MjIzOS1hMjNlNGM5NS0xOGZhLTQ4MmMtODQyMy02MDc4ZmI0Y2YyODUubXA0P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9YjYyMDIxNDIzM2FkZTJiNWE2MzI3NWRhYWMxZWI5YzQwNjFhYWZkODBmYzhkNDc3M2Q1MGMxOGRlOWE0YjRlYiZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.P1U9EhuTBvyekDZELqKisVSI9BJeUlAyXqDdEAEOE2k" controls="controls"></video> | ``` t0.experimental_list_inferences(   function_name="sales_agent",   variant_name="qwen3-promptv2",   filters=BooleanMetricFilter(       metric_name="converted_sale",       value=True,   ),   order_by=[OrderBy(by="timestamp", direction="DESC")],   limit=100_000,   # ... and more ... ) ``` |

  

> **Send production metrics and human feedback to easily optimize your prompts, models, and inference strategies — using the UI or programmatically.**

- Optimize your models with supervised fine-tuning, RLHF, and other techniques
- Optimize your prompts with automated prompt engineering algorithms like MIPROv2
- Optimize your inference strategy with dynamic in-context learning, chain of thought, best/mixture-of-N sampling, etc.
- Enable a feedback loop for your LLMs: a data & learning flywheel turning production data into smarter, faster, and cheaper models
- Soon: synthetic data generation

#### Model Optimization

Optimize closed-source and open-source models using supervised fine-tuning (SFT) and preference fine-tuning (DPO).

| **Supervised Fine-tuning — UI** | **Preference Fine-tuning (DPO) — Jupyter Notebook** |
| --- | --- |
| tensorzero-ui-supervised-fine-tuning.mp4<video src="https://private-user-images.githubusercontent.com/1275491/*********-82f76be7-5e02-4ada-b503-69dfa209a442.mp4?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQ3NTY2NjgwMC04MmY3NmJlNy01ZTAyLTRhZGEtYjUwMy02OWRmYTIwOWE0NDIubXA0P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9ZDBjMTg4OGYwNDg0Zjc0MTcxNzhkMzM4YzY4ZmI3NzNhMjg4MzhjY2Q4Mzc1YmVmODQwY2ZmMDhlMDUxYTFkYSZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.GzPAuwkkLfi8nrlK3erfjU6UR841Y9_Asdf47oypBus" controls="controls"></video> | [![](https://private-user-images.githubusercontent.com/1275491/*********-a67a0634-04a7-42b0-b934-9130cb7cdf51.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQwODI1NjA4Mi1hNjdhMDYzNC0wNGE3LTQyYjAtYjkzNC05MTMwY2I3Y2RmNTEucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9ODkyZTJmZWQyMDFmOWFkZmFlYzM2ZmJiOWEyZmNiNGUwNjhjOGQ0ODEzZDVkYzU5MjVlNDY5ZDE0YzdmOTc2OCZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.NKv8gUqEJjIqaIx4K42RHdC4AB69L-Q-PjsiHUsY-Xw)](https://private-user-images.githubusercontent.com/1275491/*********-a67a0634-04a7-42b0-b934-9130cb7cdf51.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQwODI1NjA4Mi1hNjdhMDYzNC0wNGE3LTQyYjAtYjkzNC05MTMwY2I3Y2RmNTEucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9ODkyZTJmZWQyMDFmOWFkZmFlYzM2ZmJiOWEyZmNiNGUwNjhjOGQ0ODEzZDVkYzU5MjVlNDY5ZDE0YzdmOTc2OCZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.NKv8gUqEJjIqaIx4K42RHdC4AB69L-Q-PjsiHUsY-Xw) |

#### Inference-Time Optimization

Boost performance by dynamically updating your prompts with relevant examples, combining responses from multiple inferences, and more.

| **[Best-of-N Sampling](https://www.tensorzero.com/docs/gateway/guides/inference-time-optimizations#best-of-n-sampling)** | **[Mixture-of-N Sampling](https://www.tensorzero.com/docs/gateway/guides/inference-time-optimizations#mixture-of-n-sampling)** |
| --- | --- |
| [![](https://private-user-images.githubusercontent.com/1275491/*********-c0edfa4c-713c-4996-9964-50c0d26e6970.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQwODI1NjE1OC1jMGVkZmE0Yy03MTNjLTQ5OTYtOTk2NC01MGMwZDI2ZTY5NzAucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9Mzg2ZTIyNDBmM2ZlOWY1MjkxOTM2Zjk2MWIxOTdkMDg4OGM1ZWYwOTE3M2ZjMmNhMGIwNzg4MjcwMzBlNjcyZiZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.XuAQq83GdjbIwR3UXH0pXO9mdFIGMpG0RchJvY-hj3o)](https://private-user-images.githubusercontent.com/1275491/*********-c0edfa4c-713c-4996-9964-50c0d26e6970.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQwODI1NjE1OC1jMGVkZmE0Yy03MTNjLTQ5OTYtOTk2NC01MGMwZDI2ZTY5NzAucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9Mzg2ZTIyNDBmM2ZlOWY1MjkxOTM2Zjk2MWIxOTdkMDg4OGM1ZWYwOTE3M2ZjMmNhMGIwNzg4MjcwMzBlNjcyZiZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.XuAQq83GdjbIwR3UXH0pXO9mdFIGMpG0RchJvY-hj3o) | [![](https://private-user-images.githubusercontent.com/1275491/*********-75b5bf05-4c1f-43c4-b158-d69d1b8d05be.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQwODI1NjI1Ni03NWI1YmYwNS00YzFmLTQzYzQtYjE1OC1kNjlkMWI4ZDA1YmUucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9YTZjOTM1NjJmNDc0NzVlNTIzZmRmMGEyZWZjNDJjN2Y4OGQwZWI0NWU2ZjZjYTE1YjZhZmZlOTcyNmU4ZDU3OSZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.j0IHOB9qWjcdhff4dnwmcmFnQpg-YjJ1KStPIWwjChg)](https://private-user-images.githubusercontent.com/1275491/*********-75b5bf05-4c1f-43c4-b158-d69d1b8d05be.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQwODI1NjI1Ni03NWI1YmYwNS00YzFmLTQzYzQtYjE1OC1kNjlkMWI4ZDA1YmUucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9YTZjOTM1NjJmNDc0NzVlNTIzZmRmMGEyZWZjNDJjN2Y4OGQwZWI0NWU2ZjZjYTE1YjZhZmZlOTcyNmU4ZDU3OSZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.j0IHOB9qWjcdhff4dnwmcmFnQpg-YjJ1KStPIWwjChg) |
| **[Dynamic In-Context Learning (DICL)](https://www.tensorzero.com/docs/gateway/guides/inference-time-optimizations#dynamic-in-context-learning-dicl)** | **[Chain-of-Thought (CoT)](https://www.tensorzero.com/docs/gateway/guides/inference-time-optimizations#chain-of-thought-cot)** |
| [![](https://private-user-images.githubusercontent.com/1275491/*********-d8489e92-ce93-46ac-9aab-289ce19bb67d.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQwODI1NjMyNi1kODQ4OWU5Mi1jZTkzLTQ2YWMtOWFhYi0yODljZTE5YmI2N2QucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9N2EzYTU2YjEwY2ExMzc3MDBhOGU0OGZiY2RmNGI3OWYzZGFkZWYxNzUyZDZjYmQyNGU4MGE3ZjYwZmVhNjdhYyZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.lljqBQDMHxoRZEEVKEVpYsClOgXScCYu__P-JWpxtOE)](https://private-user-images.githubusercontent.com/1275491/*********-d8489e92-ce93-46ac-9aab-289ce19bb67d.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQwODI1NjMyNi1kODQ4OWU5Mi1jZTkzLTQ2YWMtOWFhYi0yODljZTE5YmI2N2QucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9N2EzYTU2YjEwY2ExMzc3MDBhOGU0OGZiY2RmNGI3OWYzZGFkZWYxNzUyZDZjYmQyNGU4MGE3ZjYwZmVhNjdhYyZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.lljqBQDMHxoRZEEVKEVpYsClOgXScCYu__P-JWpxtOE) | [![](https://private-user-images.githubusercontent.com/1275491/*********-ea13d73c-76a4-4e0c-a35b-0c648f898311.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQ0MDk0MDg3NC1lYTEzZDczYy03NmE0LTRlMGMtYTM1Yi0wYzY0OGY4OTgzMTEucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9MzI4YzljZTQ0ZjdkZWU0ODU2N2I0MGJlZjZjNmZiNWNkN2NlZDJhOTU1M2ZhZjhjOTRhMGU4ZTU0NDhiNTg4MyZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.2CmKEgG2Fd6Pacw0F1fwZGo8O9YtWe1pzFO3r_g4fmI)](https://private-user-images.githubusercontent.com/1275491/*********-ea13d73c-76a4-4e0c-a35b-0c648f898311.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQ0MDk0MDg3NC1lYTEzZDczYy03NmE0LTRlMGMtYTM1Yi0wYzY0OGY4OTgzMTEucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9MzI4YzljZTQ0ZjdkZWU0ODU2N2I0MGJlZjZjNmZiNWNkN2NlZDJhOTU1M2ZhZjhjOTRhMGU4ZTU0NDhiNTg4MyZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.2CmKEgG2Fd6Pacw0F1fwZGo8O9YtWe1pzFO3r_g4fmI) |

*More coming soon...*

  

#### Prompt Optimization

Optimize your prompts programmatically using research-driven optimization techniques.

| **[MIPROv2](https://www.tensorzero.com/docs/gateway/guides/inference-time-optimizations#best-of-n-sampling)** | **[DSPy Integration](https://github.com/tensorzero/tensorzero/tree/main/examples/gsm8k-custom-recipe-dspy)** |
| --- | --- |
| [![MIPROv2 diagram](https://private-user-images.githubusercontent.com/1275491/*********-d81a7c37-382f-4c46-840f-e6c2593301db.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQzMTQ1NjEzMC1kODFhN2MzNy0zODJmLTRjNDYtODQwZi1lNmMyNTkzMzAxZGIucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9MmMyNTNjYzRjNWVmMDJjYzRmYzRhNWJmNGRkNTE3YzExOTU4ZTRiNzkzZWViZDMzZjU1NmQzN2Q0YTE4YmMyZSZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.a2SkK6wMHUa2neGE6m4NBvu8--huDT6E7MowJgXTYoM)](https://private-user-images.githubusercontent.com/1275491/*********-d81a7c37-382f-4c46-840f-e6c2593301db.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTcyNDYxMzMsIm5iZiI6MTc1NzI0NTgzMywicGF0aCI6Ii8xMjc1NDkxLzQzMTQ1NjEzMC1kODFhN2MzNy0zODJmLTRjNDYtODQwZi1lNmMyNTkzMzAxZGIucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI1MDkwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA5MDdUMTE1MDMzWiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9MmMyNTNjYzRjNWVmMDJjYzRmYzRhNWJmNGRkNTE3YzExOTU4ZTRiNzkzZWViZDMzZjU1NmQzN2Q0YTE4YmMyZSZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QifQ.a2SkK6wMHUa2neGE6m4NBvu8--huDT6E7MowJgXTYoM) | TensorZero comes with several optimization recipes, but you can also easily create your own. This example shows how to optimize a TensorZero function using an arbitrary tool — here, DSPy, a popular library for automated prompt engineering. |

*More coming soon...*

  

> **Compare prompts, models, and inference strategies using evaluations powered by heuristics and LLM judges.**

- Evaluate individual inferences with *static evaluations* powered by heuristics or LLM judges (≈ unit tests for LLMs)
- Evaluate end-to-end workflows with *dynamic evaluations* with complete flexibility (≈ integration tests for LLMs)
- Optimize LLM judges just like any other TensorZero function to align them to human preferences
- Soon: more built-in evaluators; headless evaluations

| **Evaluation » UI** | **Evaluation » CLI** |
| --- | --- |
| [![](https://private-user-images.githubusercontent.com/1275491/*********-f4bf54e3-1b63-46c8-be12-2eaabf615699.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FA2_VmCkQDnwKv3KPe5uD-KJoN8cKNh1nG8jUGj1MrU)](https://private-user-images.githubusercontent.com/1275491/*********-f4bf54e3-1b63-46c8-be12-2eaabf615699.png?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FA2_VmCkQDnwKv3KPe5uD-KJoN8cKNh1nG8jUGj1MrU) | ``` docker compose run --rm evaluations \   --evaluation-name extract_data \   --dataset-name hard_test_cases \   --variant-name gpt_4o \   --concurrency 5 ```  ``` Run ID: 01961de9-c8a4-7c60-ab8d-15491a9708e4 Number of datapoints: 100 ██████████████████████████████████████ 100/100 exact_match: 0.83 ± 0.03 semantic_match: 0.98 ± 0.01 item_count: 7.15 ± 0.39 ``` |

> **Ship with confidence with built-in A/B testing, routing, fallbacks, retries, etc.**

- Ship with confidence with built-in A/B testing for models, prompts, providers, hyperparameters, etc.
- Enforce principled experiments (RCTs) in complex workflows, including multi-turn and compound LLM systems
- Soon: multi-armed bandits; AI-managed experiments

### & more!

> **Build with an open-source stack well-suited for prototypes but designed from the ground up to support the most complex LLM applications and deployments.**

- Build simple applications or massive deployments with GitOps-friendly orchestration
- Extend TensorZero with built-in escape hatches, programmatic-first usage, direct database access, and more
- Integrate with third-party tools: specialized observability and evaluations, model providers, agent orchestration frameworks, etc.
- Iterate quickly by experimenting with prompts interactively using the Playground UI

## Demo

> **Watch LLMs get better at data extraction in real-time with TensorZero!**
> 
> **[Dynamic in-context learning (DICL)](https://www.tensorzero.com/docs/gateway/guides/inference-time-optimizations#dynamic-in-context-learning-dicl)** is a powerful inference-time optimization available out of the box with TensorZero. It enhances LLM performance by automatically incorporating relevant historical examples into the prompt, without the need for model fine-tuning.

LLMs-get-better-at-data-extraction-in-real-time-with-TensorZero.mp4<video src="https://private-user-images.githubusercontent.com/1275491/*********-4df1022e-886e-48c2-8f79-6af3cdad79cb.mp4?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NqnKh-yR6j1Cjt0egCRyVmdkE3JBJt8FbJGqaXl15yc" controls="controls"></video>

## Get Started

**Start building today.**The **[Quick Start](https://www.tensorzero.com/docs/quickstart)** shows it's easy to set up an LLM application with TensorZero.

**Questions?**Ask us on **[Slack](https://www.tensorzero.com/slack)** or **[Discord](https://www.tensorzero.com/discord)**.

**Using TensorZero at work?**Email us at **[<EMAIL>](https://github.com/tensorzero/)** to set up a Slack or Teams channel with your team (free).

## Examples

We are working on a series of **complete runnable examples** illustrating TensorZero's data & learning flywheel.

> **[Optimizing Data Extraction (NER) with TensorZero](https://github.com/tensorzero/tensorzero/tree/main/examples/data-extraction-ner)**
> 
> This example shows how to use TensorZero to optimize a data extraction pipeline. We demonstrate techniques like fine-tuning and dynamic in-context learning (DICL). In the end, an optimized GPT-4o Mini model outperforms GPT-4o on this task — at a fraction of the cost and latency — using a small amount of training data.

> **[Agentic RAG — Multi-Hop Question Answering with LLMs](https://github.com/tensorzero/tensorzero/tree/main/examples/rag-retrieval-augmented-generation/simple-agentic-rag/)**
> 
> This example shows how to build a multi-hop retrieval agent using TensorZero. The agent iteratively searches Wikipedia to gather information, and decides when it has enough context to answer a complex question.

> **[Writing Haikus to Satisfy a Judge with Hidden Preferences](https://github.com/tensorzero/tensorzero/tree/main/examples/haiku-hidden-preferences)**
> 
> This example fine-tunes GPT-4o Mini to generate haikus tailored to a specific taste. You'll see TensorZero's "data flywheel in a box" in action: better variants leads to better data, and better data leads to better variants. You'll see progress by fine-tuning the LLM multiple times.

> **[Image Data Extraction — Multimodal (Vision) Fine-tuning](https://github.com/tensorzero/tensorzero/tree/main/examples/multimodal-vision-finetuning)**
> 
> This example shows how to fine-tune multimodal models (VLMs) like GPT-4o to improve their performance on vision-language tasks. Specifically, we'll build a system that categorizes document images (screenshots of computer science research papers).

> **[Improving LLM Chess Ability with Best-of-N Sampling](https://github.com/tensorzero/tensorzero/tree/main/examples/chess-puzzles/)**
> 
> This example showcases how best-of-N sampling can significantly enhance an LLM's chess-playing abilities by selecting the most promising moves from multiple generated options.

> **[Improving Math Reasoning with a Custom Recipe for Automated Prompt Engineering (DSPy)](https://github.com/tensorzero/tensorzero/tree/main/examples/gsm8k-custom-recipe-dspy)**
> 
> TensorZero provides a number of pre-built optimization recipes covering common LLM engineering workflows. But you can also easily create your own recipes and workflows! This example shows how to optimize a TensorZero function using an arbitrary tool — here, DSPy.

*& many more on the way!*

## Releases 73

[\+ 72 releases](https://github.com/tensorzero/tensorzero/releases)