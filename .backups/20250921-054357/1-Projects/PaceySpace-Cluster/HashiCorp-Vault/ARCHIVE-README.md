# HashiCorp Vault Setup for Website Cluster

This project sets up a HashiCorp Vault instance in a Docker container to manage secrets for a website cluster serving multiple small business clients. The Vault is isolated using Docker for security and is designed to handle S3 authentication, API keys, and other secrets.

## Project Structure

- `docker-compose.yml`: Defines the Docker service for running Vault.
- `config/vault-config.hcl`: Configuration file for Vault, specifying storage and listener settings.

## Prerequisites

- **Docker**: Ensure Docker and Docker Compose are installed on your Linux VPS.
- **Access**: Ensure you have the necessary permissions to run Docker containers and manage ports (e.g., 8200 for Vault) on the VPS.
- **SSH Access**: Ensure you have SSH access to your VPS with the necessary credentials (IP address, username, and password or key file).

## Setup Instructions

### 1. Access Your VPS via SSH
   To deploy Vault on your VPS, you first need to connect to it remotely from your dev laptop. Follow these steps:
   - Open a terminal on your laptop.
   - Use the following command to SSH into your VPS, replacing `<username>` with your VPS username and `<vps-ip-address>` with your VPS IP address:
     ```
     ssh <username>@<vps-ip-address>
     ```
   - If you use an SSH key for authentication, specify the key file with the `-i` option:
     ```
     ssh -i /path/to/your/keyfile.pem <username>@<vps-ip-address>
     ```
   - Enter your password if prompted, or ensure your key is correctly configured for passwordless access.
   - Once connected, you will be in the VPS terminal environment.

### 2. Transfer Project Files to VPS
   After SSH-ing into your VPS, you need to transfer the Vault setup files from your laptop to the VPS. You can use `scp` for this:
   - On your local laptop (in a new terminal window or after exiting SSH), navigate to the directory containing this project:
     ```
     cd /path/to/HashiCorp\ Vault
     ```
   - Use `scp` to copy the project directory to your VPS, replacing `<username>` and `<vps-ip-address>` as before, and specifying a target directory on the VPS (e.g., `/home/<USER>/vault-setup`):
     ```
     scp -r . <username>@<vps-ip-address>:/home/<USER>/vault-setup
     ```
   - If using an SSH key, add the `-i /path/to/your/keyfile.pem` option to the `scp` command.
   - Once transferred, SSH back into your VPS and navigate to the target directory:
     ```
     cd /home/<USER>/vault-setup
     ```

### 3. Install Docker and Docker Compose (if not already installed)
   If Docker and Docker Compose are not installed on your VPS, install them:
   - Update the package index:
     ```
     sudo apt update
     ```
   - Install Docker:
     ```
     sudo apt install -y docker.io
     sudo systemctl start docker
     sudo systemctl enable docker
     ```
   - Install Docker Compose:
     ```
     sudo curl -L "https://github.com/docker/compose/releases/download/v2.38.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
     sudo chmod +x /usr/local/bin/docker-compose
     ```
   - Verify installations:
     ```
     docker --version
     docker-compose --version
     ```
   - Add your user to the Docker group to run Docker without sudo (optional, requires logout/login):
     ```
     sudo usermod -aG docker $USER
     ```

### 4. Start the Vault Container
   Run the following command in the project directory on your VPS to start the Vault server in a Docker container:
   ```
   docker-compose up -d
   ```

### 5. Initialize Vault
   After the container is running, initialize Vault to set up the master keys and unseal tokens. Use the following command:
   ```
   docker exec -it vault-server vault operator init
   ```
   This will output the unseal keys and initial root token. **Store these securely** as they are required to unseal Vault and access it initially.

### 6. Unseal Vault
   Use the unseal keys from the initialization step to unseal Vault. Run the following command for each key (typically 3 out of 5 keys are needed):
   ```
   docker exec -it vault-server vault operator unseal
   ```
   Enter one unseal key at a time when prompted.

### 7. Access Vault UI
   Once unsealed, you can access the Vault UI by navigating to `http://<vps-ip-address>:8200` in your browser from your laptop. Ensure port 8200 is open on your VPS firewall or security group settings. Log in with the initial root token from the initialization step.
   - If port 8200 is not accessible directly, consider setting up an SSH tunnel from your laptop:
     ```
     ssh -L 8200:localhost:8200 <username>@<vps-ip-address>
     ```
     Then access `http://localhost:8200` in your browser.

### 8. Configure Secrets and Policies
   - Enable secrets engines (e.g., KV for API keys, AWS for S3 credentials) via the UI or CLI.
   - Define policies for each client to restrict access to their specific secrets.
   - Set up authentication methods (e.g., Token or AppRole) for your website backends to access Vault.

### 9. Integrate with Website Cluster
   - For .NET backends, use a library like VaultSharp to fetch secrets.
   - For WordPress backends, create a custom script or plugin to interact with Vault.
   - Ensure JavaScript frontends only access secrets indirectly through secure backend API endpoints.

## Security Notes

- **TLS Configuration**: The current setup has TLS disabled for simplicity. For production, enable TLS by updating the `vault-config.hcl` file with certificate paths and restarting the container.
- **Key Management**: Keep unseal keys and root tokens secure and do not store them in version control.
- **Network Isolation**: The Docker network is set up to isolate Vault; ensure only necessary services can access port 8200.

## Next Steps

- Configure specific secrets engines and policies based on client needs.
- Set up backup mechanisms for Vault data.
- Implement monitoring and logging for Vault access and health.

For further assistance or to proceed with detailed configuration, refer to the HashiCorp Vault documentation or reach out for support.
