---
title: "Docker Quick Reference - YendorCats"
tags: [docker, quick-reference, cheatsheet, ports, endpoints, commands]
aliases: [docker-cheat, docker-ref, quick-docker]
created: 2025-07-27
updated: 2025-07-27
type: reference
status: complete
---

# Docker Quick Reference

> **Navigation**: [[README]] | [[Docker Deployment Guide]] | [[Docker Troubleshooting]]

---

## 🚀 Quick Start Commands

```bash
# Deploy staging environment
./deploy-staging.sh

# Manual deployment
docker-compose -f docker-compose.staging.yml up -d

# Stop all services
docker-compose -f docker-compose.staging.yml down

# View logs
docker-compose -f docker-compose.staging.yml logs -f
```

---

## 🌐 Service Endpoints & Ports

### Frontend Service (nginx)
- **Container**: `yendorcats-frontend-staging`
- **External Ports**: `80`, `443`
- **Internal Port**: `80`
- **Health Check**: `http://localhost/health`
- **Main Site**: `http://localhost/`

### API Service (.NET Core)
- **Container**: `yendorcats-api-staging`
- **External Port**: `5003`
- **Internal Port**: `80`
- **Health Check**: `http://localhost:5003/health`
- **API Base**: `http://localhost:5003/api/`

### Uploader Service (Node.js)
- **Container**: `yendorcats-uploader-staging`
- **External Port**: `5002`
- **Internal Port**: `80`
- **Health Check**: `http://localhost:5002/health`
- **Upload Endpoint**: `http://localhost:5002/upload`

---

## 🔗 API Endpoints

### Through Frontend Proxy
```bash
# API calls through frontend
curl http://localhost/api/cats
curl http://localhost/api/auth/login
curl http://localhost/api/gallery

# File upload through frontend
curl http://localhost/uploader/upload
curl http://localhost/uploader/categories
```

### Direct API Access
```bash
# Direct API calls
curl http://localhost:5003/api/cats
curl http://localhost:5003/api/auth/login
curl http://localhost:5003/health

# Direct uploader calls
curl http://localhost:5002/health
curl http://localhost:5002/categories
```

---

## 🏥 Health Checks

### Quick Health Verification
```bash
# All services health check
curl http://localhost/health          # Frontend: "healthy"
curl http://localhost:5003/health     # API: JSON response
curl http://localhost:5002/health     # Uploader: JSON response

# Proxy health checks
curl http://localhost/api/cats        # API through proxy: []
curl http://localhost/uploader/health # Uploader through proxy
```

### Container Health Status
```bash
# Check container health
docker ps --format "table {{.Names}}\t{{.Status}}"

# Detailed health info
docker inspect yendorcats-frontend-staging | grep -A 5 Health
docker inspect yendorcats-api-staging | grep -A 5 Health
docker inspect yendorcats-uploader-staging | grep -A 5 Health
```

---

## 🐳 Essential Docker Commands

### Container Management
```bash
# List running containers
docker ps

# List all containers
docker ps -a

# Stop specific container
docker stop yendorcats-api-staging

# Restart specific container
docker restart yendorcats-api-staging

# Remove container
docker rm yendorcats-api-staging
```

### Image Management
```bash
# List images
docker images

# Remove image
docker rmi yendorcats/api:staging

# Build specific service
docker-compose -f docker-compose.staging.yml build api

# Build without cache
docker-compose -f docker-compose.staging.yml build --no-cache
```

### Logs & Debugging
```bash
# View logs for all services
docker-compose -f docker-compose.staging.yml logs

# Follow logs for specific service
docker-compose -f docker-compose.staging.yml logs -f api

# Last 50 lines
docker-compose -f docker-compose.staging.yml logs --tail=50 api

# Execute command in container
docker exec -it yendorcats-api-staging bash
docker exec -it yendorcats-api-staging sqlite3 /app/data/yendorcats.db
```

---

## 📊 System Requirements

### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Disk**: 10GB free space
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

### Recommended Requirements
- **CPU**: 4 cores
- **RAM**: 8GB
- **Disk**: 20GB free space
- **Network**: Stable internet for image pulls

### Resource Allocation
| Service | CPU Limit | Memory Limit | CPU Reserve | Memory Reserve |
|---------|-----------|--------------|-------------|----------------|
| Frontend | 0.5 cores | 256MB | 0.25 cores | 128MB |
| API | 1.0 cores | 1GB | 0.5 cores | 512MB |
| Uploader | 0.5 cores | 512MB | 0.25 cores | 256MB |

---

## 🔧 Environment Variables

### Required Variables
```bash
# Storage Configuration
AWS_S3_BUCKET_NAME=your-bucket-name
AWS_S3_ACCESS_KEY=your-access-key
AWS_S3_SECRET_KEY=your-secret-key

# Security
YENDOR_JWT_SECRET=your-jwt-secret-32-chars-minimum

# Environment
ASPNETCORE_ENVIRONMENT=Staging
CONTAINERIZED_BUILD=true
```

### Optional Variables
```bash
# AWS Configuration
AWS_REGION=us-west-004
AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com

# B2 Configuration
B2_BUCKET_NAME=your-b2-bucket
B2_APPLICATION_KEY_ID=your-b2-key-id
B2_APPLICATION_KEY=your-b2-key
```

> **Full Reference**: [[Environment Variables Reference]]

---

## ⚡ Quick Troubleshooting

### Service Won't Start
```bash
# Check logs
docker-compose -f docker-compose.staging.yml logs service-name

# Check environment variables
docker exec service-name env | grep -i error

# Rebuild and restart
docker-compose -f docker-compose.staging.yml build --no-cache service-name
docker-compose -f docker-compose.staging.yml up -d
```

### CORS Issues
```bash
# Check CORS headers
curl -I http://localhost/api/cats

# Verify environment variable
docker exec yendorcats-api-staging env | grep CONTAINERIZED_BUILD
```

### Database Issues
```bash
# Check database file
docker exec yendorcats-api-staging ls -la /app/data/

# Access database
docker exec -it yendorcats-api-staging sqlite3 /app/data/yendorcats.db
```

> **Complete Troubleshooting**: [[Docker Troubleshooting]]

---

## 📁 Important Files

### Configuration Files
- `docker-compose.staging.yml` - [[Docker Deployment Guide#Staging Configuration]]
- `.env.staging` - [[Environment Variables Reference]]
- `frontend/nginx.conf` - [[Docker Networking Guide#Nginx Configuration]]

### Scripts
- `deploy-staging.sh` - [[Docker Scripts Reference#Staging Deployment]]
- `deploy-ecr.sh` - [[Docker Scripts Reference#ECR Deployment]]

### Dockerfiles
- `frontend/Dockerfile` - [[Docker Service Configuration#Frontend Service]]
- `backend/YendorCats.API/Dockerfile` - [[Docker Service Configuration#API Service]]
- `tools/file-uploader/Dockerfile` - [[Docker Service Configuration#Uploader Service]]

---

## 🎯 Common Tasks

### Update Single Service
```bash
# Rebuild and restart API
docker-compose -f docker-compose.staging.yml build api
docker-compose -f docker-compose.staging.yml up -d api
```

### View Resource Usage
```bash
# Container resource usage
docker stats

# Specific container stats
docker stats yendorcats-api-staging
```

### Backup Database
```bash
# Copy database file
docker cp yendorcats-api-staging:/app/data/yendorcats.db ./backup-$(date +%Y%m%d).db
```

### Clean Up
```bash
# Remove stopped containers
docker container prune

# Remove unused images
docker image prune

# Remove unused volumes
docker volume prune

# Full cleanup
docker system prune -a
```

---

## 🏷 Quick Tags

**Commands**: #docker-commands #docker-compose #container-management
**Networking**: #ports #endpoints #proxy #cors
**Monitoring**: #health-checks #logs #debugging #troubleshooting
**Maintenance**: #backup #cleanup #updates #resource-monitoring

---

> **Related**: [[Docker Commands Cheatsheet]] | [[Docker Troubleshooting]] | [[Docker Monitoring Guide]]
