---
title: "Docker Commands Cheatsheet - YendorCats"
tags: [docker, commands, cheatsheet, docker-compose, cli, reference]
aliases: [docker-commands, docker-cli, compose-commands]
created: 2025-07-27
updated: 2025-07-27
type: cheatsheet
status: complete
---

# Docker Commands Cheatsheet

> **Navigation**: [[README]] | [[Docker Quick Reference]] | [[Docker Deployment Guide]]

---

## 🐳 Docker Compose Commands

### Basic Operations
```bash
# Start all services (detached)
docker-compose -f docker-compose.staging.yml up -d

# Start all services (with logs)
docker-compose -f docker-compose.staging.yml up

# Stop all services
docker-compose -f docker-compose.staging.yml down

# Stop and remove volumes
docker-compose -f docker-compose.staging.yml down -v

# Restart all services
docker-compose -f docker-compose.staging.yml restart

# Restart specific service
docker-compose -f docker-compose.staging.yml restart api
```

### Build Operations
```bash
# Build all services
docker-compose -f docker-compose.staging.yml build

# Build specific service
docker-compose -f docker-compose.staging.yml build api

# Build without cache
docker-compose -f docker-compose.staging.yml build --no-cache

# Build and start
docker-compose -f docker-compose.staging.yml up --build -d
```

### Service Management
```bash
# List running services
docker-compose -f docker-compose.staging.yml ps

# View service logs
docker-compose -f docker-compose.staging.yml logs

# Follow logs for specific service
docker-compose -f docker-compose.staging.yml logs -f api

# View last 50 lines of logs
docker-compose -f docker-compose.staging.yml logs --tail=50 api

# Scale service (if supported)
docker-compose -f docker-compose.staging.yml up -d --scale api=2
```

---

## 🔧 Container Management

### Container Lifecycle
```bash
# List running containers
docker ps

# List all containers (including stopped)
docker ps -a

# Start container
docker start yendorcats-api-staging

# Stop container
docker stop yendorcats-api-staging

# Restart container
docker restart yendorcats-api-staging

# Remove container
docker rm yendorcats-api-staging

# Force remove running container
docker rm -f yendorcats-api-staging
```

### Container Inspection
```bash
# Inspect container configuration
docker inspect yendorcats-api-staging

# View container logs
docker logs yendorcats-api-staging

# Follow container logs
docker logs -f yendorcats-api-staging

# View last 100 lines
docker logs --tail=100 yendorcats-api-staging

# Container resource usage
docker stats yendorcats-api-staging

# Container processes
docker top yendorcats-api-staging
```

### Container Interaction
```bash
# Execute command in running container
docker exec yendorcats-api-staging ls -la /app

# Interactive shell
docker exec -it yendorcats-api-staging bash

# Run as specific user
docker exec -u root -it yendorcats-api-staging bash

# Copy files from container
docker cp yendorcats-api-staging:/app/data/yendorcats.db ./backup.db

# Copy files to container
docker cp ./config.json yendorcats-api-staging:/app/config.json
```

---

## 🖼 Image Management

### Image Operations
```bash
# List images
docker images

# List images with specific tag
docker images yendorcats/*

# Remove image
docker rmi yendorcats/api:staging

# Remove multiple images
docker rmi $(docker images yendorcats/* -q)

# Force remove image
docker rmi -f yendorcats/api:staging

# Build image from Dockerfile
docker build -t yendorcats/api:staging backend/YendorCats.API/

# Build with no cache
docker build --no-cache -t yendorcats/api:staging backend/YendorCats.API/
```

### Image Information
```bash
# Inspect image
docker inspect yendorcats/api:staging

# View image history
docker history yendorcats/api:staging

# Show image layers
docker image inspect yendorcats/api:staging --format='{{.RootFS.Layers}}'

# Image size information
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
```

---

## 🌐 Network Management

### Network Operations
```bash
# List networks
docker network ls

# Inspect network
docker network inspect yendorcats-staging-network

# Create network
docker network create yendorcats-custom

# Remove network
docker network rm yendorcats-custom

# Connect container to network
docker network connect yendorcats-staging-network container-name

# Disconnect container from network
docker network disconnect yendorcats-staging-network container-name
```

### Network Troubleshooting
```bash
# Test connectivity between containers
docker exec yendorcats-frontend-staging ping api

# Check port connectivity
docker exec yendorcats-frontend-staging nc -zv api 80

# View network configuration
docker exec yendorcats-api-staging ip addr show

# DNS resolution test
docker exec yendorcats-frontend-staging nslookup api
```

---

## 💾 Volume Management

### Volume Operations
```bash
# List volumes
docker volume ls

# Inspect volume
docker volume inspect yendorcats-api-data-staging

# Create volume
docker volume create yendorcats-custom-volume

# Remove volume
docker volume rm yendorcats-custom-volume

# Remove unused volumes
docker volume prune

# Backup volume
docker run --rm -v yendorcats-api-data-staging:/data -v $(pwd):/backup alpine tar czf /backup/data-backup.tar.gz -C /data .
```

### Volume Information
```bash
# Show volume usage
docker system df

# List volumes with size
docker volume ls --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}"

# Find volume location
docker volume inspect yendorcats-api-data-staging --format='{{.Mountpoint}}'
```

---

## 🔍 Debugging & Troubleshooting

### Log Analysis
```bash
# Search logs for errors
docker-compose -f docker-compose.staging.yml logs | grep -i error

# Search specific service logs
docker logs yendorcats-api-staging 2>&1 | grep -i "database"

# Export logs to file
docker logs yendorcats-api-staging > api-logs-$(date +%Y%m%d).log

# Real-time log monitoring
docker logs -f yendorcats-api-staging | grep -E "(ERROR|WARN|FATAL)"
```

### Health Checks
```bash
# Check container health status
docker inspect yendorcats-api-staging --format='{{.State.Health.Status}}'

# View health check logs
docker inspect yendorcats-api-staging --format='{{range .State.Health.Log}}{{.Output}}{{end}}'

# Manual health check
docker exec yendorcats-api-staging curl -f http://localhost/health
```

### Resource Monitoring
```bash
# Real-time resource usage
docker stats

# Resource usage for specific container
docker stats yendorcats-api-staging --no-stream

# Memory usage details
docker exec yendorcats-api-staging cat /proc/meminfo

# Disk usage in container
docker exec yendorcats-api-staging df -h
```

---

## 🧹 Cleanup Commands

### Container Cleanup
```bash
# Remove stopped containers
docker container prune

# Remove containers older than 24 hours
docker container prune --filter "until=24h"

# Remove all containers (dangerous!)
docker rm -f $(docker ps -aq)
```

### Image Cleanup
```bash
# Remove unused images
docker image prune

# Remove dangling images
docker image prune -f

# Remove all unused images
docker image prune -a

# Remove images older than 24 hours
docker image prune --filter "until=24h"
```

### System Cleanup
```bash
# Remove all unused resources
docker system prune

# Aggressive cleanup (removes everything unused)
docker system prune -a

# Remove everything including volumes
docker system prune -a --volumes

# Show disk usage
docker system df
```

---

## 🚀 ECR & Registry Commands

### ECR Authentication
```bash
# Login to ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-west-2.amazonaws.com

# Login to Docker Hub
docker login
```

### Image Tagging & Pushing
```bash
# Tag for ECR
docker tag yendorcats/api:staging 123456789012.dkr.ecr.us-west-2.amazonaws.com/yendorcats/api:staging

# Push to ECR
docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/yendorcats/api:staging

# Pull from ECR
docker pull 123456789012.dkr.ecr.us-west-2.amazonaws.com/yendorcats/api:staging
```

> **ECR Guide**: [[Docker ECR Integration]]

---

## 🔧 Development Shortcuts

### Quick Development Commands
```bash
# Rebuild and restart API only
docker-compose -f docker-compose.staging.yml build api && docker-compose -f docker-compose.staging.yml up -d api

# View API logs in real-time
docker-compose -f docker-compose.staging.yml logs -f api

# Quick database backup
docker cp yendorcats-api-staging:/app/data/yendorcats.db ./backup-$(date +%Y%m%d-%H%M%S).db

# Reset database (careful!)
docker-compose -f docker-compose.staging.yml down && docker volume rm yendorcats-api-data-staging && docker-compose -f docker-compose.staging.yml up -d
```

### Environment Debugging
```bash
# Check environment variables
docker exec yendorcats-api-staging env | sort

# Check specific environment variable
docker exec yendorcats-api-staging env | grep JWT_SECRET

# Test API connectivity
docker exec yendorcats-frontend-staging curl -I http://api:80/health
```

---

## 📝 Useful Aliases

Add these to your `~/.bashrc` or `~/.zshrc`:

```bash
# Docker Compose shortcuts
alias dcu='docker-compose -f docker-compose.staging.yml up -d'
alias dcd='docker-compose -f docker-compose.staging.yml down'
alias dcl='docker-compose -f docker-compose.staging.yml logs -f'
alias dcb='docker-compose -f docker-compose.staging.yml build'
alias dcp='docker-compose -f docker-compose.staging.yml ps'

# Docker shortcuts
alias dps='docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"'
alias dimg='docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"'
alias dstats='docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"'

# YendorCats specific
alias yc-logs='docker-compose -f docker-compose.staging.yml logs -f'
alias yc-health='curl -s http://localhost/health && curl -s http://localhost:5003/health && curl -s http://localhost:5002/health'
alias yc-restart='docker-compose -f docker-compose.staging.yml restart'
```

---

## 🏷 Command Categories

**Lifecycle**: #docker-up #docker-down #docker-restart #container-lifecycle
**Debugging**: #docker-logs #docker-exec #docker-inspect #troubleshooting
**Maintenance**: #docker-prune #docker-cleanup #volume-management #image-management
**Development**: #docker-build #docker-compose #development-workflow

---

> **Related**: [[Docker Quick Reference]] | [[Docker Troubleshooting]] | [[Docker Deployment Guide]]
