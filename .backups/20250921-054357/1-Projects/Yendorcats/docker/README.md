---
title: "Docker Integration Documentation - Table of Contents"
tags: [docker, deployment, containerization, microservices, documentation, toc]
aliases: [docker-docs, container-docs, deployment-docs]
created: 2025-07-27
updated: 2025-07-27
type: index
status: complete
---

# Docker Integration Documentation

> **Quick Access**: [[Docker Quick Reference]] | [[Docker Deployment Guide]] | [[Docker Troubleshooting]]

---

## 📋 Table of Contents

### 🚀 Quick Reference & Cheat Sheets
- [[Docker Quick Reference]] - Commands, ports, endpoints, and quick troubleshooting
- [[Docker Commands Cheatsheet]] - Essential Docker and Docker Compose commands
- [[Environment Variables Reference]] - Complete environment configuration guide

### 📖 Complete Guides
- [[Docker Architecture Overview]] - System architecture and service relationships
- [[Docker Deployment Guide]] - Step-by-step deployment instructions
- [[Docker Development Workflow]] - Development and testing procedures
- [[Docker Production Setup]] - Production deployment and ECR integration

### 🔧 Configuration & Setup
- [[Docker Service Configuration]] - Individual service configurations
- [[Docker Networking Guide]] - Container networking and communication
- [[Docker Storage & Volumes]] - Data persistence and volume management
- [[Docker Security Guide]] - Security best practices and configurations

### 🛠 Operations & Maintenance
- [[Docker Monitoring Guide]] - Health checks, logging, and monitoring
- [[Docker Troubleshooting]] - Common issues and solutions
- [[Docker Backup & Recovery]] - Data backup and disaster recovery
- [[Docker Performance Tuning]] - Optimization and resource management

### 🔄 CI/CD & Automation
- [[Docker CI-CD Pipeline]] - Continuous integration and deployment
- [[Docker Scripts Reference]] - Automation scripts and utilities
- [[Docker ECR Integration]] - Amazon ECR deployment and management

---

## 🏗 Architecture Overview

```mermaid
graph TB
    subgraph "YendorCats Docker Architecture"
        Client[Client Browser] --> Frontend[Frontend Container<br/>nginx:alpine<br/>Port 80]
        Frontend --> |/api/*| API[API Container<br/>.NET Core<br/>Port 5003]
        Frontend --> |/uploader/*| Uploader[Uploader Container<br/>Node.js<br/>Port 5002]
        API --> DB[(SQLite Database<br/>Persistent Volume)]
        Uploader --> Storage[S3/B2 Storage]
    end
```

---

## 📊 Service Summary

| Service | Container | Technology | Ports | Status |
|---------|-----------|------------|-------|--------|
| **Frontend** | `yendorcats-frontend-staging` | nginx + static files | 80, 443 | [[Docker Service Configuration#Frontend Service\|✅ Active]] |
| **API** | `yendorcats-api-staging` | .NET Core 8.0 | 5003 | [[Docker Service Configuration#API Service\|✅ Active]] |
| **Uploader** | `yendorcats-uploader-staging` | Node.js 18 | 5002 | [[Docker Service Configuration#Uploader Service\|✅ Active]] |

---

## 🎯 Quick Start

1. **Prerequisites**: [[Docker Deployment Guide#Prerequisites]]
2. **Environment Setup**: [[Environment Variables Reference]]
3. **Deploy Staging**: `./deploy-staging.sh`
4. **Verify Deployment**: [[Docker Quick Reference#Health Checks]]
5. **Deploy to Production**: [[Docker Production Setup]]

---

## 📁 File Structure

```
├── docker-compose.staging.yml          # [[Docker Deployment Guide#Staging Environment]]
├── docker-compose.production.yml       # [[Docker Production Setup]]
├── deploy-staging.sh                   # [[Docker Scripts Reference#Staging Deployment]]
├── deploy-ecr.sh                       # [[Docker Scripts Reference#ECR Deployment]]
├── .env.staging.template               # [[Environment Variables Reference]]
├── frontend/
│   ├── Dockerfile                      # [[Docker Service Configuration#Frontend Service]]
│   └── nginx.conf                      # [[Docker Networking Guide#Frontend Proxy]]
├── backend/YendorCats.API/
│   └── Dockerfile                      # [[Docker Service Configuration#API Service]]
└── tools/file-uploader/
    └── Dockerfile                      # [[Docker Service Configuration#Uploader Service]]
```

---

## 🔗 Related Documentation

### Internal Links
- [[YendorCats Architecture]] - Overall system architecture
- [[API Documentation]] - Backend API reference
- [[Frontend Documentation]] - Frontend implementation
- [[Storage Configuration]] - S3/B2 storage setup

### External Resources
- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Reference](https://docs.docker.com/compose/)
- [Amazon ECR Documentation](https://docs.aws.amazon.com/ecr/)

---

## 🏷 Tags & Categories

**Primary Tags**: #docker #deployment #containerization #microservices
**Technology Tags**: #nginx #dotnet #nodejs #sqlite #s3
**Process Tags**: #staging #production #cicd #monitoring #troubleshooting

---

## 📝 Document Status

- ✅ **Architecture Documented**: Complete service breakdown and relationships
- ✅ **Deployment Tested**: Staging environment fully functional
- ✅ **Scripts Created**: Automation scripts for deployment and management
- ✅ **Monitoring Setup**: Health checks and logging configured
- 🔄 **Production Ready**: ECR integration prepared, awaiting production deployment

---

> **Last Updated**: 2025-07-27  
> **Next Review**: Check for updates after production deployment  
> **Maintainer**: Development Team
