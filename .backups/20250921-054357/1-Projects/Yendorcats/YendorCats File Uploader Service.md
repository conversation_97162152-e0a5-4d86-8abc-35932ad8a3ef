---
creation_date: 2025-04-28
modification_date: 2025-04-28
type: resource
source: Internal documentation
tags: [para/resources, yendorcats, software-dev, file-uploader, node-js]
area: Software-Development
difficulty: medium
url: 
---
[[1-Projects/Yendorcats/YendorCats_Service_Level_Agreement]]
# YendorCats File Uploader Service

## Overview
The YendorCats File Uploader Service is a dedicated Node.js microservice that provides a user-friendly interface for uploading cat images to Backblaze B2 with rich metadata. The service is containerized for easy deployment and integrates seamlessly with the main YendorCats application.

## Key Points
- Node.js microservice for file uploads
- User-friendly web interface for adding metadata
- Uploads images to Backblaze B2 with S3-compatible API
- Handles file validation and error handling
- Containerized for easy deployment

## Technical Architecture

### Technologies Used
- **Node.js**: Server-side JavaScript runtime
- **Express**: Web framework for Node.js
- **Multer**: Middleware for handling multipart/form-data
- **AWS SDK**: For S3-compatible storage operations
- **Bootstrap**: Frontend framework for responsive design
- **Docker**: Containerization for deployment

### Directory Structure
```
tools/file-uploader/
├── public/                 # Static files
│   ├── css/                # Stylesheets
│   ├── js/                 # JavaScript files
│   └── index.html          # Main HTML file
├── server.js               # Main server file
├── routes/                 # API routes
├── middleware/             # Custom middleware
├── Dockerfile              # Docker configuration
├── package.json            # Node.js dependencies
└── .env.example            # Example environment variables
```

## Implementation Details

### Server Configuration
The server is configured in `server.js`:
- Sets up Express and middleware
- Configures AWS SDK for S3
- Defines routes for file uploads
- Handles error logging and reporting

```javascript
// Configure AWS SDK for S3
const s3 = new AWS.S3({
  endpoint: process.env.AWS_S3_ENDPOINT,
  accessKeyId: process.env.AWS_S3_ACCESS_KEY,
  secretAccessKey: process.env.AWS_S3_SECRET_KEY,
  region: process.env.AWS_S3_REGION,
  s3ForcePathStyle: true
});
```

### File Upload Endpoint
The main upload endpoint handles:
- File validation (size, type, etc.)
- Metadata extraction from form fields
- File upload to S3 with metadata
- Error handling and response formatting

```javascript
app.post('/upload', upload.single('file'), async (req, res) => {
  try {
    // Extract file and metadata
    const file = req.file;
    const { name, age, description, category } = req.body;
    
    // Format age for consistency
    const formattedAge = formatAge(age);
    
    // Generate S3 key
    const s3Key = `resources/${category}/${name}-${formattedAge}-${getDateString()}-1.${getFileExtension(file.originalname)}`;
    
    // Read file content
    const fileContent = fs.readFileSync(file.path);
    
    // Extract all metadata fields from the request
    const { 
      gender, 
      breed, 
      bloodline, 
      hair_color, 
      personality, 
      traits, 
      date_taken, 
      tags, 
      mother, 
      father 
    } = req.body;

    // Get file metadata
    const fileSize = file.size;
    const fileFormat = path.extname(file.originalname).substring(1);
    const dateUploaded = new Date().toISOString();
    
    // Upload to S3 with metadata
    const params = {
      Bucket: process.env.AWS_S3_BUCKET_NAME,
      Key: s3Key,
      Body: fileContent,
      ContentType: file.mimetype,
      ACL: 'public-read',
      Metadata: {
        // Required fields
        'name': name,
        'gender': gender || '',
        'category': category,
        
        // Optional user-provided fields
        'age': formattedAge,
        'description': description || '',
        'breed': breed || '',
        'bloodline': bloodline || '',
        'hair_color': hair_color || '',
        'personality': personality || '',
        'traits': traits || '',
        'tags': tags || '',
        'mother': mother || '',
        'father': father || '',
        
        // System metadata
        'date_uploaded': dateUploaded,
        'date_taken': date_taken ? new Date(date_taken).toISOString() : '',
        'file_format': fileFormat,
        'file_size': fileSize.toString(),
        'content_type': file.mimetype
      }
    };
    
    // Upload to S3
    await s3.upload(params).promise();
    
    // Return success response
    res.json({
      success: true,
      message: 'File uploaded successfully',
      fileUrl: `${process.env.API_BASE_URL}/resources/${category}/${path.basename(s3Key)}`
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    res.status(500).json({
      success: false,
      message: 'Error uploading file',
      error: error.message
    });
  }
});
```

### Frontend Interface
The frontend interface provides:
- File selection with drag-and-drop support
- Form fields for all metadata
- Preview of selected image
- Upload progress indicator
- Success/error messages

```html
<form id="uploadForm" enctype="multipart/form-data">
    <div class="form-group">
        <label for="name">Cat's Name <span class="required">*</span></label>
        <input type="text" id="name" name="name" required placeholder="e.g., Fluffy">
    </div>
    
    <div class="form-group">
        <label for="age">Age <span class="required">*</span></label>
        <input type="text" id="age" name="age" required placeholder="e.g., 2.5 (years)">
    </div>
    
    <div class="form-group">
        <label for="category">Category <span class="required">*</span></label>
        <select id="category" name="category" required>
            <option value="" disabled selected>Select category</option>
            <option value="studs">Studs (Male)</option>
            <option value="queens">Queens (Female)</option>
            <option value="kittens">Kittens</option>
            <option value="gallery">Gallery</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="gender">Gender <span class="required">*</span></label>
        <select id="gender" name="gender" required>
            <option value="" disabled selected>Select gender</option>
            <option value="M">Male</option>
            <option value="F">Female</option>
        </select>
    </div>
    
    <!-- Additional metadata fields -->
    <div class="form-group">
        <label for="breed">Breed</label>
        <input type="text" id="breed" name="breed" placeholder="e.g., Siamese">
    </div>
    
    <!-- More metadata fields... -->
    
    <div class="form-group">
        <label for="description">Description</label>
        <textarea id="description" name="description" rows="4" placeholder="Enter a description of the cat..."></textarea>
    </div>
    
    <button type="submit" id="uploadButton">Upload</button>
</form>
```

## Deployment

### Docker Configuration
The service is containerized using Docker:

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install --production

COPY . .

EXPOSE 80

CMD ["node", "server.js"]
```

### Environment Variables
The service requires the following environment variables:

```
AWS_S3_BUCKET_NAME=your-bucket-name
AWS_S3_REGION=us-west-004
AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
AWS_S3_ACCESS_KEY=your-access-key
AWS_S3_SECRET_KEY=your-secret-key
API_BASE_URL=https://your-api-url.com
PORT=80
```

### Deployment Steps
1. Build the Docker image:
   ```bash
   docker build -t yendorcats/file-uploader .
   ```

2. Run the container:
   ```bash
   docker run -p 5002:80 --env-file .env yendorcats/file-uploader
   ```

3. For Enhance Control Panel deployment, use the Docker integration to deploy the container.

## Usage Guide

### Uploading a New Image
1. Navigate to the file uploader interface (typically at `/upload`)
2. Click "Choose File" or drag and drop an image
3. Fill in the required fields (name, age, category, gender)
4. Add optional metadata (breed, bloodline, etc.)
5. Click "Upload"
6. Wait for the upload to complete
7. The success message will include a link to view the uploaded image

### Metadata Guidelines
- **Name**: The cat's name (required)
- **Age**: The cat's age in years (required, use decimal for months, e.g., 2.5 for 2 years and 6 months)
- **Category**: The category for the image (required, one of: studs, queens, kittens, gallery)
- **Gender**: The cat's gender (required, M or F)
- **Breed**: The cat's breed (optional)
- **Bloodline**: The cat's bloodline or lineage (optional)
- **Hair Color**: The cat's fur color or pattern (optional)
- **Personality**: Description of the cat's personality traits (optional)
- **Tags**: Keywords for filtering and searching (optional, comma-separated)
- **Mother/Father**: Names of the cat's parents (optional)
- **Description**: Detailed description of the cat or image (optional)

## Testing

### Manual Testing
1. Test file uploads with various image types and sizes
2. Test form validation for required fields
3. Test error handling for invalid inputs
4. Test metadata storage and retrieval

### Automated Testing
The service includes automated tests for:
- API endpoint functionality
- File validation
- Metadata handling
- Error scenarios

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[YendorCats File Uploader Service]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[YendorCats File Uploader Service]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "yendorcats") OR contains(tags, "file-uploader") OR contains(tags, "node-js")
LIMIT 5
```

## Quick Links
- [[YendorCats Project Documentation|Back to Project]]
- [[YendorCats S3 Metadata Implementation|S3 Metadata Documentation]]
- [[3-Resources|All Resources]]
