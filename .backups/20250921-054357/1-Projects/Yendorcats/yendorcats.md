---
creation_date: 2025-04-26
modification_date: 2025-04-26
type: project
status: active
priority: medium
deadline: 2025-05-26
project_owner: Jordan
project_client: Personal
completion_percentage: 0
estimated_hours: 75
tags: [para/projects, software-dev]
related: []
area: Software-Development
start_date: 2025-04-26
stakeholders: []
---

# yendorcats

## Overview
<!-- Brief description of the project -->

## Objectives
<!-- What are you trying to achieve? -->
- 

## Success Criteria
<!-- How will you know when the project is successful? -->
- 

## Tasks
<!-- List of tasks to complete -->
- [ ] 

## Timeline
- **Start Date**: 2025-04-26
- **Deadline**: 2025-05-26
- **Milestones**:
  - [ ] Initial Planning - 2025-05-03
  - [ ] Development - 2025-05-10
  - [ ] Testing - 2025-05-17
  - [ ] Completion - 2025-05-26

## Initial Planning
<!-- Links to relevant resources -->
 - [ ] Make up finance estimation sheet
	 - [[]]
## Resources
<!-- Links to relevant resources -->
- 

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(file.name, "Software-Development")
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "software-dev")
LIMIT 5
```

## Meeting Notes
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  date as "Date",
  participants as "Participants"
FROM #meeting
WHERE contains(related, "yendorcats") OR contains(file.name, "yendorcats")
SORT date DESC
```

## Progress Updates
<!-- Regular updates on project progress -->
### 2025-04-26 - Initial Setup
- Project created
- Initial planning started

## Create Related Notes
- [[yendorcats Meeting 2025-04-26|Create Meeting Note]]
- [[Templates/YendorCats Resource|Create Resource Note]]
- [[yendorcats Documentation|Create Documentation]]

## Related
- [[1-Projects]]
- [[Projects TOC]]
- [[Software-Development Overview]]
