---
creation_date: 2025-04-28
modification_date: 2025-04-28
type: resource
source: Internal documentation
tags: [para/resources, yendorcats, software-dev, testing]
area: Software-Development
difficulty: medium
url: 
---

# YendorCats Testing Guide

## Overview
This guide provides comprehensive instructions for testing the YendorCats application, with a focus on the S3 metadata implementation and file uploader service. It covers unit testing, integration testing, and end-to-end testing approaches.

## Key Points
- Testing is essential for ensuring the reliability of the metadata implementation
- Both automated and manual testing approaches are covered
- Tests are organized by component and functionality
- Special attention is given to edge cases and error handling

## Testing Environment Setup

### Prerequisites
- .NET 8 SDK for backend testing
- Node.js for file uploader testing
- Docker for containerized testing
- Backblaze B2 test bucket or minio for S3 testing

### Local Development Environment
1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/yendorcats.git
   cd yendorcats
   ```

2. Set up environment variables:
   ```bash
   # For backend testing
   export AWS_S3_ACCESS_KEY=your-test-access-key
   export AWS_S3_SECRET_KEY=your-test-secret-key
   export AWS_S3_BUCKET_NAME=your-test-bucket
   export AWS_S3_REGION=us-west-004
   export AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
   
   # For file uploader testing
   export PORT=5002
   export API_BASE_URL=http://localhost:5000
   ```

3. Set up a test database:
   ```bash
   # Using Docker
   docker run -d --name yendorcats-test-db -e MYSQL_ROOT_PASSWORD=password -e MYSQL_DATABASE=yendorcats_test -p 3306:3306 mariadb:10.6
   
   # Set connection string
   export DB_CONNECTION_STRING="Server=localhost;Database=yendorcats_test;User=root;Password=password;"
   ```

4. Set up a local S3-compatible server (optional):
   ```bash
   # Using MinIO
   docker run -d --name minio -p 9000:9000 -p 9001:9001 -e "MINIO_ROOT_USER=minioadmin" -e "MINIO_ROOT_PASSWORD=minioadmin" minio/minio server /data --console-address ":9001"
   
   # Create a test bucket
   aws --endpoint-url http://localhost:9000 s3 mb s3://yendorcats-test --profile minio
   
   # Update environment variables for local testing
   export AWS_S3_ENDPOINT=http://localhost:9000
   export AWS_S3_ACCESS_KEY=minioadmin
   export AWS_S3_SECRET_KEY=minioadmin
   export AWS_S3_BUCKET_NAME=yendorcats-test
   export AWS_S3_REGION=us-east-1
   ```

## Backend Testing

### Unit Testing

#### Setting Up Unit Tests
1. Navigate to the test project:
   ```bash
   cd backend/YendorCats.Tests
   ```

2. Run the tests:
   ```bash
   dotnet test
   ```

#### Testing the CatImageMetadata Model
Create tests for the `CatImageMetadata` class to verify:
- Conversion between S3 metadata and the model
- Handling of required and optional fields
- Validation of metadata values

```csharp
[Fact]
public void ToS3Metadata_WithRequiredFields_ReturnsValidDictionary()
{
    // Arrange
    var metadata = new CatImageMetadata
    {
        Name = "Fluffy",
        Gender = "F",
        FileFormat = "jpg",
        ContentType = "image/jpeg"
    };
    
    // Act
    var result = metadata.ToS3Metadata();
    
    // Assert
    Assert.Equal("Fluffy", result["name"]);
    Assert.Equal("F", result["gender"]);
    Assert.Equal("jpg", result["file_format"]);
    Assert.Equal("image/jpeg", result["content_type"]);
}

[Fact]
public void FromS3Metadata_WithValidMetadata_ReturnsPopulatedModel()
{
    // Arrange
    var s3Metadata = new Dictionary<string, string>
    {
        ["name"] = "Fluffy",
        ["gender"] = "F",
        ["breed"] = "Maine Coon",
        ["bloodline"] = "Champion lineage",
        ["hair_color"] = "Brown Tabby",
        ["date_uploaded"] = DateTime.UtcNow.ToString("o"),
        ["file_format"] = "jpg",
        ["content_type"] = "image/jpeg"
    };
    
    // Act
    var result = CatImageMetadata.FromS3Metadata(s3Metadata);
    
    // Assert
    Assert.Equal("Fluffy", result.Name);
    Assert.Equal("F", result.Gender);
    Assert.Equal("Maine Coon", result.Breed);
    Assert.Equal("Champion lineage", result.Bloodline);
    Assert.Equal("Brown Tabby", result.HairColor);
    Assert.Equal("jpg", result.FileFormat);
    Assert.Equal("image/jpeg", result.ContentType);
}
```

#### Testing the S3StorageService
Create tests for the `S3StorageService` class to verify:
- Uploading files with metadata
- Retrieving metadata from S3
- Handling errors and edge cases

```csharp
[Fact]
public async Task UploadFileAsync_WithValidParameters_ReturnsUrl()
{
    // Arrange
    var mockS3Client = new Mock<IAmazonS3>();
    mockS3Client.Setup(x => x.PutObjectAsync(It.IsAny<PutObjectRequest>(), It.IsAny<CancellationToken>()))
        .ReturnsAsync(new PutObjectResponse());
    
    var service = new S3StorageService(mockS3Client.Object, "test-bucket", "https://test-endpoint");
    
    var stream = new MemoryStream(Encoding.UTF8.GetBytes("test content"));
    var metadata = new Dictionary<string, string>
    {
        ["name"] = "Fluffy",
        ["gender"] = "F"
    };
    
    // Act
    var result = await service.UploadFileAsync("test-key.jpg", stream, "image/jpeg", metadata);
    
    // Assert
    Assert.Contains("test-key.jpg", result);
    mockS3Client.Verify(x => x.PutObjectAsync(
        It.Is<PutObjectRequest>(r => 
            r.Key == "test-key.jpg" && 
            r.ContentType == "image/jpeg" && 
            r.Metadata["name"] == "Fluffy" &&
            r.Metadata["gender"] == "F"),
        It.IsAny<CancellationToken>()),
        Times.Once);
}

[Fact]
public async Task GetObjectMetadataAsync_WithValidKey_ReturnsMetadata()
{
    // Arrange
    var mockS3Client = new Mock<IAmazonS3>();
    var metadata = new MetadataCollection
    {
        { "name", "Fluffy" },
        { "gender", "F" }
    };
    
    mockS3Client.Setup(x => x.GetObjectMetadataAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
        .ReturnsAsync(new GetObjectMetadataResponse { Metadata = metadata });
    
    var service = new S3StorageService(mockS3Client.Object, "test-bucket", "https://test-endpoint");
    
    // Act
    var result = await service.GetObjectMetadataAsync("test-key.jpg");
    
    // Assert
    Assert.Equal("Fluffy", result["name"]);
    Assert.Equal("F", result["gender"]);
}
```

#### Testing the CatGalleryController
Create tests for the `CatGalleryController` to verify:
- Retrieving images for a category
- Searching images based on metadata
- Handling errors and edge cases

```csharp
[Fact]
public async Task GetCategoryImages_WithValidCategory_ReturnsImages()
{
    // Arrange
    var mockService = new Mock<ICatGalleryService>();
    mockService.Setup(x => x.GetCategoryImagesAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>()))
        .ReturnsAsync(new List<CatGalleryImage>
        {
            new CatGalleryImage { Id = "1", CatName = "Fluffy", Category = "queens" },
            new CatGalleryImage { Id = "2", CatName = "Mittens", Category = "queens" }
        });
    
    var controller = new CatGalleryController(mockService.Object);
    
    // Act
    var result = await controller.GetCategoryImages("queens");
    
    // Assert
    var okResult = Assert.IsType<OkObjectResult>(result.Result);
    var images = Assert.IsAssignableFrom<IEnumerable<CatGalleryImage>>(okResult.Value);
    Assert.Equal(2, images.Count());
    Assert.Contains(images, i => i.CatName == "Fluffy");
    Assert.Contains(images, i => i.CatName == "Mittens");
}

[Fact]
public async Task SearchImages_WithFilters_ReturnsFilteredImages()
{
    // Arrange
    var mockService = new Mock<ICatGalleryService>();
    mockService.Setup(x => x.SearchImagesAsync(
        It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), 
        It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(),
        It.IsAny<float?>(), It.IsAny<float?>(), It.IsAny<string>(), It.IsAny<bool>()))
        .ReturnsAsync(new List<CatGalleryImage>
        {
            new CatGalleryImage { Id = "1", CatName = "Fluffy", Category = "queens", Gender = "F", Breed = "Maine Coon" }
        });
    
    var controller = new CatGalleryController(mockService.Object);
    
    // Act
    var result = await controller.SearchImages(
        category: "queens", 
        gender: "F", 
        breed: "Maine Coon");
    
    // Assert
    var okResult = Assert.IsType<OkObjectResult>(result.Result);
    var images = Assert.IsAssignableFrom<IEnumerable<CatGalleryImage>>(okResult.Value);
    Assert.Single(images);
    Assert.Equal("Fluffy", images.First().CatName);
    Assert.Equal("Maine Coon", images.First().Breed);
}
```

### Integration Testing

#### Setting Up Integration Tests
1. Create a test fixture for integration tests:
   ```csharp
   public class IntegrationTestFixture : IDisposable
   {
       public TestServer Server { get; }
       public HttpClient Client { get; }
       
       public IntegrationTestFixture()
       {
           var builder = new WebHostBuilder()
               .UseEnvironment("Testing")
               .UseStartup<TestStartup>();
           
           Server = new TestServer(builder);
           Client = Server.CreateClient();
       }
       
       public void Dispose()
       {
           Client.Dispose();
           Server.Dispose();
       }
   }
   ```

2. Create integration tests for the API endpoints:
   ```csharp
   public class CatGalleryControllerIntegrationTests : IClassFixture<IntegrationTestFixture>
   {
       private readonly HttpClient _client;
       
       public CatGalleryControllerIntegrationTests(IntegrationTestFixture fixture)
       {
           _client = fixture.Client;
       }
       
       [Fact]
       public async Task GetCategoryImages_ReturnsSuccessAndData()
       {
           // Act
           var response = await _client.GetAsync("/api/gallery/category/queens");
           
           // Assert
           response.EnsureSuccessStatusCode();
           var content = await response.Content.ReadAsStringAsync();
           var images = JsonSerializer.Deserialize<List<CatGalleryImage>>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
           
           Assert.NotNull(images);
           Assert.NotEmpty(images);
       }
       
       [Fact]
       public async Task SearchImages_WithFilters_ReturnsFilteredData()
       {
           // Act
           var response = await _client.GetAsync("/api/gallery/search?category=queens&gender=F&breed=Maine%20Coon");
           
           // Assert
           response.EnsureSuccessStatusCode();
           var content = await response.Content.ReadAsStringAsync();
           var images = JsonSerializer.Deserialize<List<CatGalleryImage>>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
           
           Assert.NotNull(images);
           Assert.All(images, image => 
           {
               Assert.Equal("queens", image.Category);
               Assert.Equal("F", image.Gender);
               Assert.Equal("Maine Coon", image.Breed);
           });
       }
   }
   ```

## File Uploader Testing

### Unit Testing

#### Setting Up Unit Tests
1. Navigate to the file uploader directory:
   ```bash
   cd tools/file-uploader
   ```

2. Install testing dependencies:
   ```bash
   npm install --save-dev jest supertest
   ```

3. Create a test configuration:
   ```javascript
   // jest.config.js
   module.exports = {
     testEnvironment: 'node',
     testMatch: ['**/*.test.js'],
     collectCoverage: true,
     coverageDirectory: 'coverage',
     coverageReporters: ['text', 'lcov'],
   };
   ```

4. Add test script to package.json:
   ```json
   "scripts": {
     "test": "jest"
   }
   ```

5. Run the tests:
   ```bash
   npm test
   ```

#### Testing the Upload Endpoint
Create tests for the upload endpoint to verify:
- File validation
- Metadata extraction
- S3 upload functionality
- Error handling

```javascript
const request = require('supertest');
const fs = require('fs');
const path = require('path');
const AWS = require('aws-sdk');

// Mock AWS S3
jest.mock('aws-sdk', () => {
  const mS3Instance = {
    upload: jest.fn().mockReturnThis(),
    promise: jest.fn().mockResolvedValue({ Location: 'https://test-bucket.s3.amazonaws.com/test-key' })
  };
  return {
    S3: jest.fn(() => mS3Instance)
  };
});

// Import server after mocking dependencies
const app = require('../server');

describe('File Upload API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('should upload file with metadata', async () => {
    // Create a test file
    const testFilePath = path.join(__dirname, 'test-image.jpg');
    fs.writeFileSync(testFilePath, 'test image content');
    
    // Perform the request
    const response = await request(app)
      .post('/upload')
      .field('name', 'Fluffy')
      .field('age', '2.5')
      .field('category', 'queens')
      .field('gender', 'F')
      .field('breed', 'Maine Coon')
      .field('bloodline', 'Champion lineage')
      .field('hair_color', 'Brown Tabby')
      .field('personality', 'Playful, Affectionate')
      .field('tags', 'kitten, playful, adoption')
      .attach('file', testFilePath);
    
    // Clean up test file
    fs.unlinkSync(testFilePath);
    
    // Assertions
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.message).toBe('File uploaded successfully');
    
    // Verify S3 upload was called with correct parameters
    const s3Instance = new AWS.S3();
    expect(s3Instance.upload).toHaveBeenCalledTimes(1);
    
    const uploadParams = s3Instance.upload.mock.calls[0][0];
    expect(uploadParams.Bucket).toBe(process.env.AWS_S3_BUCKET_NAME);
    expect(uploadParams.ContentType).toBe('image/jpeg');
    expect(uploadParams.Metadata.name).toBe('Fluffy');
    expect(uploadParams.Metadata.gender).toBe('F');
    expect(uploadParams.Metadata.breed).toBe('Maine Coon');
    expect(uploadParams.Metadata.bloodline).toBe('Champion lineage');
    expect(uploadParams.Metadata.hair_color).toBe('Brown Tabby');
    expect(uploadParams.Metadata.personality).toBe('Playful, Affectionate');
    expect(uploadParams.Metadata.tags).toBe('kitten, playful, adoption');
  });
  
  test('should return error for missing required fields', async () => {
    // Create a test file
    const testFilePath = path.join(__dirname, 'test-image.jpg');
    fs.writeFileSync(testFilePath, 'test image content');
    
    // Perform the request without required fields
    const response = await request(app)
      .post('/upload')
      .attach('file', testFilePath);
    
    // Clean up test file
    fs.unlinkSync(testFilePath);
    
    // Assertions
    expect(response.status).toBe(400);
    expect(response.body.success).toBe(false);
    expect(response.body.message).toContain('required');
  });
  
  test('should return error for invalid file type', async () => {
    // Create a test file
    const testFilePath = path.join(__dirname, 'test-file.txt');
    fs.writeFileSync(testFilePath, 'test text content');
    
    // Perform the request with invalid file type
    const response = await request(app)
      .post('/upload')
      .field('name', 'Fluffy')
      .field('age', '2.5')
      .field('category', 'queens')
      .field('gender', 'F')
      .attach('file', testFilePath);
    
    // Clean up test file
    fs.unlinkSync(testFilePath);
    
    // Assertions
    expect(response.status).toBe(400);
    expect(response.body.success).toBe(false);
    expect(response.body.message).toContain('file type');
  });
});
```

### End-to-End Testing

#### Manual Testing Checklist
1. **File Upload Interface**:
   - [ ] Verify the upload form loads correctly
   - [ ] Test file selection via browse button
   - [ ] Test drag-and-drop file selection
   - [ ] Verify image preview works
   - [ ] Test form validation for required fields
   - [ ] Test file type validation
   - [ ] Test file size validation

2. **Metadata Entry**:
   - [ ] Verify all metadata fields are present
   - [ ] Test entering data in all fields
   - [ ] Test form validation for required fields
   - [ ] Test special characters in text fields
   - [ ] Test date picker for date fields

3. **Upload Process**:
   - [ ] Verify upload progress indicator works
   - [ ] Test successful upload flow
   - [ ] Test error handling for network issues
   - [ ] Test error handling for server errors
   - [ ] Verify success message and link to uploaded image

4. **Gallery Integration**:
   - [ ] Verify uploaded image appears in the gallery
   - [ ] Verify metadata is correctly displayed
   - [ ] Test filtering by uploaded metadata
   - [ ] Test searching for the uploaded image

#### Automated End-to-End Testing
For automated end-to-end testing, you can use tools like Cypress or Playwright:

```javascript
// Example Cypress test for file upload
describe('File Upload Flow', () => {
  beforeEach(() => {
    cy.visit('/upload');
  });
  
  it('should upload a file with metadata', () => {
    // Attach file
    cy.get('input[type=file]').attachFile('test-image.jpg');
    
    // Fill in metadata
    cy.get('#name').type('Fluffy');
    cy.get('#age').type('2.5');
    cy.get('#category').select('queens');
    cy.get('#gender').select('F');
    cy.get('#breed').type('Maine Coon');
    cy.get('#bloodline').type('Champion lineage');
    cy.get('#hair_color').type('Brown Tabby');
    cy.get('#personality').type('Playful, Affectionate');
    cy.get('#tags').type('kitten, playful, adoption');
    
    // Submit form
    cy.get('#uploadButton').click();
    
    // Verify success message
    cy.get('.success-message').should('be.visible');
    cy.get('.success-message').should('contain', 'File uploaded successfully');
    
    // Verify image appears in gallery
    cy.visit('/gallery/queens');
    cy.get('.gallery-item').should('contain', 'Fluffy');
  });
  
  it('should show validation errors for missing required fields', () => {
    // Attach file
    cy.get('input[type=file]').attachFile('test-image.jpg');
    
    // Submit form without filling required fields
    cy.get('#uploadButton').click();
    
    // Verify validation errors
    cy.get('.error-message').should('be.visible');
    cy.get('#name').should('have.class', 'is-invalid');
    cy.get('#gender').should('have.class', 'is-invalid');
  });
});
```

## Performance Testing

### Load Testing
1. Install k6 for load testing:
   ```bash
   # Download and install k6
   wget https://github.com/loadimpact/k6/releases/download/v0.33.0/k6-v0.33.0-linux-amd64.tar.gz
   tar -xzf k6-v0.33.0-linux-amd64.tar.gz
   sudo cp k6-v0.33.0-linux-amd64/k6 /usr/local/bin/
   ```

2. Create a load test script for the gallery API:
   ```javascript
   // gallery-load-test.js
   import http from 'k6/http';
   import { sleep, check } from 'k6';
   
   export const options = {
     vus: 10,
     duration: '30s',
   };
   
   export default function () {
     const res = http.get('https://api.yendorcats.yourdomain.com/api/gallery/category/queens');
     
     check(res, {
       'status is 200': (r) => r.status === 200,
       'response time < 500ms': (r) => r.timings.duration < 500,
     });
     
     sleep(1);
   }
   ```

3. Run the load test:
   ```bash
   k6 run gallery-load-test.js
   ```

### Performance Metrics to Monitor
- Response time for gallery API endpoints
- Image load time in the frontend
- S3 upload time for new images
- Database query performance
- Memory and CPU usage during peak loads

## Security Testing

### OWASP Top 10 Checklist
1. **Injection**:
   - [ ] Test SQL injection in search parameters
   - [ ] Test command injection in file paths
   - [ ] Test XSS in metadata fields

2. **Broken Authentication**:
   - [ ] Test admin authentication endpoints
   - [ ] Test session management
   - [ ] Test password policies

3. **Sensitive Data Exposure**:
   - [ ] Verify HTTPS is enforced
   - [ ] Check for sensitive data in logs
   - [ ] Verify S3 bucket permissions

4. **XML External Entities (XXE)**:
   - [ ] Test XML parsing if used

5. **Broken Access Control**:
   - [ ] Test access to admin functions
   - [ ] Test direct object references

6. **Security Misconfiguration**:
   - [ ] Check for default credentials
   - [ ] Verify error handling doesn't expose details
   - [ ] Check for unnecessary open ports

7. **Cross-Site Scripting (XSS)**:
   - [ ] Test for stored XSS in metadata
   - [ ] Test for reflected XSS in search parameters

8. **Insecure Deserialization**:
   - [ ] Test serialization/deserialization if used

9. **Using Components with Known Vulnerabilities**:
   - [ ] Scan dependencies for vulnerabilities
   - [ ] Check for outdated components

10. **Insufficient Logging & Monitoring**:
    - [ ] Verify security events are logged
    - [ ] Check log retention policies

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[YendorCats Testing Guide]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[YendorCats Testing Guide]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "yendorcats") OR contains(tags, "testing") OR contains(tags, "software-dev")
LIMIT 5
```

## Quick Links
- [[YendorCats Project Documentation|Back to Project]]
- [[YendorCats S3 Metadata Implementation|S3 Metadata Documentation]]
- [[YendorCats File Uploader Service|File Uploader Documentation]]
- [[3-Resources|All Resources]]
