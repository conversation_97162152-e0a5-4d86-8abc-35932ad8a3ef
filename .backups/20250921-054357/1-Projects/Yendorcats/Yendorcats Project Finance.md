---
creation_date: 2025-04-27
modification_date: 2025-04-27
type: project
status: active
priority: medium
deadline: 2025-05-27
project_owner: Jordan
project_client: Yendorcats
completion_percentage: 0
estimated_hours: 75
tags: [para/projects, software-dev, web, development, website, yendor, cats]
related: []
area: Software-Development
start_date: 2025-04-27
stakeholders: []
---

# Yendorcats Project Finance

## Overview
<!-- Brief description of the project -->

## Objectives
<!-- What are you trying to achieve? -->
- 

## Success Criteria
<!-- How will you know when the project is successful? -->
- 

## Tasks
<!-- List of tasks to complete -->
- [ ] 

## Timeline
- **Start Date**: 2025-04-27
- **Deadline**: 2025-05-27
- **Milestones**:
  - [ ] Initial Planning - 2025-05-04
  - [ ] Development - 2025-05-11
  - [ ] Testing - 2025-05-18
  - [ ] Completion - 2025-05-27

## Resources
<!-- Links to relevant resources -->
- 

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(file.name, "Software-Development")
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "software-dev")
LIMIT 5
```

## Meeting Notes
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  date as "Date",
  participants as "Participants"
FROM #meeting
WHERE contains(related, "Yendorcats Project Finance") OR contains(file.name, "Yendorcats Project Finance")
SORT date DESC
```

## Progress Updates
<!-- Regular updates on project progress -->
### 2025-04-27 - Initial Setup
- Project created
- Initial planning started

## Create Related Notes
- [[Yendorcats Project Finance Meeting 2025-04-27|Create Meeting Note]]
- [[Yendorcats Project Finance Resource|Create Resource Note]]
- [[Yendorcats Project Finance Documentation|Create Documentation]]

## Related
- [[1-Projects]]
- [[Projects TOC]]
- [[Web Development Overview]]
