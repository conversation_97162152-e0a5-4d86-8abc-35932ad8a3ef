---
creation_date: 2025-04-06
modification_date: 2025-04-16
type: project
status: active
priority: high
deadline: 2025-06-30
project_owner: Jordan
project_client: Personal
completion_percentage: 80
estimated_hours: 60
tags: [para/projects, networking, zerotier, architecture, security, docker, nginx, vpn, aws, ec2, iptables, ufw, infrastructure]
related: ["Minimal Gateway For ZeroTier Networks", "Secure ZeroTier Network Separation", "EC2 Bridge Server Maintenance"]
area: "Network-Administration"
project: "Bridge Network"
ip_addresses: ["************", "*************", "**********"]
domains: ["get.spacey.my", "prowl.spacey.my", "watch.spacey.my", "radarr.spacey.my", "sonarr.spacey.my", "spacey.my"]
---

# Network Architecture Overview

This document provides a comprehensive overview of our secure network architecture leveraging ZeroTier networks, an EC2 bridge, and Nginx Proxy Manager in Docker to securely expose services to the internet.

## Architecture Components

### Current Simplified Architecture (Single ZeroTier Network)

```mermaid
graph LR
    User[User Browser] -->|HTTPS| AWS[AWS EC2 Bridge];

    subgraph AWS
        EC2[Public IP] --> IPT_EC2{iptables DNAT};
        IPT_EC2 --> EC2_ZT[ZT Client x.63];
        EC2_ZT -->|ZeroTier| ZT_Net[ZT Net ***********/24];
        EC2_ZT --> IPT_MASQ{iptables MASQ};
        IPT_MASQ --> EC2;
    end

    ZT_Net --> Home[Home Server Pop-OS];

    subgraph Home
        Home_ZT[ZT Client x.217] --> UFW{UFW};
        UFW --> IPT_Host{Host iptables};

        subgraph Docker Net **********/16
            IPT_Host --> NPM[NPM x.0.2];
            NPM --> FLR[Flaresolverr];
        end

        subgraph Host Services
             NPM --> QB[QB 8055];
             NPM --> PR[Prowlarr 9696];
             NPM --> JF[Jellyfin 8096];
             NPM --> RD[Radarr 7878];
             NPM --> SN[Sonarr 8989];
             NPM --> Dash[Dash Plan];
             PR --> FLR;
        end

        IPT_Host --> Host_Services[Host Services];

        %% Response Path Simplified
        NPM --> IPT_Host;
        FLR --> NPM;
        Host_Services --> IPT_Host;
        IPT_Host --> UFW;
        UFW --> Home_ZT;
    end

    Home_ZT --> ZT_Net;
    ZT_Net --> EC2_ZT;
    AWS --> User;

```
*See [[Simplified ZeroTier Network Setup]] for implementation details.*

### Alternative Security Architecture (Dual Network with Gateway - Future Reference)

```mermaid
graph LR
    subgraph Internet
        User[User Browser]
    end

    subgraph AWS_EC2 [AWS EC2 Bridge]
        EC2[EC2 Instance Public IP];
        EC2ZT[ZeroTier Client Bridge Network];
        IPT[iptables Forwarding];
    end

    subgraph ZT_Bridge_Net [ZeroTier Bridge Network]
        ZTB[ZT Network A];
    end

    subgraph OnPrem_GW [On-Prem Gateway]
        Gateway[Minimal Gateway Server];
        GatewayZT_A[ZeroTier Client Bridge Network];
        GatewayZT_B[ZeroTier Client Service Network];
        IPT_GW[iptables Forwarding];
    end

    subgraph ZT_Service_Net [ZeroTier Service Network]
        ZTS[ZT Network B];
    end

    subgraph Home_Server [Home Server Pop-OS]
        direction LR
        PopOS[Host OS];
        subgraph Docker_Network [Docker]
            NPM[Nginx Proxy Manager];
        end
        PopOSZT[ZeroTier Client Service Network];
        Services[Backend Services];
    end

    User --> EC2;
    EC2 -- Fwd --> ZTB --> GatewayZT_A;
    GatewayZT_A -- GW Fwd --> ZTS --> PopOSZT;
    PopOSZT --> NPM --> Services;
```
*See [[Minimal Gateway For ZeroTier Networks]] for design details.*

## Network Layers (Simplified Architecture)

1.  **Internet**: Public network where users access services via DNS.
2.  **AWS EC2 Network**: The VPC network where the EC2 bridge resides.
3.  **ZeroTier Network (`***********/24`)**: Secure, encrypted overlay network connecting the EC2 bridge and the Home Server.
4.  **Home Server Local LAN (`***********/16`)**: The physical home network.
5.  **Docker Bridge Network (`**********/16`)**: Isolated network created by Docker Compose for Nginx Proxy Manager and Flaresolverr.

## Component Roles (Simplified Architecture)

### 1. EC2 Bridge Server (`************` on ZT)
*   **Purpose**: Internet-facing entry point, performs NAT and port forwarding using `iptables`.
*   **Location**: AWS.
*   **Security**: AWS Security Groups, minimal OS hardening, `iptables` rules only forward necessary ports (80/443) to the Nginx host.
*   **Networks**: Internet, AWS VPC, ZeroTier.

### 2. ZeroTier Network
*   **Purpose**: Provides secure, direct connectivity between the EC2 instance and the Home Server, bypassing complex NAT traversal.
*   **Traffic**: Primarily forwarded HTTP/HTTPS traffic, ICMP for diagnostics.
*   **Access control**: ZeroTier Flow Rules restrict traffic to only allow communication between the Bridge and Nginx host on ports 80/443.

### 3. Home Server (Pop-OS) (`*************` on ZT)
*   **Purpose**: Hosts Docker environment and backend services.
*   **Location**: Private local network.
*   **Security**: `UFW` firewall restricts access based on source IP/network and port. `iptables` handles forwarding between Docker and ZeroTier interfaces.
*   **Networks**: Local LAN, ZeroTier, Docker Bridge.

### 4. Docker (on Home Server)
*   **Purpose**: Runs containerized applications (Nginx Proxy Manager, Flaresolverr).
*   **Network**: Custom bridge network (`torrents_proxy-manager`, subnet `**********/16`).
*   **Connectivity**: Relies on host `iptables` FORWARD and NAT rules to communicate with the ZeroTier network (`ztmjfcpubr`) and host-based services.

### 5. Nginx Proxy Manager (Docker Container @ `**********`)
*   **Purpose**: Reverse proxy, SSL termination (Let's Encrypt), hostname routing, access control.
*   **Connectivity**: Receives traffic on host ports 80/443 (mapped by Docker). Forwards requests to backend services using the host's ZeroTier IP (`*************`) and the respective service port.

### 6. Backend Services (on Home Server Host)
*   **qBittorrent**: Torrent client, WebUI on port `8055`.
*   **Prowlarr**: Indexer manager, WebUI on port `9696`. Communicates with Flaresolverr.
*   **Jellyfin**: Media server, WebUI on port `8096`.
*   **Radarr**: Movie management, WebUI on port `7878` (assuming host install).
*   **Sonarr**: TV Show management, WebUI on port `8989` (assuming host install).
*   **Flaresolverr (Docker Container)**: Proxy solver for Prowlarr. Runs in the same Docker network (`**********/16`) as Nginx Proxy Manager.
*   **Web Dashboard (Planned)**: Future service, likely accessed via base domain.
*   **Access**: Services bind to `0.0.0.0` or `localhost` on the host. UFW rules allow access primarily from the Docker subnet (`**********/16`) and the local LAN (`***********/16`).

## Service Endpoints & Domains

| Service | Domain(s) | Backend Target (Forwarded by NPM) | Notes |
|---|---|---|---|
| qBittorrent | `get.spacey.my` | `http://*************:8055` |  |
| Prowlarr | `prowl.spacey.my` | `http://*************:9696` |  |
| Jellyfin | `watch.spacey.my` | `http://*************:8096` | Requires Websockets Support in NPM |
| Radarr | `radarr.spacey.my` (example) | `http://*************:7878` | Assuming host install |
| Sonarr | `sonarr.spacey.my` (example) | `http://*************:8989` | Assuming host install |
| Web Dashboard | `spacey.my` (Planned) | `http://*************:PORT` (TBD) | Base domain |
| Nginx Proxy Manager | - | Accessed via `http://<local_ip>:81` | Admin UI - restricted by UFW |
| Flaresolverr | - | `http://flaresolverr:8191` | Accessed internally by Prowlarr via Docker network |

## Data Flow Path (Example: Accessing Prowlarr)

1.  **Client Request**: User accesses `https://prowl.spacey.my`.
2.  **DNS Resolution**: `prowl.spacey.my` points to EC2 Public IP.
3.  **EC2 Bridge**: Receives HTTPS on port 443.
4.  **EC2 iptables**: DNATs destination to `*************:443`. Allows FORWARD from public (`enX0`) to ZeroTier (`ztmjfcpubr`).
5.  **ZeroTier**: Traffic traverses the overlay to the Home Server.
6.  **Home Server (Host)**: Receives traffic on `ztmjfcpubr`.
7.  **Home Server UFW**: Allows traffic `in on ztmjfcpubr` from `************` to port 443.
8.  **Docker Port Mapping**: Host port 443 maps to NPM container port 443.
9.  **Nginx Proxy Manager**: Terminates SSL for `prowl.spacey.my`. Finds proxy rule.
10. **Proxy Forward**: NPM initiates a *new* HTTP connection from `**********` to `*************:9696`.
11. **Home Server iptables**: FORWARD rules allow traffic from `br-fc93a4406e3f` to `ztmjfcpubr` (or host). NAT rule masquerades source IP if needed.
12. **Home Server UFW**: Allows traffic from Docker subnet (`**********/16`) to host port 9696.
13. **Prowlarr Service**: Receives HTTP request on port 9696.
14. **(If Prowlarr needs Flaresolverr)**: Prowlarr sends HTTP request to `flaresolverr:8191` (resolved via Docker DNS within `**********/16` network).
15. **Flaresolverr**: Processes request and responds to Prowlarr.
16. **Response**: Follows the reverse path.

## Security Benefits & Considerations

*   **Isolation**: Backend services are not directly exposed to the internet.
*   **Controlled Flow**: Multiple layers of filtering (AWS SG, EC2 iptables, ZeroTier Flow Rules, Host UFW, Host iptables, NPM Access Lists).
*   **SSL Termination**: Handled centrally and automatically renewed by NPM.
*   **Vulnerability**: EC2 compromise is the main risk. Strong host firewalls and ZeroTier rules mitigate lateral movement.
*   **Docker Security**: Custom bridge network enhances isolation. iptables rules control Docker's external access.

## Configuration Files & Key Settings

*   **EC2 Bridge**: `/etc/iptables/rules.v4` (Managed by `iptables-persistent`, see [[Simplified ZeroTier Network Setup#Configure EC2 Bridge Server iptables Forwarding]])
*   **Home Server (Pop-OS)**:
    *   UFW Rules: Managed via `ufw` commands (see [[Simplified ZeroTier Network Setup#Configure Nginx Host Firewall (UFW)]])
    *   iptables Rules: `/etc/iptables/rules.v4` (Managed by `iptables-persistent`, see [[Simplified ZeroTier Network Setup#Configure Nginx Host iptables (Docker <-> ZeroTier)]])
    *   NPM Docker Compose: `/path/to/your/docker-compose.yml`
    *   Flaresolverr Docker Compose: (If separate from NPM compose)
    *   Radarr/Sonarr Docker Compose: (If using Docker, see [[Brain/Family/Docker Configuration Files]])
*   **ZeroTier Central**: Network Flow Rules (see [[Simplified ZeroTier Network Setup#Configure ZeroTier Flow Rules]])
*   **Nginx Proxy Manager**: Configuration is managed via the Web UI (data stored in `npm_data` Docker volume).

## Implementation Details

- [[Simplified ZeroTier Network Setup]] - Step-by-step guide for the current architecture.
- [[EC2 Bridge Server Maintenance]] - EC2 configuration details.
- [[ZeroTier Flow Rules Placement]] - Network rules configuration.
- [[Troubleshooting ZeroTier and Docker Networking]] - Diagnostic steps.

## Tasks

- [ ] Regularly update all components (EC2, Gateway, Nginx, Services)
- [ ] Review and test network flow rules quarterly
- [ ] Verify security groups on EC2 instance
- [ ] Confirm gateway logs for unauthorized access attempts
- [ ] Test restoration procedure for gateway
- [ ] Update DNS records if EC2 IP changes
- [ ] Review AWS billing for unexpected charges

## Disaster Recovery

### EC2 Bridge Loss
1. Launch new EC2 instance
2. Install ZeroTier
3. Configure iptables rules
4. Join bridge network
5. Update DNS

### Gateway Loss
1. Provision new minimal server
2. Follow [[Minimal Gateway For ZeroTier Networks]] setup
3. Restore from backup if available
4. Test connectivity

### Complete Restoration
See specific recovery docs for each component.

## Future Improvements

- [ ] Consider redundant gateway for high availability
- [ ] Implement automated health checks
- [ ] Add intrusion detection on gateway
- [ ] Consider WireGuard as alternate to ZeroTier
- [ ] Add metrics collection for traffic analysis

## Related
- [[1-Projects]]
- [[Minimal Gateway For ZeroTier Networks]]
- [[Secure ZeroTier Network Separation]]
- [[Simplified ZeroTier Network Setup]]
- [[EC2 Bridge Server Maintenance]]
- [[ZeroTier Flow Rules Placement]]
- [[Troubleshooting ZeroTier and Docker Networking]]
