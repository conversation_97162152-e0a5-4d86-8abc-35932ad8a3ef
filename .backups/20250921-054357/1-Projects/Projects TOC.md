---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: index
aliases: [Projects TOC, Projects Table of Contents]
tags: [para/projects, index, toc]
---

# Projects Table of Contents

> A comprehensive index of all projects organized by category.

## Website Projects
- [[Brain/1-Projects/CRUCA Website/CRUCA Website Updates]] - Updates to the Caboolture Uniting Church website
- [[Website Redesign]] - Project to redesign and modernize the church website
- [[YendorCats Project Documentation]] - Maine Coon cat breeder gallery website with .NET backend and JavaScript frontend

## Network Projects
- [[<media streaming and indexing cluster> Network Architecture Overview]] - Comprehensive overview of secure network architecture

## Media Server Projects
- [[Media Streaming Server Setup]] - Setting up a media streaming server on a VPN network

## Self-Hosted AI Projects
- [[Self-Hosted AI Options]] - Options for self-hosted AI prompt applications
- [[MCP Server Project Planning Phase]] - Setting up a centralized MCP server for unified context across development environments

## Family Projects
```dataview
LIST
FROM "1-Projects/Family"
SORT file.name ASC
```

## University Projects
```dataview
LIST
FROM "1-Projects/University"
SORT file.name ASC
```

## Business Projects
```dataview
LIST
FROM "1-Projects/Business"
SORT file.name ASC
```

## All Projects by Status
```dataview
TABLE
  status as "Status",
  priority as "Priority",
  deadline as "Deadline"
FROM "1-Projects"
SORT status ASC, priority ASC
```

## Related
- [[1-Projects]]
- [[Home]]
