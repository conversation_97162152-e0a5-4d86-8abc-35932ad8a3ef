---

# Multi-Client Vault Setup for YendorCats

## Overview

This guide covers setting up HashiCorp Vault to manage secrets for multiple cat breeder clients, each with their own isolated environments, databases, and API keys while maintaining cost-effectiveness.

## Tags
#vault #multi-client #isolation #policies #namespaces #client-management #yendorcats #enhance

---

## Multi-Client Architecture

### Client Isolation Strategy

```
vault/
├── secret/
│   ├── client1/
│   │   └── yendorcats/
│   │       ├── app-secrets
│   │       ├── db-credentials
│   │       └── api-keys
│   ├── client2/
│   │   └── yendorcats/
│   │       ├── app-secrets
│   │       ├── db-credentials
│   │       └── api-keys
│   └── shared/
│       ├── common-configs
│       └── templates
```

### Benefits of This Approach

- ✅ **Complete Isolation**: Each client's secrets are separate
- ✅ **Scalable**: Easy to add new clients
- ✅ **Secure**: Client-specific access policies
- ✅ **Cost-Effective**: Single Vault instance serves all clients
- ✅ **Manageable**: Centralized secret management

---

## Client Onboarding Process

### 1. Create Client Directory Structure

```bash
#!/bin/bash
# onboard-client.sh

CLIENT_NAME="$1"
if [ -z "$CLIENT_NAME" ]; then
    echo "Usage: $0 <client-name>"
    exit 1
fi

echo "Onboarding client: $CLIENT_NAME"

# Create client-specific secret paths
vault kv put secret/$CLIENT_NAME/yendorcats/app-secrets \
    DbConnectionString="Server=$CLIENT_NAME-db;Database=YendorCats;User=catuser;Password=TEMP_PASSWORD;" \
    JwtSecret="$(openssl rand -base64 64)" \
    JwtIssuer="YendorCatsApi-$CLIENT_NAME" \
    JwtAudience="YendorCatsClients-$CLIENT_NAME" \
    JwtExpiryMinutes=60 \
    RefreshExpiryDays=7 \
    S3AccessKey="TEMP_S3_KEY" \
    S3SecretKey="TEMP_S3_SECRET" \
    ApiKey="$(openssl rand -hex 32)"

echo "Created secret structure for $CLIENT_NAME"
```

### 2. Client-Specific Policies

```bash
# Create policy for each client
create_client_policy() {
    local CLIENT_NAME="$1"
    
    vault policy write ${CLIENT_NAME}-policy - << EOF
# Allow full access to client's own secrets
path "secret/data/$CLIENT_NAME/yendorcats/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Allow reading shared configurations
path "secret/data/shared/*" {
  capabilities = ["read", "list"]
}

# Deny access to other clients
path "secret/data/+/yendorcats/*" {
  capabilities = ["deny"]
}

# Allow token self-renewal
path "auth/token/renew-self" {
  capabilities = ["update"]
}

# Allow token lookup
path "auth/token/lookup-self" {
  capabilities = ["read"]
}
EOF

    echo "Created policy for $CLIENT_NAME"
}

# Usage
create_client_policy "client1"
create_client_policy "client2"
```

### 3. Client Authentication Setup

```bash
# Create AppRole for each client
setup_client_auth() {
    local CLIENT_NAME="$1"
    
    # Create AppRole
    vault write auth/approle/role/${CLIENT_NAME}-role \
        token_policies="${CLIENT_NAME}-policy" \
        token_ttl=1h \
        token_max_ttl=4h \
        bind_secret_id=true \
        secret_id_ttl=24h
    
    # Get role ID
    ROLE_ID=$(vault read -field=role_id auth/approle/role/${CLIENT_NAME}-role/role-id)
    
    # Generate secret ID
    SECRET_ID=$(vault write -field=secret_id -f auth/approle/role/${CLIENT_NAME}-role/secret-id)
    
    echo "Client: $CLIENT_NAME"
    echo "Role ID: $ROLE_ID"
    echo "Secret ID: $SECRET_ID"
    echo "---"
    
    # Store credentials securely for client deployment
    mkdir -p /secure/clients/$CLIENT_NAME
    echo "$ROLE_ID" > /secure/clients/$CLIENT_NAME/role-id
    echo "$SECRET_ID" > /secure/clients/$CLIENT_NAME/secret-id
    chmod 600 /secure/clients/$CLIENT_NAME/*
}

# Setup auth for multiple clients
setup_client_auth "client1"
setup_client_auth "client2"
```

---

## Client Configuration Templates

### 1. Client-Specific appsettings.json

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=YendorCats;User=root;Password=password;Port=3306;",
    "ProductionConnection": "Server={{CLIENT_NAME}}-db;Database=YendorCats;User=${MYSQL_USER};Password=${MYSQL_PASSWORD};Port=3306;"
  },
  "Vault": {
    "Address": "https://vault.yourdomain.com:8200",
    "RoleId": "{{ROLE_ID}}",
    "SecretId": "{{SECRET_ID}}",
    "SecretPath": "secret/{{CLIENT_NAME}}/yendorcats/app-secrets"
  },
  "AWS": {
    "Region": "us-west-004",
    "UseCredentialsFromSecrets": true,
    "S3": {
      "BucketName": "{{CLIENT_NAME}}-yendorcats-images",
      "UseDirectS3Urls": true,
      "ServiceUrl": "https://s3.us-west-004.backblazeb2.com",
      "PublicUrl": "https://f004.backblazeb2.com/file/{{CLIENT_NAME}}-yendorcats-images/{key}",
      "UseCdn": false
    }
  },
  "JwtSettings": {
    "Secret": "fallback-secret-for-development",
    "Issuer": "YendorCatsApi-{{CLIENT_NAME}}",
    "Audience": "YendorCatsClients-{{CLIENT_NAME}}",
    "ExpiryMinutes": 60,
    "RefreshExpiryDays": 7
  }
}
```

### 2. Client Deployment Script

```bash
#!/bin/bash
# deploy-client.sh

CLIENT_NAME="$1"
DEPLOYMENT_PATH="$2"

if [ -z "$CLIENT_NAME" ] || [ -z "$DEPLOYMENT_PATH" ]; then
    echo "Usage: $0 <client-name> <deployment-path>"
    exit 1
fi

echo "Deploying YendorCats for client: $CLIENT_NAME"

# Read client credentials
ROLE_ID=$(cat /secure/clients/$CLIENT_NAME/role-id)
SECRET_ID=$(cat /secure/clients/$CLIENT_NAME/secret-id)

# Create client-specific configuration
sed -e "s/{{CLIENT_NAME}}/$CLIENT_NAME/g" \
    -e "s/{{ROLE_ID}}/$ROLE_ID/g" \
    -e "s/{{SECRET_ID}}/$SECRET_ID/g" \
    templates/appsettings.production.json > $DEPLOYMENT_PATH/appsettings.json

# Create client-specific docker-compose
sed -e "s/{{CLIENT_NAME}}/$CLIENT_NAME/g" \
    templates/docker-compose.client.yml > $DEPLOYMENT_PATH/docker-compose.yml

# Set environment variables
cat > $DEPLOYMENT_PATH/.env << EOF
CLIENT_NAME=$CLIENT_NAME
VAULT_ROLE_ID=$ROLE_ID
VAULT_SECRET_ID=$SECRET_ID
MYSQL_USER=${CLIENT_NAME}_user
MYSQL_PASSWORD=$(openssl rand -base64 32)
MYSQL_ROOT_PASSWORD=$(openssl rand -base64 32)
EOF

echo "Deployment files created for $CLIENT_NAME"
```

---

## Client Management Scripts

### 1. List All Clients

```bash
#!/bin/bash
# list-clients.sh

echo "Active YendorCats Clients:"
echo "========================="

# List all client directories
vault kv list secret/ | grep -E '^client[0-9]+/$' | while read client_dir; do
    CLIENT_NAME=${client_dir%/}
    
    # Get last updated time
    METADATA=$(vault kv metadata secret/$CLIENT_NAME/yendorcats/app-secrets 2>/dev/null)
    if [ $? -eq 0 ]; then
        LAST_UPDATED=$(echo "$METADATA" | grep "updated_time" | awk '{print $2}')
        echo "✓ $CLIENT_NAME (Last updated: $LAST_UPDATED)"
    else
        echo "✗ $CLIENT_NAME (No secrets found)"
    fi
done
```

### 2. Client Health Check

```bash
#!/bin/bash
# client-health-check.sh

CLIENT_NAME="$1"

if [ -z "$CLIENT_NAME" ]; then
    echo "Usage: $0 <client-name>"
    exit 1
fi

echo "Health check for client: $CLIENT_NAME"
echo "===================================="

# Check if secrets exist
if vault kv get secret/$CLIENT_NAME/yendorcats/app-secrets >/dev/null 2>&1; then
    echo "✓ Secrets exist"
else
    echo "✗ Secrets missing"
    exit 1
fi

# Check if policy exists
if vault policy read ${CLIENT_NAME}-policy >/dev/null 2>&1; then
    echo "✓ Policy exists"
else
    echo "✗ Policy missing"
fi

# Check if AppRole exists
if vault read auth/approle/role/${CLIENT_NAME}-role >/dev/null 2>&1; then
    echo "✓ AppRole exists"
else
    echo "✗ AppRole missing"
fi

# Test authentication
ROLE_ID=$(cat /secure/clients/$CLIENT_NAME/role-id 2>/dev/null)
SECRET_ID=$(cat /secure/clients/$CLIENT_NAME/secret-id 2>/dev/null)

if [ -n "$ROLE_ID" ] && [ -n "$SECRET_ID" ]; then
    TOKEN=$(vault write -field=token auth/approle/login role_id="$ROLE_ID" secret_id="$SECRET_ID" 2>/dev/null)
    if [ -n "$TOKEN" ]; then
        echo "✓ Authentication successful"
        
        # Test secret access
        VAULT_TOKEN=$TOKEN vault kv get secret/$CLIENT_NAME/yendorcats/app-secrets >/dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "✓ Secret access successful"
        else
            echo "✗ Secret access failed"
        fi
    else
        echo "✗ Authentication failed"
    fi
else
    echo "✗ Client credentials missing"
fi
```

### 3. Bulk Client Operations

```bash
#!/bin/bash
# bulk-client-operations.sh

OPERATION="$1"

case $OPERATION in
    "rotate-jwt")
        echo "Rotating JWT secrets for all clients..."
        vault kv list secret/ | grep -E '^client[0-9]+/$' | while read client_dir; do
            CLIENT_NAME=${client_dir%/}
            NEW_JWT_SECRET=$(openssl rand -base64 64)
            vault kv patch secret/$CLIENT_NAME/yendorcats/app-secrets JwtSecret="$NEW_JWT_SECRET"
            echo "✓ Rotated JWT secret for $CLIENT_NAME"
        done
        ;;
    
    "update-shared")
        echo "Updating shared configurations..."
        vault kv put secret/shared/common-configs \
            DefaultJwtExpiryMinutes=60 \
            DefaultRefreshExpiryDays=7 \
            SupportEmail="<EMAIL>" \
            ApiVersion="v1.0"
        echo "✓ Updated shared configurations"
        ;;
    
    "backup-all")
        echo "Creating backup of all client secrets..."
        BACKUP_DIR="/secure/backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p $BACKUP_DIR
        
        vault kv list secret/ | grep -E '^client[0-9]+/$' | while read client_dir; do
            CLIENT_NAME=${client_dir%/}
            vault kv get -format=json secret/$CLIENT_NAME/yendorcats/app-secrets > $BACKUP_DIR/$CLIENT_NAME.json
            echo "✓ Backed up secrets for $CLIENT_NAME"
        done
        ;;
    
    *)
        echo "Usage: $0 {rotate-jwt|update-shared|backup-all}"
        exit 1
        ;;
esac
```

---

## Client-Specific Deployment

### 1. Docker Compose Template

```yaml
# templates/docker-compose.client.yml
version: '3.8'

services:
  yendorcats-api-{{CLIENT_NAME}}:
    image: yendorcats-api:latest
    container_name: yendorcats-api-{{CLIENT_NAME}}
    ports:
      - "{{CLIENT_PORT}}:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - CLIENT_NAME={{CLIENT_NAME}}
      - VAULT_ROLE_ID=${VAULT_ROLE_ID}
      - VAULT_SECRET_ID=${VAULT_SECRET_ID}
    volumes:
      - ./appsettings.json:/app/appsettings.json:ro
      - ./logs:/app/Logs
    depends_on:
      - mysql-{{CLIENT_NAME}}
    networks:
      - {{CLIENT_NAME}}-network

  mysql-{{CLIENT_NAME}}:
    image: mysql:8.0
    container_name: mysql-{{CLIENT_NAME}}
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=YendorCats
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - mysql-{{CLIENT_NAME}}-data:/var/lib/mysql
    networks:
      - {{CLIENT_NAME}}-network

volumes:
  mysql-{{CLIENT_NAME}}-data:

networks:
  {{CLIENT_NAME}}-network:
    driver: bridge
```

### 2. Nginx Configuration for Multiple Clients

```nginx
# /etc/nginx/sites-available/yendorcats-clients
server {
    listen 80;
    server_name client1.yendorcats.com;
    
    location / {
        proxy_pass http://localhost:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

server {
    listen 80;
    server_name client2.yendorcats.com;
    
    location / {
        proxy_pass http://localhost:5002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## Monitoring and Maintenance

### 1. Client Usage Monitoring

```bash
#!/bin/bash
# monitor-client-usage.sh

echo "Client Usage Report - $(date)"
echo "=============================="

vault kv list secret/ | grep -E '^client[0-9]+/$' | while read client_dir; do
    CLIENT_NAME=${client_dir%/}
    
    # Get secret access count from audit logs
    ACCESS_COUNT=$(grep "secret/$CLIENT_NAME" /vault/logs/audit.log | grep -c "read")
    
    # Get last access time
    LAST_ACCESS=$(grep "secret/$CLIENT_NAME" /vault/logs/audit.log | tail -1 | jq -r '.time')
    
    echo "$CLIENT_NAME: $ACCESS_COUNT accesses, last: $LAST_ACCESS"
done
```

### 2. Automated Client Cleanup

```bash
#!/bin/bash
# cleanup-inactive-clients.sh

INACTIVE_DAYS=90

echo "Checking for inactive clients (>$INACTIVE_DAYS days)..."

vault kv list secret/ | grep -E '^client[0-9]+/$' | while read client_dir; do
    CLIENT_NAME=${client_dir%/}
    
    # Check last access from audit logs
    LAST_ACCESS=$(grep "secret/$CLIENT_NAME" /vault/logs/audit.log | tail -1 | jq -r '.time')
    
    if [ -n "$LAST_ACCESS" ]; then
        LAST_ACCESS_EPOCH=$(date -d "$LAST_ACCESS" +%s)
        CURRENT_EPOCH=$(date +%s)
        DAYS_INACTIVE=$(( (CURRENT_EPOCH - LAST_ACCESS_EPOCH) / 86400 ))
        
        if [ $DAYS_INACTIVE -gt $INACTIVE_DAYS ]; then
            echo "WARNING: $CLIENT_NAME inactive for $DAYS_INACTIVE days"
            # Optionally disable or archive the client
        fi
    fi
done
```

---

## Cost Optimization

### Resource Sharing Strategies

- **Single Vault Instance**: All clients share one Vault server
- **Shared Database Server**: Multiple client databases on one MySQL instance
- **Bulk S3 Storage**: Negotiate better rates with higher volume
- **Automated Scaling**: Scale resources based on actual usage

### Billing and Tracking

```bash
# Track resource usage per client for billing
#!/bin/bash
# generate-client-billing.sh

MONTH="$1"
YEAR="$2"

echo "Client Billing Report - $MONTH/$YEAR"
echo "=================================="

for client in client1 client2 client3; do
    # Calculate storage usage
    STORAGE_MB=$(du -sm /var/lib/mysql/${client}_db | cut -f1)
    
    # Calculate API calls from logs
    API_CALLS=$(grep "$client" /var/log/nginx/access.log | grep "$MONTH/$YEAR" | wc -l)
    
    # Calculate costs
    STORAGE_COST=$(echo "$STORAGE_MB * 0.01" | bc)
    API_COST=$(echo "$API_CALLS * 0.001" | bc)
    TOTAL_COST=$(echo "$STORAGE_COST + $API_COST" | bc)
    
    echo "$client: Storage: ${STORAGE_MB}MB (\$${STORAGE_COST}), API: ${API_CALLS} calls (\$${API_COST}), Total: \$${TOTAL_COST}"
done
```

---
