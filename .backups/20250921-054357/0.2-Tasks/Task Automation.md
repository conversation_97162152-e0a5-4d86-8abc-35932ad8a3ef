---
creation_date: 2025-07-14
modification_date: 2025-07-14
type: automation
status: active
priority: high
area_category: Administration
owner: Jordan
tags:
  - tasks
  - automation
  - scripts
  - workflow
related_areas:
  - "[[Task Management]]"
  - "[[Administration]]"
related_resources:
  - "[[Vault Organization System Guide]]"
  - "[[Automation Scripts]]"
related_people:
  - "[[Jordan]]"
task_priority: high
task_context: admin
TQ_explain: 
TQ_extra_instructions: 
TQ_short_mode: 
TQ_show_backlink: 
TQ_show_cancelled_date: 
TQ_show_created_date: 
TQ_show_depends_on: 
TQ_show_done_date: 
TQ_show_due_date: 
TQ_show_edit_button: 
TQ_show_id: 
TQ_show_on_completion: 
TQ_show_postpone_button: 
TQ_show_priority: 
TQ_show_recurrence_rule: 
TQ_show_scheduled_date: 
TQ_show_start_date: 
TQ_show_tags: 
TQ_show_task_count: 
TQ_show_tree: 
TQ_show_urgency: 
---

# ⚙️ Task Automation - Scripts & Workflows

> **Automated systems for task management, carryover, and maintenance**

---

## 🤖 Automation Overview

The task automation system provides:
1. **Daily task carryover** from daily notes to task system
2. **Automatic categorization** based on content analysis
3. **Link generation** to related vault content
4. **Duplicate prevention** and cleanup
5. **Archive management** for completed tasks

---

## 🔄 Daily Task Sync Automation

### Manual Sync Process
Since Obsidian doesn't support full automation, here's the **manual process** to sync tasks from daily notes:

#### Step 1: Identify Unfinished Tasks
```dataview
TABLE WITHOUT ID
  file.link as "Daily Note",
  date as "Date",
  length(filter(file.tasks, (t) => !t.completed)) as "Open Tasks",
  join(map(filter(file.tasks, (t) => !t.completed), (t) => t.text), " | ") as "Task Preview"
FROM "0-Daily Notes"
WHERE file.tasks
  AND length(filter(file.tasks, (t) => !t.completed)) > 0
  AND date >= date(today) - dur(30 days)
SORT date DESC
LIMIT 15
```

#### Step 2: Task Migration Checklist
For each unfinished task found above:

1. **📋 Copy Task Content**
   - Copy the task text from the daily note
   - Note the source date and daily note name
   - Identify any priority indicators or context tags

2. **🏷️ Analyze & Categorize**
   - **Priority**: Look for [URGENT], [HIGH], [MEDIUM], [ROUTINE] indicators
   - **Context**: Identify #deep-work, #admin, #communication tags
   - **Project Links**: Find project names or #project-tags
   - **Due Date**: Extract any date mentions

3. **📝 Create Task File**
   - Use naming convention: `YYYY-MM-DD-###-TaskName.md`
   - Place in `0.2-Tasks/Active/` folder
   - Use the Enhanced Task template

4. **🔗 Add Relationships**
   - Link to source daily note
   - Connect to related projects/areas
   - Add relevant resource links

5. **✅ Verify & Update**
   - Check task appears in dashboard
   - Verify all metadata is correct
   - Update task indices if needed

---

## 📋 Task Creation Templates

### Quick Task Template (Copy & Paste)
```markdown
---
# CORE METADATA
creation_date: 2025-07-14
modification_date: 2025-07-14
type: task
status: not-started
priority: medium
area_category: [Choose: Software-Development|Administration|Personal|Church|Education|Finance]
owner: Jordan
tags: [task, [add-context-tags]]

# TASK MANAGEMENT
task_priority: [urgent|high|medium|routine|someday]
task_context: [deep-work|admin|communication|maintenance|creative]
estimated_time: [15min|30min|1hr|2hr|4hr|8hr]
energy_required: [high|medium|low]
due_date: [YYYY-MM-DD or leave blank]

# RELATIONSHIPS (Enhanced System)
related_projects: [["[[Project Name]]"]]
related_areas: [["[[Area Name]]"]]
related_resources: []
related_people: [["[[Jordan]]"]]
source_note: [["[[Daily Note]]"]] # If migrated from daily note
source_date: [YYYY-MM-DD] # If migrated from daily note

# TASK LIFECYCLE
created_date: 2025-07-14
completed: false
---

# [Task Title]

## 📋 Task Description
[What needs to be done? Be specific and actionable]

## ✅ Success Criteria
- [ ] [Specific outcome 1]
- [ ] [Specific outcome 2]
- [ ] [Specific outcome 3]

## 🎯 Context & Motivation
[Why is this important? What's the bigger picture?]

## 📚 Resources Needed
- [Resource 1]
- [Resource 2]

## 🔄 Subtasks
- [ ] [Subtask 1]
- [ ] [Subtask 2]
- [ ] [Subtask 3]

## 🔗 Related Vault Content
- **Project**: [[Related Project]]
- **Area**: [[Related Area]]
- **Resources**: [[Helpful Resource]]
- **Source**: [[Source Daily Note]] (if applicable)

---
**Navigation**: [[0.2-Tasks/Active/Active Tasks Index|📂 Active Tasks]] | [[0.2-Tasks/Tasks Dashboard|🎯 Dashboard]]
```

---

## 🏷️ Priority & Context Assignment Rules

### Automatic Priority Detection
**Keywords that trigger priority levels:**

#### 🔥 URGENT Priority
- "urgent", "ASAP", "emergency", "critical", "immediately"
- "deadline today", "due now", "blocking"
- "[URGENT]", "[🔥]", "URGENT:"

#### ⚡ HIGH Priority  
- "important", "high priority", "milestone", "deadline"
- Project names (YendorCats, Church, etc.)
- "[HIGH]", "[⚡]", "HIGH:"
- "this week", "by Friday"

#### 📋 MEDIUM Priority
- Regular task language without special indicators
- "[MEDIUM]", "[📋]", "MEDIUM:"
- "when possible", "next week"

#### 🔄 ROUTINE Priority
- "routine", "maintenance", "cleanup", "organize"
- "[ROUTINE]", "[🔄]", "ROUTINE:"
- "weekly", "monthly", "regular"

### Context Detection Rules
**Hashtags and keywords that assign context:**

#### #deep-work
- "research", "design", "write", "develop", "analyze"
- "focus", "concentration", "thinking"
- Complex technical tasks

#### #admin
- "email", "paperwork", "forms", "documentation"
- "organize", "file", "update records"
- Administrative language

#### #communication
- "call", "email", "meeting", "respond", "contact"
- "follow up", "reach out", "discuss"

#### #maintenance
- "backup", "update", "clean", "organize", "fix"
- "maintain", "check", "verify", "test"

---

## 🔗 Automatic Linking Rules

### Project Detection
**Keywords that link to projects:**
- "YendorCats" → [[YendorCats Project Documentation]]
- "Church" → [[Church Administration]]
- "Website" → [[Website-Maintenance]]
- "Vault" → [[HashiCorp-Vault]]

### Area Detection
**Context that links to areas:**
- Admin tasks → [[Administration]]
- Technical tasks → [[Software-Development]]
- Church tasks → [[Church Administration]]
- Personal tasks → [[Personal]]

### Resource Linking
**Automatic resource suggestions:**
- Cloudflare tasks → [[Cloudflare Documentation]]
- Git tasks → [[Git Command Cheatsheet]]
- Docker tasks → [[Docker Configuration Files]]
- Documentation tasks → [[Documentation Best Practices]]

---

## 🧹 Maintenance Automation

### Weekly Cleanup Tasks
1. **Archive Completed Tasks**
   - Move completed tasks to `0.2-Tasks/Completed/`
   - Update completion statistics
   - Clean up broken links

2. **Update Task Priorities**
   - Review overdue tasks
   - Escalate important items
   - Demote stale tasks

3. **Link Validation**
   - Check for broken project/area links
   - Update changed note names
   - Verify resource links still work

### Monthly Maintenance
1. **Archive Old Completed Tasks**
   - Move tasks older than 30 days to archive
   - Generate completion reports
   - Clean up metadata

2. **System Health Check**
   - Verify all automation is working
   - Check for duplicate tasks
   - Update templates if needed

---

## 📊 Automation Reports

### Daily Sync Report Template
```markdown
# Daily Task Sync Report - [DATE]

## 📊 Summary
- **Daily Notes Scanned**: [NUMBER]
- **Unfinished Tasks Found**: [NUMBER]
- **Tasks Migrated**: [NUMBER]
- **Duplicates Prevented**: [NUMBER]

## 📋 Migrated Tasks
1. [Task Name] - Priority: [LEVEL] - Source: [DAILY NOTE]
2. [Task Name] - Priority: [LEVEL] - Source: [DAILY NOTE]

## ⚠️ Issues Found
- [Any problems or manual intervention needed]

## 🔗 Links Created
- [Automatic links generated during migration]

---
Generated: [TIMESTAMP]
```

---

## 🚀 Quick Actions

### Manual Sync Actions
- [[0.2-Tasks/Automation/Run Daily Sync|🔄 Execute Daily Sync]]
- [[0.2-Tasks/Automation/Archive Completed|📚 Archive Completed Tasks]]
- [[0.2-Tasks/Automation/Update Links|🔗 Update Broken Links]]

### Maintenance Actions
- [[0.2-Tasks/Automation/Cleanup Duplicates|🧹 Remove Duplicates]]
- [[0.2-Tasks/Automation/Validate Metadata|✅ Validate Task Metadata]]
- [[0.2-Tasks/Automation/Generate Report|📊 Generate Status Report]]

---

**Navigation**: [[0.2-Tasks/Tasks TOC|📋 Tasks TOC]] | [[0.2-Tasks/Tasks Dashboard|🎯 Dashboard]] | [[Home|🏠 Home]]
