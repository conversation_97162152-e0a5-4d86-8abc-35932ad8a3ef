---
# CORE METADATA
creation_date: 2025-07-14
modification_date: 2025-07-14
type: guide
status: active
priority: high
area_category: Administration
owner: Jordan
tags: [tasks, linking, guide, connectivity, vault-organization]

# RELATIONSHIPS (Enhanced System)
related_areas: ["[[Task Management]]", "[[Administration]]"]
related_resources: ["[[Vault Organization System Guide]]", "[[Task Management System]]"]
related_people: ["[[Jordan]]"]

# TASK MANAGEMENT
task_priority: high
task_context: admin
---

# 🔗 Task Linking Guide - Vault Connectivity

> **Complete guide for linking tasks to vault resources** - Create a highly connected knowledge web.

---

## 🎯 Linking Philosophy

The task linking system creates **bidirectional connections** between tasks and all relevant vault content, enabling:
- **Context discovery** - Find related information quickly
- **Knowledge reuse** - Leverage existing resources for task completion
- **Progress tracking** - See how tasks relate to larger goals
- **Learning capture** - Connect task outcomes to knowledge base

---

## 📋 Standard Linking Format

### Core Relationship Fields
Every task should include these relationship fields:

```yaml
# RELATIONSHIPS (Enhanced System)
related_projects: [["[[Project Name 1]]", "[[Project Name 2]]"]]
related_areas: [["[[Area Name 1]]", "[[Area Name 2]]"]]
related_resources: [["[[Resource Name 1]]", "[[Resource Name 2]]"]]
related_people: [["[[Person Name 1]]", "[[Person Name 2]]"]]

# TASK-SPECIFIC RELATIONSHIPS
source_note: "[[Daily Note Name]]" # If migrated from daily note
dependencies: [["[[Blocking Task 1]]", "[[Required Resource 1]]"]]
blocks: [["[[Dependent Task 1]]", "[[Dependent Task 2]]"]]
references: [["[[Documentation 1]]", "[[Guide 1]]"]]
```

### Content Linking Section
Include this section in every task:

```markdown
## 🔗 Related Vault Content

### Related Projects
- **[[Project Name]]** - How this task contributes to the project

### Related Areas
- **[[Area Name]]** - Which area of responsibility this falls under

### Related Resources
- **[[Resource Name]]** - Helpful guides, documentation, or tools
- **[[Another Resource]]** - Additional reference material

### Related People
- **[[Person Name]]** - Who is involved or responsible

### Dependencies
- **[[Blocking Item]]** - What must be completed first
- **[[Required Resource]]** - What resources are needed

### Source Information
- **Original Note**: [[Source Daily Note]] (if applicable)
- **Related Tasks**: [[Similar Task 1]], [[Similar Task 2]]
```

---

## 🏷️ Linking Rules by Task Type

### 🔥 Urgent Tasks
**Must link to:**
- **Area**: Which responsibility area is affected
- **People**: Who needs to be notified/involved
- **Dependencies**: What's blocking resolution

**Example:**
```yaml
related_areas: [["[[Administration]]"]]
related_people: [["[[Jordan]]", "[[Support Team]]"]]
dependencies: [["[[Server Access]]", "[[Backup Verification]]"]]
```

### 🚀 Project Tasks
**Must link to:**
- **Project**: The specific project this advances
- **Resources**: Relevant documentation/guides
- **Dependencies**: Other project tasks or resources

**Example:**
```yaml
related_projects: [["[[YendorCats Project Documentation]]"]]
related_areas: [["[[Software-Development]]"]]
related_resources: [["[[YendorCats Deployment Guide]]", "[[Docker Configuration Files]]"]]
dependencies: [["[[Database Setup]]", "[[S3 Configuration]]"]]
```

### 📋 Administrative Tasks
**Must link to:**
- **Area**: Administrative area (Church, Personal, etc.)
- **Resources**: Procedures, templates, or guides
- **People**: Who is affected or involved

**Example:**
```yaml
related_areas: [["[[Church Administration]]"]]
related_resources: [["[[Hall Hire Procedures]]", "[[Email Templates]]"]]
related_people: [["[[Jordan]]", "[[Church Committee]]"]]
```

### 🔄 Routine Tasks
**Must link to:**
- **Area**: Area requiring maintenance
- **Resources**: Checklists, procedures, or tools
- **Frequency**: Related recurring task patterns

**Example:**
```yaml
related_areas: [["[[Website-Maintenance]]"]]
related_resources: [["[[Backup Procedures]]", "[[Maintenance Checklist]]"]]
references: [["[[Monthly Maintenance Schedule]]"]]
```

---

## 🔍 Auto-Linking Patterns

### Project Detection Keywords
**Automatic project linking based on content:**

| Keyword | Links to Project |
|---------|------------------|
| "YendorCats", "cat gallery", "S3 metadata" | [[YendorCats Project Documentation]] |
| "Church", "hall hire", "administration" | [[Church Administration]] |
| "Website", "domain", "hosting" | [[Website-Maintenance]] |
| "Vault", "secrets", "HashiCorp" | [[HashiCorp-Vault]] |
| "Network", "ZeroTier", "VPN" | [[Network Architecture]] |

### Area Detection Patterns
**Automatic area linking based on context:**

| Context | Links to Area |
|---------|---------------|
| Admin tasks, paperwork, forms | [[Administration]] |
| Code, development, technical | [[Software-Development]] |
| Church, hall, pastoral | [[Church Administration]] |
| Personal, family, home | [[Personal]] |
| Learning, courses, research | [[Education]] |
| Money, budget, payments | [[Finances]] |

### Resource Linking Rules
**Automatic resource suggestions:**

| Task Content | Suggested Resources |
|--------------|-------------------|
| "Cloudflare", "DNS", "domain" | [[Cloudflare Documentation]] |
| "Git", "commit", "repository" | [[Git Command Cheatsheet]] |
| "Docker", "container", "deployment" | [[Docker Configuration Files]] |
| "Backup", "rclone", "sync" | [[Backup Procedures]] |
| "Documentation", "writing", "guide" | [[Documentation Best Practices]] |

---

## 📊 Linking Quality Checklist

### ✅ High-Quality Task Links
- [ ] **Project Connection**: Clearly linked to relevant project(s)
- [ ] **Area Assignment**: Properly categorized by responsibility area
- [ ] **Resource References**: Includes helpful guides/documentation
- [ ] **People Involvement**: Identifies who is responsible/affected
- [ ] **Dependency Mapping**: Shows what's needed to complete task
- [ ] **Source Tracking**: Links back to origin (daily note, meeting, etc.)
- [ ] **Context Tags**: Includes relevant hashtags for discovery

### ❌ Poor Linking Examples
- **Vague connections**: "Related to website stuff"
- **Missing dependencies**: No indication of what's needed first
- **No resource links**: Missing helpful documentation
- **Orphaned tasks**: No connection to projects or areas
- **Missing people**: No indication of who's involved

---

## 🔄 Bidirectional Linking Strategy

### From Tasks to Vault Content
Tasks should link **TO** relevant vault content:
```markdown
## 🔗 Related Vault Content
- **Project**: [[YendorCats Project Documentation]]
- **Area**: [[Software-Development]]
- **Resource**: [[S3 Configuration Guide]]
```

### From Vault Content to Tasks
Vault content should link **BACK** to relevant tasks:

#### In Project Notes:
```dataview
TABLE WITHOUT ID
  file.link as "Related Task",
  task_priority as "Priority",
  due_date as "Due"
FROM "0.2-Tasks/Active"
WHERE contains(related_projects, this.file.name)
SORT task_priority ASC, due_date ASC
```

#### In Area Notes:
```dataview
TABLE WITHOUT ID
  file.link as "Area Task",
  task_priority as "Priority",
  estimated_time as "Time"
FROM "0.2-Tasks/Active"
WHERE contains(related_areas, this.file.name)
SORT task_priority ASC
```

#### In Resource Notes:
```dataview
LIST
FROM "0.2-Tasks/Active"
WHERE contains(related_resources, this.file.name)
SORT task_priority ASC
```

---

## 📝 Daily Note Task Format

### Recommended Format for Daily Notes
When adding tasks to daily notes, use this format for optimal linking:

```markdown
## Tasks for Today
- [ ] [HIGH] Update YendorCats documentation #deep-work #yendorcats
- [ ] [URGENT] Fix server backup issue #admin #maintenance #server
- [ ] [MEDIUM] Review church hall bookings #admin #church #communication
- [ ] [ROUTINE] Clean email inbox #admin #communication #routine
```

### Priority Indicators
- **[URGENT]** or **[🔥]** - Critical, immediate action required
- **[HIGH]** or **[⚡]** - Important for project/area success
- **[MEDIUM]** or **[📋]** - Regular planned work
- **[ROUTINE]** or **[🔄]** - Maintenance, can be batched
- **[SOMEDAY]** or **[💡]** - Ideas, no immediate deadline

### Context Tags
- **#deep-work** - Requires focused concentration
- **#admin** - Administrative/bureaucratic tasks
- **#communication** - Emails, calls, meetings
- **#maintenance** - System upkeep, routine checks
- **#creative** - Design, writing, brainstorming

### Project/Area Tags
- **#yendorcats** - YendorCats project tasks
- **#church** - Church administration tasks
- **#personal** - Personal tasks
- **#website** - Website maintenance tasks
- **#network** - Network/infrastructure tasks

---

## 🚀 Advanced Linking Techniques

### Task Chains
Link related tasks in sequence:
```yaml
dependencies: [["[[Previous Task]]"]]
blocks: [["[[Next Task]]"]]
```

### Resource Collections
Group related resources:
```yaml
related_resources: [
  ["[[Primary Guide]]"],
  ["[[Reference Documentation]]"],
  ["[[Tool/Software]]"],
  ["[[Example/Template]]"]
]
```

### Multi-Project Tasks
Tasks that span multiple projects:
```yaml
related_projects: [
  ["[[Primary Project]]"],
  ["[[Secondary Project]]"]
]
```

### Cross-Area Tasks
Tasks affecting multiple areas:
```yaml
related_areas: [
  ["[[Primary Area]]"],
  ["[[Affected Area]]"]
]
```

---

## 📊 Linking Analytics

### Link Quality Metrics
```dataview
TABLE WITHOUT ID
  "📊 Linking Quality" as "Metric",
  length(filter(pages('"0.2-Tasks/Active"'), (p) => p.related_projects)) as "Tasks with Projects",
  length(filter(pages('"0.2-Tasks/Active"'), (p) => p.related_areas)) as "Tasks with Areas",
  length(filter(pages('"0.2-Tasks/Active"'), (p) => p.related_resources)) as "Tasks with Resources",
  length(pages('"0.2-Tasks/Active"')) as "Total Tasks"
FROM ""
WHERE file.name = "Task Linking Guide"
LIMIT 1
```

### Most Connected Tasks
```dataview
TABLE WITHOUT ID
  file.link as "Well-Connected Task",
  length(related_projects) + length(related_areas) + length(related_resources) as "Total Links",
  task_priority as "Priority"
FROM "0.2-Tasks/Active"
WHERE related_projects OR related_areas OR related_resources
SORT (length(related_projects) + length(related_areas) + length(related_resources)) DESC
LIMIT 10
```

---

## 🔧 Troubleshooting Links

### Common Linking Issues
1. **Broken Links**: Note names changed after linking
2. **Missing Bidirectional**: Links only go one way
3. **Over-linking**: Too many irrelevant connections
4. **Under-linking**: Missing obvious connections

### Link Maintenance
- **Weekly**: Check for broken links in active tasks
- **Monthly**: Verify bidirectional connections work
- **Quarterly**: Review and optimize linking patterns

---

**Navigation**: [[0.2-Tasks/Tasks TOC|📋 Tasks TOC]] | [[0.2-Tasks/Tasks Dashboard|🎯 Dashboard]] | [[Home|🏠 Home]]
