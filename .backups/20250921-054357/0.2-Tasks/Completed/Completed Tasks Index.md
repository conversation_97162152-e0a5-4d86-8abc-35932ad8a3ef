---
# CORE METADATA
creation_date: 2025-07-14
modification_date: 2025-07-14
type: index
status: active
priority: medium
area_category: Administration
owner: Jordan
tags: [tasks, completed, archive, index]

# RELATIONSHIPS (Enhanced System)
related_areas: ["[[Task Management]]", "[[Administration]]"]
related_resources: ["[[Task Management System]]", "[[Productivity Analytics]]"]
related_people: ["[[Jordan]]"]

# TASK MANAGEMENT
task_priority: medium
task_context: admin
---

# ✅ Completed Tasks Index

> **Archive of completed tasks with analytics and insights** - Learn from your productivity patterns.

---

## 📊 Completion Statistics

### This Week's Achievements
```dataview
TABLE WITHOUT ID
  "📊 This Week" as "Period",
  length(filter(rows, (r) => r.completed_date >= date(today) - dur(7 days))) as "Completed",
  length(filter(rows, (r) => r.task_priority = "urgent" AND r.completed_date >= date(today) - dur(7 days))) as "🔥 Urgent",
  length(filter(rows, (r) => r.task_priority = "high" AND r.completed_date >= date(today) - dur(7 days))) as "⚡ High",
  round(average(filter(rows, (r) => r.actual_time AND r.completed_date >= date(today) - dur(7 days)).actual_time), 1) + " hrs" as "Avg Time"
FROM "0.2-Tasks/Completed"
WHERE type = "task" AND completed_date
GROUP BY "week"
```

### This Month's Progress
```dataview
TABLE WITHOUT ID
  "📊 This Month" as "Period",
  length(filter(rows, (r) => r.completed_date >= date(today) - dur(30 days))) as "Completed",
  length(filter(rows, (r) => r.task_priority = "high" AND r.completed_date >= date(today) - dur(30 days))) as "High Priority",
  round(length(filter(rows, (r) => r.completed_date >= date(today) - dur(30 days))) / 30, 1) as "Tasks/Day"
FROM "0.2-Tasks/Completed"
WHERE type = "task" AND completed_date
GROUP BY "month"
```

---

## 📅 RECENTLY COMPLETED TASKS

### Last 7 Days
```dataview
TABLE WITHOUT ID
  "✅" as "",
  file.link as "COMPLETED TASK",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", choice(task_priority = "medium", "📋", "🔄"))) as "P",
  completed_date as "Done",
  choice(actual_time, actual_time, estimated_time) as "Time",
  choice(source_note, source_note, "Direct") as "Source"
FROM "0.2-Tasks/Completed"
WHERE completed_date >= date(today) - dur(7 days)
SORT completed_date DESC
```

### Last 30 Days
```dataview
TABLE WITHOUT ID
  "✅" as "",
  file.link as "COMPLETED TASK",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", choice(task_priority = "medium", "📋", "🔄"))) as "P",
  completed_date as "Done",
  choice(actual_time, actual_time, estimated_time) as "Time",
  task_context as "Context"
FROM "0.2-Tasks/Completed"
WHERE completed_date >= date(today) - dur(30 days)
  AND completed_date < date(today) - dur(7 days)
SORT completed_date DESC
LIMIT 20
```

---

## 📊 COMPLETION ANALYTICS

### Tasks by Priority Level
```dataview
TABLE WITHOUT ID
  task_priority as "Priority Level",
  length(rows) as "Total Completed",
  round(average(rows.actual_time), 1) + " hrs" as "Avg Time",
  round(length(rows) / length(group) * 100, 1) + "%" as "% of Total"
FROM "0.2-Tasks/Completed"
WHERE type = "task" AND completed_date >= date(today) - dur(30 days)
GROUP BY task_priority
SORT length(rows) DESC
```

### Tasks by Context
```dataview
TABLE WITHOUT ID
  task_context as "Context",
  length(rows) as "Completed",
  round(average(rows.actual_time), 1) + " hrs" as "Avg Time",
  round(length(filter(rows, (r) => r.completed_date >= r.due_date OR !r.due_date)) / length(rows) * 100, 1) + "%" as "On Time %"
FROM "0.2-Tasks/Completed"
WHERE type = "task" AND completed_date >= date(today) - dur(30 days)
GROUP BY task_context
SORT length(rows) DESC
```

### Tasks by Source
```dataview
TABLE WITHOUT ID
  choice(source_note, "Daily Notes", "Direct Creation") as "Source Type",
  length(rows) as "Completed",
  round(average(rows.actual_time), 1) + " hrs" as "Avg Time",
  round(length(filter(rows, (r) => r.task_priority = "high" OR r.task_priority = "urgent")) / length(rows) * 100, 1) + "%" as "High Priority %"
FROM "0.2-Tasks/Completed"
WHERE type = "task" AND completed_date >= date(today) - dur(30 days)
GROUP BY choice(source_note, "Daily Notes", "Direct Creation")
SORT length(rows) DESC
```

---

## 🎯 PROJECT & AREA COMPLETION

### Tasks by Project
```dataview
TABLE WITHOUT ID
  choice(related_projects[0], "No Project", related_projects[0]) as "Project",
  length(rows) as "Completed",
  round(average(rows.actual_time), 1) + " hrs" as "Avg Time",
  max(rows.completed_date) as "Last Completed"
FROM "0.2-Tasks/Completed"
WHERE type = "task" 
  AND completed_date >= date(today) - dur(30 days)
  AND related_projects
GROUP BY choice(related_projects[0], "No Project", related_projects[0])
SORT length(rows) DESC
```

### Tasks by Area
```dataview
TABLE WITHOUT ID
  choice(related_areas[0], "No Area", related_areas[0]) as "Area",
  length(rows) as "Completed",
  round(average(rows.actual_time), 1) + " hrs" as "Avg Time",
  max(rows.completed_date) as "Last Completed"
FROM "0.2-Tasks/Completed"
WHERE type = "task" 
  AND completed_date >= date(today) - dur(30 days)
  AND related_areas
GROUP BY choice(related_areas[0], "No Area", related_areas[0])
SORT length(rows) DESC
```

---

## ⏰ TIME ANALYSIS

### Time Estimation Accuracy
```dataview
TABLE WITHOUT ID
  file.link as "Task",
  estimated_time as "Estimated",
  actual_time as "Actual",
  choice(actual_time > estimated_time, "⏰ Over", choice(actual_time < estimated_time, "⚡ Under", "✅ Accurate")) as "Accuracy",
  completed_date as "Done"
FROM "0.2-Tasks/Completed"
WHERE actual_time AND estimated_time
  AND completed_date >= date(today) - dur(14 days)
SORT completed_date DESC
LIMIT 15
```

### Most Time-Consuming Tasks
```dataview
TABLE WITHOUT ID
  file.link as "Task",
  actual_time as "Time Spent",
  task_priority as "Priority",
  task_context as "Context",
  completed_date as "Done"
FROM "0.2-Tasks/Completed"
WHERE actual_time
  AND completed_date >= date(today) - dur(30 days)
SORT actual_time DESC
LIMIT 10
```

---

## 🏆 PRODUCTIVITY INSIGHTS

### High-Impact Completions
```dataview
TABLE WITHOUT ID
  file.link as "🏆 HIGH-IMPACT TASK",
  choice(related_projects[0], "General", related_projects[0]) as "Project",
  actual_time as "Time",
  completed_date as "Done"
FROM "0.2-Tasks/Completed"
WHERE task_priority = "urgent" OR task_priority = "high"
  AND completed_date >= date(today) - dur(14 days)
SORT completed_date DESC
```

### Quick Wins Achieved
```dataview
TABLE WITHOUT ID
  file.link as "⚡ QUICK WIN",
  actual_time as "Time",
  task_context as "Context",
  completed_date as "Done"
FROM "0.2-Tasks/Completed"
WHERE estimated_time = "15min" OR actual_time <= 0.25
  AND completed_date >= date(today) - dur(14 days)
SORT completed_date DESC
```

---

## 📈 TRENDS & PATTERNS

### Daily Completion Patterns
```dataview
TABLE WITHOUT ID
  completed_date as "Date",
  length(rows) as "Tasks Completed",
  round(sum(rows.actual_time), 1) + " hrs" as "Total Time",
  round(average(rows.actual_time), 1) + " hrs" as "Avg Time/Task"
FROM "0.2-Tasks/Completed"
WHERE completed_date >= date(today) - dur(14 days)
GROUP BY completed_date
SORT completed_date DESC
```

### Weekly Productivity Trends
```dataview
TABLE WITHOUT ID
  dateformat(completed_date, "yyyy-[W]WW") as "Week",
  length(rows) as "Tasks",
  length(filter(rows, (r) => r.task_priority = "high" OR r.task_priority = "urgent")) as "High Priority",
  round(sum(rows.actual_time), 1) + " hrs" as "Total Time"
FROM "0.2-Tasks/Completed"
WHERE completed_date >= date(today) - dur(30 days)
GROUP BY dateformat(completed_date, "yyyy-[W]WW")
SORT dateformat(completed_date, "yyyy-[W]WW") DESC
```

---

## 🔄 TASK LIFECYCLE ANALYSIS

### Average Task Lifespan
```dataview
TABLE WITHOUT ID
  task_priority as "Priority",
  round(average(map(rows, (r) => (r.completed_date - r.created_date) / dur(1 day))), 1) + " days" as "Avg Lifespan",
  round(average(rows.actual_time), 1) + " hrs" as "Avg Time",
  length(rows) as "Sample Size"
FROM "0.2-Tasks/Completed"
WHERE completed_date >= date(today) - dur(30 days)
  AND created_date
GROUP BY task_priority
SORT task_priority ASC
```

---

## 🚀 Quick Actions

### Archive Management
- [[0.2-Tasks/Automation/Archive Old Tasks|📚 Archive Old Completed Tasks]]
- [[0.2-Tasks/Analytics/Generate Report|📊 Generate Productivity Report]]
- [[0.2-Tasks/Analytics/Export Data|📤 Export Completion Data]]

### Analysis Tools
- [[0.2-Tasks/Analytics/Time Analysis|⏰ Detailed Time Analysis]]
- [[0.2-Tasks/Analytics/Project Performance|🚀 Project Performance Review]]
- [[0.2-Tasks/Analytics/Productivity Trends|📈 Productivity Trends]]

### Navigation
- [[0.2-Tasks/Tasks Dashboard|🎯 Tasks Dashboard]]
- [[0.2-Tasks/Active/Active Tasks Index|📂 Active Tasks]]
- [[0.2-Tasks/Tasks TOC|📋 Tasks TOC]]

---

**Last Updated**: 2025-07-14 | **Total Completed Tasks**: `$= dv.pages('"0.2-Tasks/Completed"').where(p => p.type === "task" && p.completed).length`

**Navigation**: [[0.2-Tasks/Tasks TOC|📋 Tasks TOC]] | [[0.2-Tasks/Tasks Dashboard|🎯 Dashboard]] | [[Home|🏠 Home]]
