---
id: agents-obsidian-handbook
title: "AGENTS.md — Obsidian Vault Agent Handbook"
description: "Comprehensive guide for AI agents on formatting markdown documents in Obsidian vault with proper metadata, categorization, and structure for optimal Dataview and search functionality."
author: "Jordan Pacey"
version: "1.0.0"
created: "2025-09-07"
updated: "2025-09-07"
status: "active"
type: "handbook/guide"
area: "documentation/obsidian"
agents_supported: ["Cline", "Roo Code", "Augment Code", "Cursor"]
requires_metadata: true
visual_aids: true
diagram_formats: ["Mermaid", "SVG", "PNG"]
task_management: true
obsidian:
  dataview: true
  tasks_plugin: true
  mermaid: true
  templater: true
  para_method: true
tags:
  - agents
  - obsidian
  - metadata
  - documentation
  - dataview
  - para
  - vault-structure
  - markdown
  - categorization
  - search
  - knowledge-management
aliases: ["AGEN<PERSON>", "Agent Handbook", "Obsidian Agent Guide"]
---

# AGENTS.md — Obsidian Vault Agent Handbook

---

## Purpose

This handbook provides comprehensive instructions for AI agents on how to format markdown documents in this Obsidian vault. The goal is to ensure consistent, searchable, and well-categorized content that maximizes the effectiveness of Dataview queries and vault search functionality.

## Core Principles

### 1. Metadata is Critical
- **Every document MUST have comprehensive frontmatter metadata**
- Metadata powers Dataview queries, search, and vault organization
- Missing or incomplete metadata severely impacts vault functionality
- Use verbose, descriptive metadata rather than minimal entries

### 2. PARA Method Structure
This vault follows the PARA method:
- **1-Projects**: Active projects with deadlines
- **2-Areas**: Ongoing responsibilities and interests
- **3-Resources**: Reference materials and knowledge
- **4-Archive**: Completed or inactive items

### 3. Consistent Categorization
- Use standardized tags and categories
- Follow established naming conventions
- Maintain hierarchical relationships between notes

---

## Required Metadata Structure

### Universal Fields (All Documents)
```yaml
---
id: unique-identifier
title: "Document Title"
description: "Brief description of content"
creation_date: YYYY-MM-DD
modification_date: YYYY-MM-DD
type: document-type
area: primary-area
tags: [tag1, tag2, tag3]
author: author-name
status: active|planning|on-hold|completed|cancelled
priority_score: 0-100
related:
  depends-on: []
  blocks: []
  area-overlap: []
  references: []
  supports: []
  relates-to: []
---
```

### Document Types and Specific Fields

#### Projects (type: project)
```yaml
project_owner: "Jordan|Other"
project_client: "Personal|Church|University|Client Name"
priority: "high|medium|low"
deadline: YYYY-MM-DD
start_date: YYYY-MM-DD
completion_percentage: 0-100
estimated_hours: number
```

#### Resources (type: resource)
```yaml
source: "Personal research|Website|Book|Course|Documentation|Video|Podcast|Other"
difficulty: "easy|medium|hard|expert"
resource_type: "guide|reference|tool|tutorial|template|checklist|documentation|other"
url: "URL if applicable"
usefulness_rating: 1-5
last_used: YYYY-MM-DD
keywords: [keyword1, keyword2]
```

#### Areas (type: area)
```yaml
responsibility_level: "primary|secondary|monitoring"
time_commitment: "daily|weekly|monthly|quarterly|annual"
stakeholders: [person1, person2]
```

#### Tasks (type: task)
```yaml
due: YYYY-MM-DD
priority: "high|medium|low"
project: "project-name"
estimated_time: "duration"
```

---

## Standardized Categories and Tags

### Areas
- `Software-Development`
- `Administration`
- `Personal`
- `Church`
- `University`
- `Finance`
- `Compliance`

### Priority Tags
- `critical` - Highest importance
- `urgent` - Time-sensitive
- `important` - High value
- `routine` - Regular maintenance

### Content Tags
- `guide` - How-to content
- `reference` - Quick lookup
- `tool` - Software/utility
- `tutorial` - Step-by-step learning
- `template` - Reusable format
- `checklist` - Verification list
- `documentation` - Technical docs

### Technology Tags
- `software-dev`
- `python`
- `javascript`
- `dotnet`
- `docker`
- `nginx`
- `mariadb`
- `s3`
- `ci-cd`

---

## Document Structure Template

### Standard Document Format
```markdown
---
[COMPREHENSIVE FRONTMATTER METADATA]
---

# Document Title

---

## Overview
Brief description of the document's purpose and content.

## Key Points
- Main takeaways
- Important information
- Critical details

## Details
Detailed information organized in logical sections.

### Subsection 1
Content with proper formatting.

### Subsection 2
More detailed content.

## Examples
```language
// Code examples with proper syntax highlighting
```
## Visual Aids
[Include Mermaid diagrams, SVG, or PNG as appropriate]

## Related Items
[Dataview queries to show related content]

## Notes
Additional context or considerations.

---

### Tags
#tag1 #tag2 #tag3 #area #priority

---
```

---

## Dataview Integration

### Essential Dataview Patterns

#### Project Dashboard
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority",
  completion_percentage + "%" as "Progress",
  deadline as "Due"
FROM "1-Projects"
WHERE status != "completed"
SORT priority_score DESC
```

#### Resource Library
```dataview
TABLE WITHOUT ID
  file.link as "Resource",
  resource_type as "Type",
  difficulty as "Difficulty",
  usefulness_rating as "Rating"
FROM "3-Resources"
WHERE area = "Software-Development"
SORT usefulness_rating DESC
```

#### Area Overview
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  responsibility_level as "Level",
  time_commitment as "Frequency"
FROM "2-Areas"
WHERE status = "active"
SORT responsibility_level ASC
```

---

## Visual Aids Policy

### Preferred Formats
1. **Mermaid** - Native Obsidian support, version controllable
2. **SVG** - Scalable, crisp rendering
3. **PNG** - Fallback for screenshots or raster images

### Mermaid Examples
```mermaid
flowchart TD
    A[Agent Input] --> B{Has Metadata?}
    B -->|Yes| C[Validate Structure]
    B -->|No| D[Add Required Metadata]
    C --> E[Process Content]
    D --> E
    E --> F[Save to Vault]
```

### Visual Aid Guidelines
- Include descriptive titles and captions
- Place diagrams near relevant content
- Use consistent styling and colors
- Tag visual content for easy retrieval

---

## Agent-Specific Instructions

### For All Agents
1. **Always include comprehensive metadata**
2. **Use horizontal rules (---) at document start and end**
3. **Apply consistent formatting and structure**
4. **Include relevant tags for searchability**
5. **Create relationships between related documents**

### Code Formatting
- Use proper language identifiers in code blocks
- Include syntax highlighting: ```python
,
```javascript, ```yaml
- Keep code examples concise and relevant
- Provide context for code snippets

### Link Management
- Use [[WikiLinks]] for internal references
- Include descriptive link text
- Maintain bidirectional relationships
- Update related documents when creating new content

---

## Quality Checklist

Before saving any document, verify:
- [ ] Complete frontmatter metadata
- [ ] Proper document type classification
- [ ] Relevant tags and categories
- [ ] Consistent formatting
- [ ] Appropriate visual aids
- [ ] Related document links
- [ ] Dataview compatibility
- [ ] Search optimization

---

## Relationship Management

### Relationship Types
- **depends-on**: Prerequisites or dependencies
- **blocks**: Items blocked by this document
- **area-overlap**: Cross-functional areas
- **references**: External sources or citations
- **supports**: Items this document enables
- **relates-to**: General associations

### Relationship Best Practices
```yaml
related:
  depends-on: ["[[Prerequisite Document]]"]
  blocks: ["[[Blocked Item]]"]
  area-overlap: ["[[Related Area]]"]
  references: ["[[Source Material]]"]
  supports: ["[[Enabled Project]]"]
  relates-to: ["[[Similar Topic]]"]
```
---

## Search Optimization

### Keyword Strategy
- Include synonyms and alternative terms
- Use domain-specific terminology
- Add common misspellings in keywords array
- Consider search intent and user queries

### Tag Hierarchy
```
Primary: #software-dev
Secondary: #python, #javascript, #dotnet
Specific: #flask, #react, #entity-framework
Context: #tutorial, #reference, #troubleshooting
```

---

## Automated Workflows

### Priority Score Calculation
Documents automatically calculate priority scores based on:
- Deadline proximity (projects)
- Usefulness rating (resources)
- Tag importance weights
- Recent usage patterns
- Relationship dependencies

### Template Integration
- Use Templater for dynamic content generation
- Auto-populate metadata fields
- Calculate relationships and scores
- Generate contextual queries

---

## Maintenance Guidelines

### Regular Reviews
- Monthly metadata audits
- Quarterly relationship updates
- Annual tag taxonomy review
- Continuous priority score validation

### Content Lifecycle
1. **Creation**: Full metadata, proper categorization
2. **Active Use**: Regular updates, relationship maintenance
3. **Archive**: Status change, relationship cleanup
4. **Deletion**: Relationship impact assessment

---

## Error Prevention

### Common Mistakes to Avoid
- Incomplete or missing metadata
- Inconsistent tag usage
- Broken internal links
- Orphaned documents without relationships
- Duplicate or conflicting information

### Validation Checklist
- Metadata completeness
- Tag standardization
- Link integrity
- Dataview query compatibility
- Search result accuracy

---

## Advanced Features

### Custom Dataview Queries
Create specialized queries for:
- Cross-project dependencies
- Resource utilization tracking
- Area responsibility mapping
- Knowledge gap identification
- Progress monitoring

### Integration Points
- Tasks plugin for project management
- Calendar for deadline tracking
- Graph view for relationship visualization
- Search for content discovery
- Export for external reporting

---

### Tags
#agents #obsidian #metadata #documentation #dataview #para #vault-structure #markdown #categorization #search #knowledge-management

---
Efficiency:
  - Prefer smallest set of high-signal actions and validations; ask if blocked
- Communication:
  - Summarize intentions and next steps; ask before risky actions
  - Create tasks for identified follow-up work

## Hosting & Cost Optimization
- Hosting: cloud VM providers (e.g., AWS EC2); consider Vultr/Hetzner/OVH for cost savings
- Region: Prefer AU to serve Brisbane clients
- CDN: Use when cost/performance beneficial; S3-compatible endpoints common
- Avoid expensive operations; reuse caches/artifacts; prune unused images/containers

## Agent Compatibility Notes
- Cline:
  - Follow this doc; propose minimal command sets; show commands and expected outputs
  - Create tasks for follow-up work as per Task Creation Policy
- Roo Code:
  - Short investigations; confirm signatures; tight edits; ask before push/deploy/package changes
  - Generate tasks for identified next steps
- Augment Code:
  - Use codebase context tools judiciously
  - When showing code, use <augment_code_snippet> tags
  - Follow tasklist triggers and minimal tool-call ethos
  - Create follow-up tasks when work identifies future needs

## Ready-to-Use Command Snippets

- Frontend (dev) with OrbStack:
```bash
docker compose -f deploy/docker-compose.dev.yml up -d frontend
curl -s -o /dev/null -w "%{http_code}\n" http://localhost:8080/index.html
```
- Health checks:
```bash
curl -s http://localhost:8080/health
```
- .NET backend quick build:
```bash
cd path/to/Your.Api
dotnet restore && dotnet build -c Release
```
- Python with uv:
```bash
uv python install 3.11
uv venv
uv pip install -r requirements.txt   # or: uv add <pkg>
uv run pytest -q
```
- MariaDB connectivity:
```bash
mysqladmin ping -h 127.0.0.1 -u root -p"${DB_ROOT_PASSWORD}"
```

---

### Tags
#agents #preferences #dev-tools #workflow #ci-cd #security #s3 #b2 #nginx #docker #orbstack #dotnet #mariadb #testing #cost-optimisation #obsidian #cline #roocode #augmentcode #python #uv #pip #mermaid #svg #diagrams #visual-aids #tasks #dataview

---
---
id: agents-handbook
title: "AGENTS.md — PaceySpace Agent Handbook (Template)"
description: "Template agent guide for PaceySpace: tooling, workflows, safety gates, CI/CD, Docker+OrbStack, Python uv, storage rules, visual aids, task creation to Obsidian/Dataview, and strict metadata policies."
company: "PaceySpace"
author: "Jordan Pacey"
owner: "<EMAIL>"
version: "1.0.3"
created: "2025-09-03"
updated: "2025-09-03"
status: "active"
type: "handbook/template"
project: "template"
area: "engineering/devops"
agents_supported: ["Cline", "Roo Code", "Augment Code"]
os: "macOS"
shell: "bash"
container_runtime: "OrbStack (docker CLI)"
languages: ["C#", ".NET 8", "JavaScript", "Python"]
db: "MariaDB 10.11"
storage: ["S3-compatible", "Backblaze B2 (S3 endpoints)"]
hosting: ["nginx", "cloud VM (e.g., AWS EC2)"]
ci_cd: true
requires_metadata: true
visual_aids: true
diagram_formats: ["Mermaid", "SVG", "PNG"]
task_management: true
obsidian:
  dataview: true
  tasks_plugin: true
  mermaid: true
  tags: ["agents","preferences","dev-tools","workflow","ci-cd","security","s3","b2","nginx","docker","orbstack","dotnet","mariadb","testing","cost-optimisation","obsidian","cline","roocode","augmentcode","python","uv","pip","mermaid","svg","diagrams","visual-aids","tasks","dataview"]
aliases: ["AGENTS","Agent Handbook","PaceySpace Agents Template"]
tags:
  - agents
  - preferences
  - dev-tools
  - workflow
  - ci-cd
  - security
  - s3
  - b2
  - nginx
  - docker
  - orbstack
  - dotnet
  - mariadb
  - testing
  - cost-optimisation
  - obsidian
  - cline
  - roocode
  - augmentcode
  - python
  - uv
  - pip
  - mermaid
  - svg
  - diagrams
  - visual-aids
  - tasks
  - dataview
permissions:
  safe_runs: ["unit_tests","linters","type_checks","builds","--help smokes"]
  dangerous_runs: ["db_migrations","deployments","long_jobs","external_cost_calls","data_writes"]
  ask_before:
    - "commits_pushes"
    - "installing_upgrading_dependencies"
    - "deployments_or_migrations"
    - "destructive_or_costly_operations"
secrets_policy:
  never_log: true
  use_env_or_secret_manager: true
  mask_in_outputs: true
profile:
  region: "Australia (Brisbane focus)"
  cost_sensitive: true
  nginx_frontend_preferred: true
  fallback_paths_required: true
  object_store_metadata_priority: true
---

## Purpose
- This file is optimized for Large Language Model Agents (Cline, Roo Code, Augment Code).
- It defines how to work in PaceySpace environments: tools, safety gates, CI/CD, data rules, documentation standards, visual aids, and task creation workflows.
- I require accurate and comprehensive Markdown file metadata in the header to power Obsidian Dataview, vault search workflows, and archival retrieval.

## My Profile and Priorities
- Location/clients: Brisbane, Australia; prefer low-latency for AU/NZ users
- Cost sensitivity: Prefer cost-effective hosting; avoid unnecessary spend
- Reliability: Maintain fallback paths and reversible operations during transitions
- Frontend hosting: Prefer nginx as a fronting server
- Data displays: When domain data includes object-store metadata (e.g., S3), prefer that metadata over fallback fields
- Documentation: I save agent outputs to Obsidian; I want clean, skimmable Markdown with tags and proper metadata

## Stack Overview
| Area | Default/Preference | Tags |
|---|---|---|
| Frontend hosting | nginx (alpine), static assets, /health | #nginx |
| Backend | .NET 8 (ASP.NET Core), EF Core | #dotnet |
| Database | MariaDB 10.11 (SQLite okay for local/dev) | #mariadb |
| Object storage | S3-compatible, often Backblaze B2 endpoints/CDN | #s3 #b2 |
| Containers | Docker CLI with OrbStack (NOT Docker Desktop) on macOS | #docker #orbstack |
| OS & shell | macOS + bash | #macos #bash |
| CI/CD | Maintain fallback deployment options; secure credential prompts | #ci-cd |
| Python | Use uv for Python version/env mgmt and installs | #python #uv #pip |

## Tools & Services I Use
- OrbStack (Docker runtime on macOS) — preferred over Docker Desktop
- Docker, docker-compose
- nginx (alpine)
- .NET 8 SDK/Runtime + EF Core tooling
- MariaDB 10.11
- S3-compatible APIs (Backblaze B2 service URLs/CDN common)
- GitHub (SSH)
- Python with uv (venv, version mgmt, fast installer)

## Package Management Policy
- Always use package managers; do not hand-edit lockfiles except for non-version config:
  - Node: npm/yarn/pnpm (match repo; default npm)
  - Python: uv preferred (env + installs + lock), pip usage routed via uv
  - Rust: cargo add/remove
  - Go: go get; go mod tidy
  - Ruby: bundler (bundle add/remove)
  - PHP: composer
  - C#/.NET: dotnet add/remove package
  - Java: Gradle/Maven
- Python with uv (examples):
```bash
uv python install 3.11
uv venv
uv pip install -r requirements.txt    # or: uv add <pkg>
uv run pytest -q
```

## Security, Permissions, and Safety Gates
- Always ask before:
  - Committing/pushing code
  - Installing/upgrading/removing dependencies
  - Deployments or database migrations
  - Running destructive or high-cost operations
- Credentials/secrets:
  - Use env vars or secrets manager; never log or commit secrets
  - Mask secrets in outputs; request explicitly and securely when needed
- Cost/safety:
  - Prefer smallest, least-expensive steps; get approval for long-running/heavy jobs

## Execution & Validation Runs
- When asked to "make sure it runs/works/builds/tests", actually run minimal safe commands and report:
  - Commands + cwd
  - Exit codes
  - Key output lines
- Safe-by-default runs (no approval needed):
  - Unit/integration tests, linters, type-checkers, builds, short CLI "--help"
- Ask first:
  - Data migrations, deployments, external calls that incur spend, heavy jobs, data writes

## CI/CD Expectations
- Maintain fallback deployment options during transitions
- Secure credential prompts: name variables/scopes; explain why needed
- Don't push or PR without confirming branch and target conventions
- After pushes: verify CI status before requesting review

## Data Handling & Storage
- Display rule: Prefer object-store metadata (S3) when available; fallback only when missing
- S3/B2:
  - Direct or CDN-backed S3 public URLs common (Backblaze B2 endpoints)
  - Keep DB and object metadata consistent
- Validation:
  - Check S3 object existence when referencing keys
  - For B2 sync, verify bucket/key/url metadata completeness

## Docker, Compose, and OrbStack Usage
- Runtime: OrbStack on macOS (assume docker CLI; avoid Docker Desktop-only features)
- Dev UX:
  - Provide compose targets that allow frontend-only serving (nginx + static files) without upstream API/uploader
  - Ensure /health endpoints for services
- Production UX:
  - Pull private images only with proper auth; otherwise provide local build fallback
- Logs:
  - Avoid logging secrets; present key lines; bound output sizes

## nginx Preferences
- Minimal configs with /health
- Proxy to backend only when backend is up/healthy
- For dev, allow static frontend serving without upstreams
- Controls/navigation visible and usable on mobile; keep within container bounds

## Code Quality, Testing, and Standards
- Testing:
  - Encourage unit/integration tests; iterate until green
  - Document how to run tests; keep runs minimal
- Coding standards:
  - Qualify namespaces to avoid type collisions (e.g., common names like MigrationResult)
  - Clear error handling and logging
- Docs:
  - Concise and skimmable; minimal necessary code excerpts

## Visual Aids Policy
- Use visual aids whenever suitable to clarify architecture, flows, or data models.
- Preferred and Obsidian-compatible formats:
  - Mermaid diagrams (native in Obsidian; ideal for architecture, sequence, flowcharts)
  - SVG (scalable, crisp; widely supported by Obsidian and plugins)
  - PNG (fallback for screenshots or raster visuals)
- Guidance:
  - Prefer Mermaid for diagrams that benefit from version control and easy diffing.
  - Provide a short caption/title and purpose.
  - Keep diagrams close to the relevant section and include tags for retrieval.
  - For complex visuals, include both a Mermaid source and an exported SVG/PNG if useful.

Example Mermaid (architecture overview):
```mermaid
flowchart LR
  Browser -->|HTTPS| Nginx
  Nginx -->|/api| API
  Nginx -->|/| Frontend
  API --> DB[(MariaDB)]
  API --> S3[(S3/B2)]
```

## Task Creation and Management Policy
- Create follow-up tasks for next steps, action items, and future work.
- Tasks are managed by Obsidian Dataview and Tasks plugin.

### Task Saving Workflow
1. First preference: Save tasks directly to my Obsidian vault (if accessible)
2. Second preference: Prompt the user to share the vault location; then save there
3. Fallback: Save a task document to docs/ in the project root

Agent prompt to user (example):
```text
Please provide your Obsidian vault path (absolute path). If unavailable, I will save tasks to docs/ at project root.
```

### Task Format Requirements
- Use Obsidian task syntax with metadata frontmatter compatible with Dataview:
```markdown
---
id: task-YYYYMMDD-HHMMSS
title: "Task Title"
created: "YYYY-MM-DD"
due: "YYYY-MM-DD"        # optional
priority: "high|medium|low"
project: "template"      # or set per-project when known
area: "engineering"
status: "todo"           # todo|in-progress|done|cancelled
tags: ["task","follow-up","engineering"]
---

# Task Title

## Description
Brief description of what needs to be done.

## Acceptance Criteria
- [ ] Specific deliverable 1
- [ ] Specific deliverable 2
- [ ] Specific deliverable 3

## Context
Background information, links to related work, etc.

## Notes
Additional notes, considerations, or dependencies.
```

### Task Creation Triggers
- When completing work that requires follow-up
- When identifying technical debt or improvements
- When encountering blockers that need resolution
- When planning multi-step implementations
- When the user requests task creation explicitly

### Task Metadata Standards
- Always include comprehensive frontmatter for Dataview queries
- Use consistent tag taxonomy: #task, #area, #priority
- Include due dates for time-sensitive items
- Link to related files, PRs, or documentation
- Use clear, actionable titles

## Obsidian Formatting Rules for Agent Outputs
- Use clean Markdown with:
  - Headings (##/###), concise paragraphs, bullet lists
  - Horizontal rules (---) at top and bottom of documents I’ll save
  - Code blocks with language hints (```bash, ```csharp, ```yaml)
  - Tags (e.g., #s3, #nginx, #optimisations)
  - Visual aids as per the Visual Aids Policy (Mermaid/SVG/PNG)
  - Task creation as per Task Creation Policy when applicable
- Augment Code:
  - When quoting existing repository code, wrap in:
    - <augment_code_snippet path="relative/path" mode="EXCERPT"> … </augment_code_snippet>
  - Use four backticks inside; keep each excerpt under 10 lines

## Secrets & Environment Variables (Common Patterns)
```env
# Database
DB_ROOT_PASSWORD=
DB_USER=
DB_PASSWORD=
DB_NAME=AppDb

# API/JWT
JWT_SECRET=

# S3 / B2 (S3-compatible)
S3_BUCKET_NAME=
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_REGION=
S3_ENDPOINT=

# Backblaze B2 (when used)
B2_APPLICATION_KEY_ID=
B2_APPLICATION_KEY=
B2_BUCKET_ID=
```
- Example connection string:
```env
ConnectionStrings__DefaultConnection=Server=db;Database=${DB_NAME};User=${DB_USER};Password=${DB_PASSWORD};Port=3306;
```
- Rule: Never log or commit secrets.

## Agent Behavior Model (Machine-Readable Summary)
- Information-gathering:
  - Targeted, minimal; stop once sufficient; confirm signatures before edits
- Task management:
  - Use a short Investigate/Triage task for non-trivial work
  - Keep one task IN_PROGRESS; batch state updates
  - Create follow-up tasks as per Task Creation Policy
- Edits:
  - Conservative, minimal; respect repository structure and conventions
- Efficiency:
  - Prefer smallest set of high-signal actions and validations; ask if blocked
- Communication:
  - Summarize intentions and next steps; ask before risky actions
  - Create tasks for identified follow-up work

## Hosting & Cost Optimization
- Hosting: cloud VM providers (e.g., AWS EC2); consider Vultr/Hetzner/OVH for cost savings
- Region: Prefer AU to serve Brisbane clients
- CDN: Use when cost/performance beneficial; S3-compatible endpoints common
- Avoid expensive operations; reuse caches/artifacts; prune unused images/containers

## Agent Compatibility Notes
- Cline:
  - Follow this doc; propose minimal command sets; show commands and expected outputs
  - Create tasks for follow-up work as per Task Creation Policy
- Roo Code:
  - Short investigations; confirm signatures; tight edits; ask before push/deploy/package changes
  - Generate tasks for identified next steps
- Augment Code:
  - Use codebase context tools judiciously
  - When showing code, use <augment_code_snippet> tags
  - Follow tasklist triggers and minimal tool-call ethos
  - Create follow-up tasks when work identifies future needs

## Ready-to-Use Command Snippets

- Frontend (dev) with OrbStack:
```bash
docker compose -f deploy/docker-compose.dev.yml up -d frontend
curl -s -o /dev/null -w "%{http_code}\n" http://localhost:8080/index.html
```

- Health checks:
```bash
curl -s http://localhost:8080/health
```

- .NET backend quick build:
```bash
cd path/to/Your.Api
dotnet restore && dotnet build -c Release
```

- Python with uv:
```bash
uv python install 3.11
uv venv
uv pip install -r requirements.txt   # or: uv add <pkg>
uv run pytest -q
```

- MariaDB connectivity:
```bash
mysqladmin ping -h 127.0.0.1 -u root -p"${DB_ROOT_PASSWORD}"
```

---

### Tags
#agents #preferences #dev-tools #workflow #ci-cd #security #s3 #b2 #nginx #docker #orbstack #dotnet #mariadb #testing #cost-optimisation #obsidian #cline #roocode #augmentcode #python #uv #pip #mermaid #svg #diagrams #visual-aids #tasks #dataview

---
