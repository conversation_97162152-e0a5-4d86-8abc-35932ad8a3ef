# Obsidian Vault Data Quality Improvement Guide

## Recommended AI Model: Claude 3.5 Sonnet

### Why Claude for This Task:
1. **200K context window** - Process multiple notes at once
2. **Structured data expertise** - Excellent with YAML/frontmatter
3. **Inference quality** - Best educated guesses for missing data
4. **Consistency** - Maintains patterns across large datasets

## Data Schema Requirements

### Essential Frontmatter Fields for All Notes:

```yaml
---
# Core Metadata
type: [project|area|resource|archive|daily|person|meeting]
title: "Note Title"
created: 2024-01-01T10:00:00
modified: 2024-01-01T10:00:00
tags: []
aliases: []

# PARA Classification
para_category: [1-Projects|2-Areas|3-Resources|4-Archive]
status: [active|on-hold|completed|archived]
priority: [high|medium|low]

# Relationships
related: []
parent: "[[Parent Note]]"
children: []

# Task Management
has_tasks: false
task_count: 0
completed_tasks: 0
next_action: ""
due_date: null
scheduled_date: null

# Content Metrics
word_count: 0
reading_time: "0 min"
completeness: [draft|partial|complete]

# Review Tracking
last_reviewed: null
review_frequency: [daily|weekly|monthly|quarterly|yearly|none]
needs_review: false

# Custom Fields (for specific note types)
project_deadline: null  # For projects
area_owner: null        # For areas
resource_type: null     # For resources
meeting_date: null      # For meetings
attendees: []           # For meetings
---
```

## Automated Improvement Process

### Phase 1: Audit Current State
```dataview
TABLE WITHOUT ID
  file.name as "Note",
  type as "Type",
  status as "Status",
  para_category as "PARA",
  length(file.tasks) as "Tasks"
FROM ""
WHERE !type OR !status OR !para_category
SORT file.mtime DESC
```

### Phase 2: Claude Processing Instructions

Use this prompt template for Claude:

```
You are a data quality specialist for an Obsidian vault using the PARA method.

TASK: Analyze these notes and add/improve frontmatter metadata.

RULES:
1. Infer missing 'type' from folder location and content
2. Guess 'created' date from file creation or content clues
3. Extract tags from content hashtags and topics
4. Identify relationships between notes from [[wikilinks]]
5. Count tasks and set has_tasks boolean
6. Estimate reading_time from word count (200 wpm)
7. Set status based on content completeness
8. For dates, use ISO 8601 format (YYYY-MM-DDTHH:MM:SS)

INFERENCE GUIDELINES:
- Projects: Look for outcomes, deadlines, deliverables
- Areas: Ongoing responsibilities, maintenance topics
- Resources: Reference materials, how-tos, documentation
- Daily notes: Date patterns in filename or content
- Meeting notes: Attendee lists, agenda items, action items

For each note, provide:
1. Updated frontmatter YAML
2. Confidence level (high/medium/low)
3. Reasoning for inferences
4. Suggested improvements for content

[INSERT NOTE CONTENT HERE]
```

### Phase 3: Batch Processing Script

```bash
#!/bin/bash
# Process notes in batches through Claude API

VAULT="/Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives"
BATCH_SIZE=10
OUTPUT_DIR="$VAULT/.data-improvement"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Find all markdown files needing improvement
find "$VAULT" -name "*.md" -type f | while read -r file; do
  # Check if frontmatter is incomplete
  if ! grep -q "^type:" "$file"; then
    echo "$file" >> "$OUTPUT_DIR/needs-improvement.txt"
  fi
done

# Process in batches
split -l $BATCH_SIZE "$OUTPUT_DIR/needs-improvement.txt" "$OUTPUT_DIR/batch-"

# Each batch can be processed through Claude
for batch in "$OUTPUT_DIR"/batch-*; do
  echo "Processing batch: $batch"
  # Add API call to Claude here
done
```

## Implementation Strategy

### Week 1: Foundation
1. Process all daily notes - establish date patterns
2. Process project folders - identify active vs completed
3. Process templates - ensure consistency

### Week 2: Relationships
1. Map parent-child relationships
2. Identify related notes clusters
3. Build topic maps from tags

### Week 3: Tasks & Reviews
1. Audit all task statuses
2. Set review schedules
3. Identify stale/outdated content

### Week 4: Validation
1. Run Dataview queries for gaps
2. User review of inferences
3. Adjust templates based on patterns

## Dataview Queries for Validation

### Find Notes Missing Critical Fields
```dataview
TABLE 
  type as "Type",
  status as "Status",
  para_category as "PARA"
FROM ""
WHERE !type OR !status OR !para_category
SORT file.name ASC
```

### Analytics Dashboard
```dataview
TABLE WITHOUT ID
  para_category as "Category",
  length(rows) as "Count",
  length(filter(rows, (r) => r.status = "active")) as "Active",
  length(filter(rows, (r) => r.has_tasks = true)) as "With Tasks"
FROM ""
WHERE file.name != this.file.name
GROUP BY para_category
```

### Review Schedule
```dataview
TABLE 
  last_reviewed as "Last Review",
  review_frequency as "Frequency",
  dateformat(date(today) - last_reviewed, "d") as "Days Ago"
FROM ""
WHERE needs_review = true OR 
  (review_frequency = "weekly" AND last_reviewed < date(today) - dur(7 days))
SORT last_reviewed ASC
```

## Alternative Models Comparison

| Model | Strength | Weakness | Best For |
|-------|----------|----------|----------|
| **Claude 3.5** | Context, reasoning, consistency | Cost | This comprehensive task ✅ |
| **GPT-4** | General knowledge | Smaller context | Quick edits |
| **Gemini 1.5 Pro** | 1M context window | Less structured data experience | Bulk reading |
| **Qwen** | Cost-effective | Less sophisticated inference | Simple field filling |
| **Augment** | IDE integration | Not for batch processing | Live editing |

## Cost Estimation

For ~1000 notes averaging 500 words each:
- Claude 3.5 Sonnet: ~$15-20
- GPT-4: ~$25-30  
- Gemini 1.5: ~$10-15
- Local Qwen: Free but lower quality

## Next Steps

1. **Export sample batch** (10-20 notes) for testing
2. **Run through Claude** with the prompt template
3. **Review quality** of inferences
4. **Adjust prompt** based on results
5. **Process full vault** in batches
6. **User review** with suggested changes highlighted
7. **Implement changes** with backup

## Monitoring Progress

```dataview
TABLE WITHOUT ID
  "**Metric**" as " ",
  "**Value**" as " "
FROM (
  {
    " ": "Total Notes",
    " ": length(file.lists)
  },
  {
    " ": "Notes with Type",
    " ": length(filter(file.lists, (f) => f.type))
  },
  {
    " ": "Notes with Status",  
    " ": length(filter(file.lists, (f) => f.status))
  },
  {
    " ": "Completion %",
    " ": round(length(filter(file.lists, (f) => f.type)) / length(file.lists) * 100) + "%"
  }
)
```

---
*Remember to backup your vault before bulk changes!*