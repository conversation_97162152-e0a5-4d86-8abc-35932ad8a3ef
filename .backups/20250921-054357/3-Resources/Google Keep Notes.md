---
creation_date: 2025-07-14
modification_date: 2025-07-14
type: index
aliases: [Google Keep, Keep Notes]
tags: [para/resources, google-keep, index, imported-notes]
resource: Reference
related_resources: ["Para Notes Guide"]
---

# Google Keep Imported Notes

> This is an index of all notes imported from Google Keep, organized by categories for easier reference.

## Categories

### Personal Information
- [[Matilda dob]]
- [[Tfn tax file number]]
- [[QCAT case number]]
- [[Medicare card]]
- [[USI NUMBER(1)]]
- [[Usi number]]

### Tasks & To-Do Lists
- [[Todo]]
- [[To get]]
- [[To get(1)]]
- [[To get toiletries]]
- [[To get tonight]]
- [[Shopping list 7_10]]

### Work & Professional
- [[Timesheet]]
- [[Timesheet(1)]]
- [[Arctick license]]
- [[Arctick]]
- [[Interview questions 12_11_24]]

### Finance & Budget
- [[BTC total investments]]
- [[Budget August 20]]
- [[Tax receipts]]
- [[Speed fine]]

### Home & Properties
- [[Lease]]
- [[Home owner built]]
- [[Jenny door fridge seal dimensions]]
- [[Bunnings]]
- [[Gardening tools needed]]

### Technical Notes
- [[Boot windows into safe mode_]]
- [[Cheat sheet cabrecu]]
- [[Capstone test notes]]

## All Google Keep Notes
```dataview
TABLE 
  file.ctime as "Created",
  file.mtime as "Modified",
  tags as "Tags"
FROM "Brain/Google Keep"
SORT file.name ASC
```

## Migration Status
To track the migration of Google Keep notes to the PARA structure, use this query:

```dataview
TABLE 
  contains(tags, "para/projects") OR contains(tags, "para/areas") OR contains(tags, "para/resources") OR contains(tags, "para/archive") AS "Categorized",
  choice(contains(tags, "para/projects"), "Project", choice(contains(tags, "para/areas"), "Area", choice(contains(tags, "para/resources"), "Resource", choice(contains(tags, "para/archive"), "Archive", "None")))) as "PARA Type"
FROM "Brain/Google Keep"
SORT file.name ASC
```

## Related
- [[3-Resources]]
- [[3-Resources/Para Notes Guide]]
- [[Resource Index]]