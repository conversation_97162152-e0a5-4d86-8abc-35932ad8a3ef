---
creation_date: 2025-04-06
modification_date: 2025-04-16
type: resource
source: Personal research
tags: [para/resources, networking, zerotier, aws, ec2, bridge, maintenance, automation, iptables, security, vpn, linux, bash, systemd]
related: ["Network Architecture Overview", "Minimal Gateway For ZeroTier Networks", "Secure ZeroTier Network Separation", "ZeroTier Flow Rules Placement"]
area: "Network-Administration"
difficulty: medium
server_type: "AWS EC2"
operating_system: "Ubuntu"
network_id: "8056c2e21ce5322d"
local_server_ip: "*************"
script_location: "/usr/local/bin/ec2-bridge-maintenance.sh"
---

# EC2 Bridge Server Maintenance

This document details the setup and maintenance procedures for the EC2 bridge server that forwards internet traffic to our ZeroTier-connected Nginx Proxy Manager. The automated script ensures iptables rules persist across reboots and handles various maintenance tasks.

## Overview

The EC2 bridge server acts as a gateway between the public internet and our private ZeroTier network. It:

1. Receives HTTP/HTTPS traffic from the internet
2. Forwards this traffic to our local Nginx Proxy Manager over ZeroTier
3. Allows our local services to be securely accessible from anywhere

## Maintenance Script

The maintenance script (`ec2-bridge-maintenance.sh`) automates the setup and maintenance of the bridge server. It handles:

- Setting up iptables rules for traffic forwarding
- Ensuring rules persist across reboots
- Checking ZeroTier connectivity
- Monitoring and maintaining the bridge configuration
- Generating status reports

### Script Location

The script should be stored in a secure location on the EC2 bridge instance:

```bash
/usr/local/bin/ec2-bridge-maintenance.sh
```

### Script Configuration

The script contains configuration variables at the top that should be adjusted to match your environment:

```bash
# Network interfaces
INTERNET_IFACE="enX0"         # Internet-facing interface (typically eth0 or enX0)
ZEROTIER_IFACE="ztmjfcpubr"   # ZeroTier interface name

# IP addresses
LOCAL_SERVER_IP="*************"  # ZeroTier IP of your local Nginx Proxy Manager

# ZeroTier Network ID
ZEROTIER_NETWORK_ID="8056c2e21ce5322d"  # Your ZeroTier network ID
```

### Installation Instructions

1. **Copy the Script to the EC2 Bridge Instance**:

   ```bash
   scp ec2-bridge-maintenance.sh ubuntu@your-ec2-instance:/tmp/
   ```

2. **SSH to the Bridge Instance**:

   ```bash
   ssh ubuntu@your-ec2-instance
   ```

3. **Move the Script to a Permanent Location**:

   ```bash
   sudo mv /tmp/ec2-bridge-maintenance.sh /usr/local/bin/
   ```

4. **Make the Script Executable**:

   ```bash
   sudo chmod +x /usr/local/bin/ec2-bridge-maintenance.sh
   ```

5. **Run the Script for Initial Setup**:

   ```bash
   sudo /usr/local/bin/ec2-bridge-maintenance.sh
   ```

6. **Set Up Automated Maintenance (Optional)**:

   ```bash
   sudo /usr/local/bin/ec2-bridge-maintenance.sh --add-cron
   ```

## Script Features

### Networking Configuration

1. **IP Forwarding**: Enables IP forwarding in the kernel to allow the server to route traffic.
2. **iptables Rules**: Sets up NAT and forwarding rules to direct traffic from the internet to the ZeroTier network.
3. **Persistence**: Configures systemd service to ensure rules persist across reboots.

### ZeroTier Management

1. **Service Verification**: Ensures the ZeroTier service is running.
2. **Network Membership**: Verifies and maintains connection to the configured ZeroTier network.
3. **Interface Monitoring**: Checks for proper ZeroTier interface configuration and IP assignment.

### Connectivity Testing

1. **Internet Connectivity**: Confirms the bridge has internet access.
2. **Local Server Connectivity**: Tests connectivity to the local Nginx Proxy Manager.
3. **Port Accessibility**: Checks if HTTP and HTTPS ports are accessible on the local server.

### Status Reporting

Generates comprehensive status reports including:

- System information
- Network interfaces
- ZeroTier status
- iptables rules
- Service status
- Connectivity test results

### Automated Maintenance

When set up with a cron job (`--add-cron`), the script will:

1. Run daily at 2 AM
2. Perform all checks
3. Fix any issues detected
4. Generate a status report

## Troubleshooting

### Common Issues

1. **ZeroTier Connection Problems**:

   - **Symptom**: Status report shows ZeroTier status as OFFLINE.
   - **Solution**:
     ```bash
     sudo systemctl restart zerotier-one
     sudo zerotier-cli info
     sudo zerotier-cli listnetworks
     ```

2. **Missing iptables Rules After Reboot**:

   - **Symptom**: Traffic not forwarding after server restart.
   - **Solution**: Verify the iptables-restore service is enabled and active:
     ```bash
     sudo systemctl status iptables-restore.service
     ```
   - If needed, manually run the maintenance script:
     ```bash
     sudo /usr/local/bin/ec2-bridge-maintenance.sh
     ```

3. **DNS Resolution Issues**:

   - **Symptom**: Domain name not resolving to the bridge server.
   - **Solution**: Verify DNS A record points to the Elastic IP of the bridge instance.

### Useful Commands

```bash
# Check ZeroTier status
sudo zerotier-cli info
sudo zerotier-cli listnetworks

# View current iptables rules
sudo iptables -L -v -n
sudo iptables -t nat -L -v -n

# Check service status
sudo systemctl status zerotier-one
sudo systemctl status iptables-restore

# View logs from the maintenance script
sudo cat /var/log/bridge-maintenance/maintenance.log

# View the latest status report
sudo ls -lt /var/log/bridge-maintenance/ | grep status_report | head -1
```

## Security Considerations

1. **AWS Security Groups**:
   - Restrict inbound traffic to only necessary ports (80, 443, 22)
   - Limit SSH access to trusted IP addresses

2. **ZeroTier Network**:
   - Use ZeroTier flow rules to restrict traffic
   - Only authorize known devices to join the network

3. **iptables Rules**:
   - The current configuration allows all outbound traffic
   - Consider adding additional rules to restrict outbound traffic if needed

## Network Topology

```
┌─────────────────┐    HTTPS    ┌────────────────┐   ZeroTier   ┌───────────────────┐
│                 │    HTTP     │                │              │                   │
│    Internet     ├────────────►│  EC2 Bridge    ├─────────────►│  Nginx Proxy Mgr  │
│                 │             │    Server      │              │   (*************) │
└─────────────────┘             └────────────────┘              └───────────────────┘
                                                                          │
                                                                          │
                                                                          ▼
                                                               ┌───────────────────┐
                                                               │                   │
                                                               │  Local Services   │
                                                               │   (e.g., Jellyfin) │
                                                               └───────────────────┘
```

## Script Maintenance

### Updating the Script

1. **Make a Backup**:

   ```bash
   sudo cp /usr/local/bin/ec2-bridge-maintenance.sh /usr/local/bin/ec2-bridge-maintenance.sh.bak
   ```

2. **Edit the Script**:

   ```bash
   sudo nano /usr/local/bin/ec2-bridge-maintenance.sh
   ```

3. **Test the Updated Script**:

   ```bash
   sudo /usr/local/bin/ec2-bridge-maintenance.sh
   ```

### Adding New Services

To add forwarding for additional services:

1. Edit the script
2. Add new iptables rules for the additional ports
3. Update the Nginx Proxy Manager configuration

Example for adding a new service on port 8080:

```bash
# Forward additional service traffic
iptables -t nat -A PREROUTING -i "$INTERNET_IFACE" -p tcp --dport 8080 -j DNAT --to-destination "${LOCAL_SERVER_IP}:8080"
iptables -A FORWARD -i "$INTERNET_IFACE" -o "$ZEROTIER_IFACE" -p tcp --dport 8080 -d "$LOCAL_SERVER_IP" -j ACCEPT
```

## Regular Maintenance Tasks

1. **Weekly**:
   - Review the status reports
   - Check for any connectivity issues
   - Verify ZeroTier network status

2. **Monthly**:
   - Update the EC2 instance (security patches)
   - Verify AWS security group rules
   - Check for ZeroTier updates

3. **Quarterly**:
   - Review and update the maintenance script if needed
   - Verify that all services are still accessible
   - Check for any abandoned or unused port forwards

## Disaster Recovery

### EC2 Instance Failure

If the EC2 bridge instance fails:

1. **Launch a New EC2 Instance**:
   - Use the same AMI or a similar Ubuntu/Debian image
   - Assign the same Elastic IP

2. **Install ZeroTier**:
   ```bash
   curl -s https://install.zerotier.com | sudo bash
   ```

3. **Join the ZeroTier Network**:
   ```bash
   sudo zerotier-cli join 8056c2e21ce5322d
   ```

4. **Install and Run the Maintenance Script**:
   - Transfer the script to the new instance
   - Update configuration variables if needed
   - Run the script to restore the bridge functionality

### Backup Strategy

1. **Regular Backups**:
   - The EC2 instance configuration
   - The maintenance script
   - Status reports

2. **Documentation**:
   - Keep this documentation updated
   - Document any changes to the network configuration
   - Document any additional services added

## Tasks
- [ ] Perform initial setup on the EC2 bridge instance
- [ ] Configure scheduled backups of the instance
- [ ] Set up monitoring for the bridge server
- [ ] Review and update security settings quarterly
- [ ] Document any custom configurations

## Related
- [[Network Architecture Overview]]
- [[Minimal Gateway For ZeroTier Networks]]
- [[Secure ZeroTier Network Separation]]
- [[3-Resources]]
- [[Resources TOC]]
