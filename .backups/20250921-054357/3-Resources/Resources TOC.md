---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: index
aliases: [Resources TOC, Resources Table of Contents]
tags: [para/resources, index, toc]
---
 [[]]
# Resources Table of Contents

> A comprehensive index of all resources organized by category.

## Guides
- [[Vault Organization Guide]] - Guide to using the PARA structure in Obsidian
- [[Dataview Guide]] - Guide to using Dataview effectively
- [[Recommended Plugins]] - List of recommended plugins for Obsidian
- [[LVM Disk Commands Linux]] - Guide to safely reduce and extend LVM partitions
- [[Flaresolverr and Prowlarr Setup]] - Guide to setting up Flaresolverr with Prowlarr
- [[Computer Education]] - Collection of computer education resources

## Cheatsheets
- [[1 Git Command Cheatsheet]] - Collection of useful Git commands
- [[Brain/3-Resources/Cheatsheets/Windows Shortcuts]] - Comprehensive list of keyboard shortcuts for Windows
- [[Senior Developer Coding Rules]] - Coding rules and best practices from a senior developer

## Software
- [[Brain/3-Resources/Software Guides/Docker Configuration Files]] - Docker Compose configuration files for various services
- [[Brain/3-Resources/Software Guides/Managing Docker Folder Permissions]] - Guide to managing Docker folder permissions

## Network
- [[Network TOC]] - Comprehensive index of all network-related resources
- [[Network Architecture Overview]] - Comprehensive overview of secure network architecture
- [[EC2 Bridge Server Maintenance]] - Setup and maintenance procedures for the EC2 bridge server

## Administration
- [[Brain/3-Resources/Hall Hire Procedures]] - Official procedures for booking church property
- [[Email Server Settings]] - Configuration settings for email clients

## Church
- [[CRUCA-Church-Vault/3-Resources/Church 1/Church TOC]] - Comprehensive index of all church-related resources
- [[CRUCA-Church-Vault/3-Resources/Church 1/Bank Account and Finance Guides]] - Banking and finance procedures for church administration
- [[CRUCA-Church-Vault/3-Resources/Church 1/Membership Definitions]] - Official definitions of different membership types within the Uniting Church

## University
- [[University TOC]] - Comprehensive index of all university-related resources
- [[University Preparation Programs]] - Information about Tertiary Preparation Programs (TPP) at various universities

## Family
- [[Family TOC]] - Comprehensive index of all family-related resources
- [[Media Influences on Children]] - Documentation of concerns regarding negative media influences on children

## Dotfiles
- [[Dotfiles-TOC]] - Table of contents for dotfiles management
- [[Dotfiles-Summary]] - Summary of dotfiles management
- [[Dotfiles-Installation]] - Installation guide for dotfiles
- [[Dotfiles-Management]] - Management guide for dotfiles
- [[Dotfiles-Components]] - Components of dotfiles
- [[Dotfiles-Backup-Script]] - Backup script for dotfiles

## Prompt Engineering
- [[Prompt Engineering TOC]] - Comprehensive index of all prompt engineering resources
- [[Prompt Engineering Fundamentals]] - Core concepts and principles of effective prompt engineering
- [[AI Assistant Memory Management Guidelines]] - Guidelines for optimizing AI assistant memory management
- [[3System Prompt Templates]] - Collection of effective system prompts for different use cases
- [[Code Generation Prompts]] - Specialized prompts for effective code generation
- [[MCP Server Setup and Utilization]] - Comprehensive guide for setting up and utilizing an MCP server
- [[Detailed Plan for Setting Up a Personalized MCP Server]] - Step-by-step implementation plan for a Python MCP server

## Website
- [[Website Design Best Practices]] - Best practices for modern website design
- [[YendorCats S3 Metadata Implementation]] - Implementation of S3 metadata for cat images in YendorCats
- [[YendorCats File Uploader Service]] - Node.js microservice for uploading cat images to Backblaze B2
- [[YendorCats Deployment Guide]] - Guide to deploying YendorCats with Enhance Control Panel
- [[YendorCats Cloudflare Integration]] - Guide to integrating Cloudflare with YendorCats

## Family
```dataview
LIST
FROM "3-Resources/Family"
SORT file.name ASC
```

## University
```dataview
LIST
FROM "3-Resources/University"
SORT file.name ASC
```

## Church
```dataview
LIST
FROM "3-Resources/Church"
SORT file.name ASC
```

## Network
```dataview
LIST
FROM "3-Resources/Network"
SORT file.name ASC
```

## Prompt Engineering
```dataview
LIST
FROM "3-Resources/Prompt Engineering"
SORT file.name ASC
```

## All Resources by Type
```dataview
TABLE
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources"
SORT type ASC, file.name ASC
```

## Related
- [[3-Resources]]
- [[Home]]
