---
creation_date: 2024-06-12
modification_date: 2024-06-12
type: note
aliases:
  - System Configuration
  - EndeavourOS Configuration
tags:
  - para/resources
  - linux
  - configuration
  - endeavouros
  - index
area: System
project: System Management
resource: Linux
archive: 
status: active
priority: 1
links:
  - "[[Brain/3-Resources/Dotfiles/Dotfiles-TOC]]"
  - "[[Brain/3-Resources/Dotfiles/Dotfiles-Summary]]"
related:
  - "[[Computer Education 1]]"
---

# System Configuration

This document serves as the main entry point for all documentation related to my EndeavourOS system configuration.

## Dotfiles Management

I've set up a comprehensive dotfiles management system to make my EndeavourOS setup portable and easy to install on other computers.

- [[Brain/3-Resources/Dotfiles/Dotfiles-Summary|Dotfiles Summary]] - Overview of the dotfiles management system
- [[Brain/3-Resources/Dotfiles/Dotfiles-TOC|Dotfiles Table of Contents]] - Complete documentation index

### Key Components

| Component | Description |
| --------- | ----------- |
| Fish Shell | My primary shell with custom functions and aliases |
| i3 Window Manager | Tiling window manager with custom keybindings |
| XFCE4 | Desktop environment components and settings |
| Development Tools | Git, SSH, and editor configurations |
| System Configuration | X11 and input device settings |

### Quick Links

- [Setup Repository Script](Brain/3-Resources/Dotfiles/Scripts/setup-repo.sh)
- [Backup Script](Brain/3-Resources/Dotfiles/Scripts/backup.sh)
- [Installation Script](Brain/3-Resources/Dotfiles/Scripts/install.sh)
- [Configuration File Example](Brain/3-Resources/Dotfiles/dotfiles.conf.example)

## System Maintenance

Regular system maintenance tasks and procedures.

### Updates

To update the system:

```bash
sudo pacman -Syu
```

### Cleaning

To clean package cache:

```bash
sudo pacman -Sc
```

To remove orphaned packages:

```bash
sudo pacman -Rns $(pacman -Qtdq)
```

## Hardware Configuration

Documentation for hardware-specific configuration.

### Graphics

- NVIDIA driver configuration
- Display settings

### Input Devices

- Keyboard layout and settings
- Mouse and touchpad configuration

## Software

Key software installed on the system.

### Development

- VS Code
- Git
- Docker
- Programming languages

### Productivity

- Web browsers
- Office applications
- Communication tools

### Multimedia

- Media players
- Image editors
- Audio tools

## Tasks
- [ ] Set up dotfiles repository
- [ ] Document hardware configuration
- [ ] Create software installation guide
- [ ] Set up automated system maintenance

## Metadata
- **Original Creation**: 2024-06-12
- **Source**: Personal configuration
- **Context**: System management and documentation
