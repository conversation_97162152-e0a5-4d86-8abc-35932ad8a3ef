---
creation_date: 2025-04-27
modification_date: 2025-04-27
type: resource
source: Project documentation
tags: [para/resources, guide, software-dev, cloudflare, cdn, waf, dns]
area: Software-Development
difficulty: medium
url: https://developers.cloudflare.com/fundamentals/
---

# YendorCats Cloudflare Integration

## Overview
This guide details the integration of Cloudflare with the YendorCats application. Cloudflare provides CDN services for faster content delivery, WAF (Web Application Firewall) for security, and DNS management for the domain. This integration enhances the performance, security, and reliability of the YendorCats website.

## Key Points
- Cloudflare as the domain registrar
- Free WAF for security
- CDN for faster content delivery
- DNS management for the domain
- SSL/TLS encryption for secure communication
- Integration with Enhance Control Panel

## Cloudflare Setup

### 1. Domain Registration and Management

1. **Register a Domain with Cloudflare**:
   - Go to [Cloudflare Registrar](https://www.cloudflare.com/products/registrar/)
   - Search for your desired domain name
   - Complete the registration process
   - Cloudflare will automatically configure DNS for your domain

2. **Transfer an Existing Domain to Cloudflare**:
   - Go to the Cloudflare dashboard
   - Click "Add a Site" and enter your domain name
   - Follow the instructions to transfer your domain to Cloudflare
   - Update your domain's nameservers to Cloudflare's nameservers

### 2. DNS Configuration

1. **Add DNS Records**:
   - Go to the DNS tab in your Cloudflare dashboard
   - Add an A record pointing to your VPS server's IP address:
     ```
     Type: A
     Name: @
     Content: your-server-ip
     Proxy status: Proxied
     TTL: Auto
     ```
   - Add CNAME records for subdomains:
     ```
     Type: CNAME
     Name: www
     Content: @
     Proxy status: Proxied
     TTL: Auto
     ```
     ```
     Type: CNAME
     Name: api
     Content: @
     Proxy status: Proxied
     TTL: Auto
     ```
     ```
     Type: CNAME
     Name: uploader
     Content: @
     Proxy status: Proxied
     TTL: Auto
     ```

2. **Configure DNS Settings**:
   - Enable DNSSEC for additional security
   - Configure DNS-only records for email (MX, TXT, etc.)
   - Set appropriate TTLs for different record types

### 3. WAF Configuration

1. **Enable Cloudflare's Free WAF**:
   - Go to Security > WAF in your Cloudflare dashboard
   - Enable the free WAF plan
   - Review and enable the recommended rule sets

2. **Configure WAF Rules**:
   - Enable OWASP Core Rule Set
   - Configure rate limiting to prevent brute force attacks
   - Set up IP access rules to block malicious IPs
   - Enable browser integrity check

3. **Custom WAF Rules**:
   - Create custom rules to protect sensitive endpoints:
     ```
     If incoming requests match...
     URI Path contains: /admin
     Then...
     Block
     ```
   - Create rate limiting rules for the API:
     ```
     If incoming requests match...
     URI Path contains: /api
     And...
     Rate exceeds: 100 requests per minute
     Then...
     Block for 1 hour
     ```

### 4. CDN Configuration

1. **Configure Caching**:
   - Go to Caching in your Cloudflare dashboard
   - Set appropriate cache TTLs for different content types:
     ```
     Cache Level: Standard
     Browser Cache TTL: 4 hours
     ```
   - Create page rules for specific caching behaviors:
     ```
     URL: */resources/*
     Cache Level: Cache Everything
     Edge Cache TTL: 1 day
     ```

2. **Configure Optimization**:
   - Enable Auto Minify for HTML, CSS, and JavaScript
   - Enable Brotli compression
   - Enable Rocket Loader for faster JavaScript loading
   - Enable Early Hints for faster page loads

3. **Configure Image Optimization**:
   - Enable Polish to optimize images
   - Enable Mirage for lazy loading images
   - Configure Image Resizing for responsive images

### 5. SSL/TLS Configuration

1. **Configure SSL/TLS**:
   - Go to SSL/TLS in your Cloudflare dashboard
   - Set the SSL/TLS encryption mode to "Full (strict)"
   - Enable "Always Use HTTPS"
   - Enable "Automatic HTTPS Rewrites"
   - Enable TLS 1.3

2. **Configure Origin Server SSL/TLS**:
   - Generate an origin certificate from Cloudflare
   - Install the certificate on your server
   - Configure your server to use the certificate

### 6. Firewall Rules

1. **Configure Firewall Rules**:
   - Go to Firewall > Firewall Rules in your Cloudflare dashboard
   - Create rules to block malicious traffic:
     ```
     If incoming requests match...
     URI Path contains: /wp-admin
     Then...
     Block
     ```
   - Create rules to allow only specific countries:
     ```
     If incoming requests match...
     Country not in: [AU, US, CA, GB, NZ]
     Then...
     Challenge
     ```

## Integration with Enhance Control Panel

### 1. Configure Enhance Control Panel for Cloudflare

1. **Add Cloudflare API Token**:
   - Generate an API token in your Cloudflare dashboard
   - Add the token to Enhance Control Panel:
     ```bash
     enhance config set cloudflare.api_token your-cloudflare-api-token
     ```

2. **Configure DNS Provider**:
   - Set Cloudflare as the DNS provider:
     ```bash
     enhance config set dns.provider cloudflare
     ```

3. **Update Domain Configuration**:
   - Update your domain configuration:
     ```bash
     enhance domain update yourdomain.com --cloudflare
     ```

### 2. Configure SSL/TLS Certificates

1. **Generate SSL/TLS Certificates**:
   - Use Enhance Control Panel to generate certificates:
     ```bash
     enhance ssl generate yourdomain.com
     ```

2. **Install Certificates**:
   - Install the certificates on your server:
     ```bash
     enhance ssl install yourdomain.com
     ```

3. **Configure Certificate Auto-Renewal**:
   - Enable auto-renewal for certificates:
     ```bash
     enhance ssl auto-renew yourdomain.com
     ```

### 3. Configure Cloudflare Workers (Optional)

1. **Create a Cloudflare Worker**:
   - Go to Workers in your Cloudflare dashboard
   - Create a new worker for custom functionality
   - Deploy the worker to your domain

2. **Configure Worker Routes**:
   - Set up routes for your worker:
     ```
     Route: yourdomain.com/api/*
     Worker: api-worker
     ```

## Performance Optimization

### 1. Configure Cache-Control Headers

1. **Set Cache-Control Headers for Static Content**:
   - Add the following headers to your static content:
     ```
     Cache-Control: public, max-age=86400
     ```

2. **Set Cache-Control Headers for API Responses**:
   - Add the following headers to your API responses:
     ```
     Cache-Control: private, no-cache, no-store, must-revalidate
     ```

### 2. Configure Cloudflare Page Rules

1. **Create Page Rules for Caching**:
   - Go to Rules > Page Rules in your Cloudflare dashboard
   - Create a rule for static content:
     ```
     URL: */resources/*
     Cache Level: Cache Everything
     Edge Cache TTL: 1 day
     ```
   - Create a rule for the API:
     ```
     URL: */api/*
     Cache Level: Bypass
     ```

## Security Considerations

### 1. Configure Security Headers

1. **Set Security Headers**:
   - Add the following headers to your responses:
     ```
     Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
     X-Content-Type-Options: nosniff
     X-Frame-Options: DENY
     Content-Security-Policy: default-src 'self'; img-src 'self' https://f004.backblazeb2.com; script-src 'self'; style-src 'self';
     Referrer-Policy: strict-origin-when-cross-origin
     ```

### 2. Configure Rate Limiting

1. **Set Rate Limits for API Endpoints**:
   - Go to Security > WAF > Rate limiting rules in your Cloudflare dashboard
   - Create a rule for the API:
     ```
     If incoming requests match...
     URI Path contains: /api
     And...
     Rate exceeds: 100 requests per minute
     Then...
     Block for 1 hour
     ```

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[YendorCats Cloudflare Integration]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[YendorCats Cloudflare Integration]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "cloudflare") OR contains(tags, "cdn") OR contains(tags, "waf") OR contains(tags, "dns")
LIMIT 5
```

## Quick Links
- [[YendorCats Project Documentation|Back to Project]]
- [[YendorCats Deployment Guide|Deployment Guide]]
- [[3-Resources|All Resources]]
