# Detailed Plan for Setting Up a Personalized MCP Server

## Research Summary

The Model Context Protocol (MCP) is an open protocol that allows LLMs to interact with external tools and data sources. This is perfect for creating a personalized code assistant workflow. Here's what I've learned:

1. **What is MCP**: The Model Context Protocol is an open standard that enables AI assistants to connect to systems where data lives, including content management, code repositories, and databases.

2. **MCP Server Implementation**: The official Python SDK (mcp) provides a straightforward way to build MCP servers. The FastMCP class makes it easy to create servers with resources, tools, and prompts.

3. **Available MCP Servers**: There are many existing MCP servers that could be useful for code assistance:
   - Git MCP Server: For interacting with Git repositories
   - GitHub MCP Server: For GitHub integration
   - AWS MCP Servers: For AWS-specific development workflows
   - Code-related MCP servers from third parties

4. **Integration Options**: MCP servers can be integrated with various clients like Claude Desktop, Cursor, and other LLM-based tools.

## Detailed Plan for Setting Up a Personalized MCP Server

Let's create a plan to set up a personalized MCP server for better LLM code assistant workflows:

### 1. Set Up the Development Environment

1. Install the required dependencies:
   - Python 3.10+ 
   - uv package manager
   - MCP Python SDK

2. Create a virtual environment for the project:
   ```bash
   mkdir -p ~/Projects/mcp-dev-server
   cd ~/Projects/mcp-dev-server
   uv venv
   source .venv/bin/activate
   ```

3. Install the MCP SDK:
   ```bash
   uv add "mcp[cli]"
   ```

### 2. Create a Basic MCP Server

Let's start with a simple MCP server that provides code-related tools:

```python
# server.py
from mcp.server.fastmcp import FastMCP, Context

# Create an MCP server
mcp = FastMCP("Code Assistant")

# Add a tool to analyze code structure
@mcp.tool()
def analyze_code_structure(file_path: str) -> str:
    """Analyze the structure of a code file and return a summary"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Simple analysis - count lines, functions, classes
        lines = content.split('\n')
        line_count = len(lines)
        function_count = content.count('def ')
        class_count = content.count('class ')
        
        return f"""
        Code Structure Analysis for {file_path}:
        - Total lines: {line_count}
        - Functions: {function_count}
        - Classes: {class_count}
        """
    except Exception as e:
        return f"Error analyzing file: {str(e)}"

# Add a resource to provide code snippets
@mcp.resource("snippet://{language}/{category}")
def get_code_snippet(language: str, category: str) -> str:
    """Get a code snippet for a specific language and category"""
    snippets = {
        "python": {
            "file_handling": """
            # Reading a file in Python
            with open('filename.txt', 'r') as file:
                content = file.read()
                
            # Writing to a file in Python
            with open('filename.txt', 'w') as file:
                file.write('Hello, World!')
            """,
            "api_request": """
            # Making an API request in Python
            import requests
            
            response = requests.get('https://api.example.com/data')
            data = response.json()
            """
        },
        "javascript": {
            "file_handling": """
            // Reading a file in Node.js
            const fs = require('fs');
            fs.readFile('filename.txt', 'utf8', (err, data) => {
                if (err) {
                    console.error(err);
                    return;
                }
                console.log(data);
            });
            
            // Writing to a file in Node.js
            fs.writeFile('filename.txt', 'Hello, World!', (err) => {
                if (err) {
                    console.error(err);
                }
            });
            """,
            "api_request": """
            // Making an API request in JavaScript
            fetch('https://api.example.com/data')
                .then(response => response.json())
                .then(data => console.log(data))
                .catch(error => console.error('Error:', error));
            """
        }
    }
    
    if language in snippets and category in snippets[language]:
        return snippets[language][category]
    else:
        return f"No snippet found for {language}/{category}"

# Add a tool to format code
@mcp.tool()
def format_code(code: str, language: str) -> str:
    """Format code according to language-specific standards"""
    if language.lower() == "python":
        try:
            import black
            formatted_code = black.format_str(code, mode=black.Mode())
            return formatted_code
        except ImportError:
            return "Black formatter not installed. Please install with 'pip install black'"
        except Exception as e:
            return f"Error formatting Python code: {str(e)}"
    else:
        return "Formatting not supported for this language yet"

if __name__ == "__main__":
    mcp.run()
```

### 3. Enhance the MCP Server with Git Integration

Let's add Git integration to our MCP server:

```python
# Add Git-related tools
@mcp.tool()
def git_status(repo_path: str) -> str:
    """Get the status of a Git repository"""
    import subprocess
    try:
        result = subprocess.run(
            ["git", "-C", repo_path, "status"],
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        return f"Error: {e.stderr}"

@mcp.tool()
def git_diff(repo_path: str, file_path: str = None) -> str:
    """Get the diff of changes in a Git repository"""
    import subprocess
    try:
        cmd = ["git", "-C", repo_path, "diff"]
        if file_path:
            cmd.append(file_path)
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout if result.stdout else "No changes detected"
    except subprocess.CalledProcessError as e:
        return f"Error: {e.stderr}"
```

### 4. Add Project-Specific Context Tools

Let's add tools to provide project-specific context:

```python
@mcp.tool()
def project_summary(project_path: str) -> str:
    """Generate a summary of the project structure and key files"""
    import os
    import subprocess
    
    try:
        # Get list of files
        file_list = []
        for root, dirs, files in os.walk(project_path):
            # Skip hidden directories and virtual environments
            dirs[:] = [d for d in dirs if not d.startswith('.') and d != '.venv' and d != 'node_modules']
            
            for file in files:
                # Skip hidden files and compiled files
                if not file.startswith('.') and not file.endswith('.pyc'):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, project_path)
                    file_list.append(rel_path)
        
        # Get git info if available
        git_info = ""
        try:
            result = subprocess.run(
                ["git", "-C", project_path, "remote", "-v"],
                capture_output=True,
                text=True,
                check=True
            )
            git_info = f"Git Remote:\n{result.stdout}\n"
        except:
            git_info = "No Git information available\n"
        
        # Look for key files
        key_files = []
        for file in file_list:
            if file in ['README.md', 'package.json', 'setup.py', 'requirements.txt', 'pyproject.toml']:
                key_files.append(file)
        
        # Generate summary
        summary = f"Project Summary for {os.path.basename(project_path)}\n\n"
        summary += git_info
        summary += f"Total files: {len(file_list)}\n\n"
        
        if key_files:
            summary += "Key Files:\n"
            for file in key_files:
                summary += f"- {file}\n"
        
        return summary
    
    except Exception as e:
        return f"Error generating project summary: {str(e)}"

@mcp.resource("project://{project_path}/file/{file_path}")
def get_file_content(project_path: str, file_path: str) -> str:
    """Get the content of a file in the project"""
    import os
    
    try:
        full_path = os.path.join(project_path, file_path)
        with open(full_path, 'r') as f:
            content = f.read()
        return content
    except Exception as e:
        return f"Error reading file: {str(e)}"
```

### 5. Add Code Search Capabilities

Let's add a tool to search for code patterns:

```python
@mcp.tool()
def search_code(project_path: str, pattern: str) -> str:
    """Search for a pattern in the codebase"""
    import os
    import re
    
    results = []
    
    try:
        for root, dirs, files in os.walk(project_path):
            # Skip hidden directories and virtual environments
            dirs[:] = [d for d in dirs if not d.startswith('.') and d != '.venv' and d != 'node_modules']
            
            for file in files:
                # Skip binary and non-text files
                if file.endswith(('.py', '.js', '.ts', '.html', '.css', '.md', '.txt', '.json')):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, project_path)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        matches = re.finditer(pattern, content)
                        for match in matches:
                            line_no = content[:match.start()].count('\n') + 1
                            line = content.split('\n')[line_no - 1]
                            results.append(f"{rel_path}:{line_no}: {line.strip()}")
                    except:
                        # Skip files that can't be read as text
                        pass
        
        if results:
            return f"Found {len(results)} matches:\n\n" + "\n".join(results)
        else:
            return f"No matches found for pattern: {pattern}"
    
    except Exception as e:
        return f"Error searching code: {str(e)}"
```

### 6. Create Configuration Files

Let's create a configuration file for our MCP server:

```python
# config.py
import os

# Project configuration
PROJECT_ROOT = os.path.expanduser("~/Projects/mcp-dev-server")
DEFAULT_LANGUAGE = "python"

# Git configuration
GIT_ENABLED = True

# Code snippets configuration
SNIPPETS_DIR = os.path.join(PROJECT_ROOT, "snippets")
```

### 7. Test the MCP Server

Let's create a test script to verify our MCP server works:

```python
# test_server.py
from mcp.client import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import asyncio

async def test_mcp_server():
    # Create server parameters for stdio connection
    server_params = StdioServerParameters(
        command="python",
        args=["server.py"],
        env=None,
    )
    
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize the connection
            await session.initialize()
            
            # List available tools
            tools = await session.list_tools()
            print("Available tools:")
            for tool in tools:
                print(f"- {tool.name}: {tool.description}")
            
            # List available resources
            resources = await session.list_resources()
            print("\nAvailable resources:")
            for resource in resources:
                print(f"- {resource.name}: {resource.description}")
            
            # Test a tool
            print("\nTesting 'project_summary' tool...")
            result = await session.call_tool("project_summary", {"project_path": "."})
            print(result)

if __name__ == "__main__":
    asyncio.run(test_mcp_server())
```

### 8. Configure Claude Desktop Integration

Create a configuration file for Claude Desktop:

```json
{
  "mcpServers": {
    "code-assistant": {
      "command": "python",
      "args": ["-m", "server"],
      "env": {
        "PROJECT_ROOT": "/home/<USER>/Projects/mcp-dev-server"
      }
    }
  }
}
```

### 9. Extend with Additional Features

Here are some additional features we can add to our MCP server:

1. **Documentation Generation**:
```python
@mcp.tool()
def generate_docstring(code: str, style: str = "google") -> str:
    """Generate a docstring for a Python function"""
    import ast
    
    try:
        parsed = ast.parse(code)
        if not parsed.body or not isinstance(parsed.body[0], ast.FunctionDef):
            return "Error: Input is not a valid Python function"
        
        func = parsed.body[0]
        func_name = func.name
        args = [arg.arg for arg in func.args.args]
        
        if style == "google":
            docstring = f'"""{func_name}\n\n'
            
            if args:
                docstring += "Args:\n"
                for arg in args:
                    docstring += f"    {arg}: Description of {arg}\n"
            
            docstring += "\nReturns:\n    Description of return value\n"
            docstring += '"""'
            
            return docstring
        else:
            return "Only Google style docstrings are supported at the moment"
    
    except Exception as e:
        return f"Error generating docstring: {str(e)}"
```

2. **Code Quality Check**:
```python
@mcp.tool()
def check_code_quality(code: str, language: str) -> str:
    """Check code quality using language-specific linters"""
    import tempfile
    import os
    import subprocess
    
    try:
        with tempfile.NamedTemporaryFile(suffix=f".{language}", delete=False) as temp:
            temp.write(code.encode('utf-8'))
            temp_path = temp.name
        
        if language == "python":
            # Run flake8
            try:
                result = subprocess.run(
                    ["flake8", temp_path],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    quality_report = "No issues found with flake8!"
                else:
                    quality_report = f"Flake8 found issues:\n{result.stdout}"
                
                return quality_report
            
            except FileNotFoundError:
                return "Flake8 is not installed. Install with 'pip install flake8'"
        else:
            return f"Code quality check not supported for {language}"
    
    except Exception as e:
        return f"Error checking code quality: {str(e)}"
    finally:
        if os.path.exists(temp_path):
            os.unlink(temp_path)
```

### 10. Deployment and Integration

1. **Install the MCP server**:
```bash
cd ~/Projects/mcp-dev-server
uv pip install -e .
```

2. **Configure Claude Desktop**:
   - Update the Claude Desktop configuration file with the path to our MCP server
   - Test the integration by asking Claude to use our custom tools

3. **Create a startup script**:
```bash
#!/bin/bash
# start_mcp_server.sh
cd ~/Projects/mcp-dev-server
source .venv/bin/activate
python -m server
```

4. **Make the script executable**:
```bash
chmod +x start_mcp_server.sh
```

## Final Implementation Plan

1. Set up the development environment with Python and the MCP SDK
2. Create the basic MCP server with code analysis tools
3. Add Git integration for repository management
4. Implement project context tools for better understanding of codebases
5. Add code search capabilities for finding patterns
6. Create configuration files for customization
7. Test the MCP server with a test script
8. Configure Claude Desktop integration
9. Extend with additional features like documentation generation and code quality checks
10. Deploy and integrate with your workflow

This plan provides a comprehensive approach to setting up a personalized MCP server for better LLM code assistant workflows. The server will provide tools for code analysis, Git integration, project context, and code search, making it a powerful assistant for your development work.
