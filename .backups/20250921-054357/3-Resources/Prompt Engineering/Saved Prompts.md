---
creation_date: 2025-06-04
modification_date: 2025-06-04
type: resource
source: jordan
tags: [para/resources, reference]
area: Software-Development
difficulty: easy
url: 
---

# Saved Prompts

## Overview
<!-- Brief description of this resource -->

## Key Points
<!-- Main takeaways or important information -->
-  For making templates for an area, such as finance:
`
```
can you tailor a finances billing template for the obsidian templator plugin, specifically for my clients as a web developer. My obsidian vault is conforming to a PARA structure, with finance as an area. i want to include all the web services in it for accounting and transparency with the client. I need a good foundation for financial record keeping for efficient invoicing to my client, aswell as tracking and visualising profit loss and expenses and income caused by my work with this client. As you can see from existing files in my vault, I am using dataview, templater, tasks, calendar and many other community plugins that i want to integrate into my templates. So please ensure you do so too``
```

## Details
<!-- Detailed information -->

## Examples
<!-- Examples or code snippets if applicable -->
```
// Code example
```

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[Saved Prompts]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[Saved Prompts]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "reference") AND file.name != "Saved Prompts"
LIMIT 5
```

## Notes
<!-- Any additional notes -->

## Quick Links
- [[Saved Prompts Project|New Project]]
- [[Saved Prompts Reference|Quick Reference]]
- [[3-Resources|All Resources]]
