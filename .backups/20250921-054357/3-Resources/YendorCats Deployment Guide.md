---
creation_date: 2025-04-27
modification_date: 2025-04-27
type: resource
source: Project documentation
tags: [para/resources, guide, software-dev, deployment, enhance, cloudflare, docker]
area: Software-Development
difficulty: medium
url: https://enhance.com/docs
---

# YendorCats Deployment Guide

## Overview
This guide details the deployment process for the YendorCats application using Enhance Control Panel on an Ubuntu 24.04 VPS server. It covers the setup of Docker containers, Backblaze B2 storage, MariaDB database, and Cloudflare integration for CDN, WAF, and DNS services.

## Key Points
- Deployment managed with Enhance Control Panel
- Docker containers for all services
- MariaDB database for data storage
- Backblaze B2 for S3-compatible storage
- Cloudflare for CDN, WAF, and DNS
- HTTPS for secure communication

## Prerequisites

Before deploying the YendorCats application, ensure you have:

1. **Ubuntu 24.04 VPS Server** with Enhance Control Panel installed
2. **Backblaze B2 Account** with an S3-compatible bucket
3. **Cloudflare Account** with your domain added
4. **Domain Name** registered with Cloudflare
5. **SSH Access** to your VPS server

## Deployment Steps

### 1. Prepare Your Environment

1. Clone the repository to your local machine:
   ```bash
   git clone <repository-url>
   cd yendorcats
   ```

2. Create a `.env` file based on the `.env.example` file:
   ```bash
   cp .env.example .env
   ```

3. Edit the `.env` file with your credentials:
   ```bash
   # Database Configuration
   MYSQL_ROOT_PASSWORD=your-secure-root-password
   MYSQL_USER=yendorcats
   MYSQL_PASSWORD=your-secure-password

   # S3 Configuration
   AWS_S3_ACCESS_KEY=your-backblaze-b2-application-key-id
   AWS_S3_SECRET_KEY=your-backblaze-b2-application-key
   ```

### 2. Deploy Using Enhance Control Panel

1. SSH into your Ubuntu 24.04 VPS server where Enhance Control Panel is installed.

2. Upload your project files to the server:
   ```bash
   # From your local machine
   scp -r /path/to/yendorcats user@your-server-ip:/path/to/destination
   ```

3. Log in to Enhance Control Panel:
   ```bash
   enhance login
   ```

4. Navigate to your project directory:
   ```bash
   cd /path/to/destination/yendorcats
   ```

5. Deploy the application:
   ```bash
   enhance deploy
   ```

6. Follow the prompts to configure your deployment:
   - Select the domain name for your application
   - Choose the services to deploy (API, DB, Uploader)
   - Configure environment variables
   - Set up SSL certificates

### 3. Configure Backblaze B2

1. Create a Backblaze B2 bucket if you haven't already:
   - Log in to your Backblaze B2 account
   - Create a new bucket named `yendorcats-images`
   - Set the bucket to be public
   - Enable CORS for the bucket

2. Create an application key with read and write access to the bucket:
   - Go to App Keys in your Backblaze B2 account
   - Create a new application key with access to the `yendorcats-images` bucket
   - Note the application key ID and application key

3. Update the `.env` file on your server with the Backblaze B2 credentials:
   ```bash
   nano .env
   ```

4. Restart the application:
   ```bash
   enhance restart
   ```

---

BackBlaze Configuration Values for the master key with unrestricted access to all buckets for PaceySpace #backblaze #buckets #b2-master-key:
![[Pasted image **************.png]]
### 4. Configure Cloudflare

1. Add your domain to Cloudflare:
   - Log in to your Cloudflare account
   - Add your domain if you haven't already
   - Update your domain's nameservers to Cloudflare's nameservers

2. Configure DNS records:
   - Add an A record pointing to your VPS server's IP address
   - Add CNAME records for any subdomains (e.g., `www`, `api`, `uploader`)

3. Enable Cloudflare's WAF:
   - Go to Security > WAF in your Cloudflare dashboard
   - Enable the free WAF plan
   - Configure WAF rules to protect your application

4. Configure SSL/TLS:
   - Go to SSL/TLS in your Cloudflare dashboard
   - Set the SSL/TLS encryption mode to "Full (strict)"
   - Enable "Always Use HTTPS"
   - Enable "Automatic HTTPS Rewrites"

5. Configure Caching:
   - Go to Caching in your Cloudflare dashboard
   - Configure caching rules for static content
   - Set appropriate cache TTLs for different content types

### 5. Configure Enhance Control Panel for Cloudflare

1. Add your Cloudflare API token to Enhance Control Panel:
   ```bash
   enhance config set cloudflare.api_token your-cloudflare-api-token
   ```

2. Configure Enhance to use Cloudflare for DNS:
   ```bash
   enhance config set dns.provider cloudflare
   ```

3. Update your domain configuration:
   ```bash
   enhance domain update yourdomain.com --cloudflare
   ```

### 6. Verify Deployment

1. Check the status of your services:
   ```bash
   enhance status
   ```

2. View the logs to ensure everything is running correctly:
   ```bash
   enhance logs
   ```

3. Test the application by accessing your domain in a web browser.

## Docker Compose Configuration

The YendorCats application uses the following Docker Compose configuration:

```yaml
version: '3.8'

services:
  # Backend API service
  api:
    build:
      context: ./backend/YendorCats.API
      dockerfile: Dockerfile
    image: yendorcats/api:latest
    container_name: yendorcats-api
    restart: unless-stopped
    ports:
      - "5000:80"
      - "5001:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80;https://+:443
      - AWS__Region=us-west-004
      - AWS__UseCredentialsFromSecrets=true
      - AWS__S3__BucketName=yendorcats-images
      - AWS__S3__UseDirectS3Urls=true
      - AWS__S3__ServiceUrl=https://s3.us-west-004.backblazeb2.com
      - AWS__S3__PublicUrl=https://f004.backblazeb2.com/file/yendorcats-images/{key}
      - AWS__S3__UseCdn=false
    volumes:
      - api-data:/app/Data
      - api-logs:/app/Logs
    depends_on:
      - db
    networks:
      - yendorcats-network

  # MariaDB Database
  db:
    image: mariadb:10.11
    container_name: yendorcats-db
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=YendorCats
    volumes:
      - mariadb-data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - yendorcats-network
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # File Upload Service
  uploader:
    build:
      context: ./tools/file-uploader
      dockerfile: Dockerfile
    image: yendorcats/uploader:latest
    container_name: yendorcats-uploader
    restart: unless-stopped
    ports:
      - "5002:80"
    environment:
      - AWS_S3_BUCKET_NAME=yendorcats-images
      - AWS_S3_REGION=us-west-004
      - AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
      - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
      - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
      - API_BASE_URL=http://api
    depends_on:
      - api
    networks:
      - yendorcats-network

networks:
  yendorcats-network:
    driver: bridge

volumes:
  api-data:
  api-logs:
  mariadb-data:
```

## Enhance Control Panel Commands

Here are some useful Enhance Control Panel commands for managing your deployment:

- **Deploy the application**: `enhance deploy`
- **View logs**: `enhance logs`
- **Restart services**: `enhance restart`
- **Stop services**: `enhance stop`
- **Start services**: `enhance start`
- **Update configuration**: `enhance config set key value`
- **Add a domain**: `enhance domain add yourdomain.com`
- **Update a domain**: `enhance domain update yourdomain.com --option value`
- **Backup the database**: `enhance backup db`
- **Restore the database**: `enhance restore db <backup-file>`

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Check your MariaDB credentials in the `.env` file.
   - Ensure the MariaDB service is running.

2. **S3 Upload Errors**:
   - Verify your Backblaze B2 credentials.
   - Check that the bucket exists and is accessible.
   - Ensure the application key has the correct permissions.

3. **Docker Issues**:
   - Check Docker logs: `enhance logs`
   - Ensure Docker and Docker Compose are installed correctly.

4. **Cloudflare Issues**:
   - Verify your Cloudflare API token.
   - Check that your domain's nameservers are set to Cloudflare's nameservers.
   - Ensure your DNS records are correctly configured.

### Getting Help

If you encounter issues not covered in this guide, please:

1. Check the Enhance documentation: https://enhance.com/docs
2. Contact your system administrator
3. Open an issue in the project repository

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[YendorCats Deployment Guide]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[YendorCats Deployment Guide]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "deployment") OR contains(tags, "enhance") OR contains(tags, "cloudflare") OR contains(tags, "docker")
LIMIT 5
```

## Quick Links
- [[YendorCats Project Documentation|Back to Project]]
- [[YendorCats Cloudflare Integration|Cloudflare Integration Guide]]
- [[3-Resources|All Resources]]
