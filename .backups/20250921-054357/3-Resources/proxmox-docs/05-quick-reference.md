# Proxmox Quick Reference Card

## Access Methods

### Web Interface
```
URL: https://[PROXMOX-IP]:8006
Username: root
Password: [your-password]
```

### SSH Access
```bash
ssh root@[PROXMOX-IP]
```

## Essential Commands

### VM Management
```bash
qm list                    # List VMs
qm start 100              # Start VM
qm shutdown 100           # Graceful shutdown
qm stop 100               # Force stop
qm config 100             # Show config
qm set 100 -memory 4096   # Change RAM
qm snapshot 100 snap1     # Create snapshot
qm rollback 100 snap1     # Restore snapshot
```

### Container Management
```bash
pct list                  # List containers
pct start 200            # Start container
pct enter 200            # Enter console
pct config 200           # Show config
pct snapshot 200 snap1   # Create snapshot
```

### Storage & Backup
```bash
pvesm status             # Storage status
vzdump 100               # Backup VM
vzdump --all             # Backup everything
qmrestore backup.vma 101 # Restore backup
```

### Network
```bash
brctl show               # Show bridges
ip addr show             # Show interfaces
```

## Web UI Navigation

```
Datacenter
├── Search
├── Summary
├── Cluster
├── Options
├── Storage
├── Backup
├── Replication
├── Permissions
│   ├── Users
│   ├── API Tokens
│   ├── Groups
│   └── Roles
└── Firewall

Node (pve)
├── Summary
├── Shell
├── System
│   ├── Network
│   ├── DNS
│   ├── Hosts
│   └── Time
├── Updates
├── Firewall
├── Disks
├── Ceph (if configured)
└── Task History

VM/Container
├── Summary
├── Console
├── Hardware (VM) / Resources (CT)
├── Options
├── Task History
├── Monitor (VM only)
├── Backup
├── Snapshots
├── Firewall
└── Permissions
```

## Default Paths

```
/etc/pve/                 # Proxmox configuration
/var/lib/vz/              # Default storage
/var/lib/vz/template/iso/ # ISO images
/var/lib/vz/template/cache/ # CT templates
/var/lib/vz/images/       # VM disks
/var/lib/vz/dump/         # Backups
```

## Common Tasks

### Upload ISO
1. Node → local → ISO Images → Upload
2. Or: `scp file.iso root@pve:/var/lib/vz/template/iso/`

### Create VM
1. Click "Create VM"
2. Follow wizard
3. Don't forget QEMU Guest Agent

### Create Container
1. Click "Create CT"
2. Choose template
3. Configure resources

### Take Snapshot
1. Select VM/CT → Snapshots
2. Click "Take Snapshot"
3. Name and describe

### Schedule Backup
1. Datacenter → Backup
2. Add backup job
3. Select targets and schedule

## Troubleshooting

### VM Won't Start
```bash
qm unlock 100            # Remove lock
qm rescan               # Rescan storage
journalctl -xe          # Check logs
```

### No Network
```bash
systemctl restart networking
brctl show
qm config 100 | grep net
```

### Storage Full
```bash
df -h
du -sh /var/lib/vz/*
apt clean
```

### High CPU/Memory
```bash
top
htop
qm monitor 100
```

## Important URLs

- Web UI: https://\[IP\]:8006
- Wiki: https://pve.proxmox.com/wiki
- Forum: https://forum.proxmox.com
- Downloads: https://proxmox.com/downloads

## Emergency Commands

```bash
# Stop all VMs
for i in $(qm list | awk 'NR>1 {print $1}'); do qm stop $i; done

# Start all VMs
for i in $(qm list | awk 'NR>1 {print $1}'); do qm start $i; done

# Backup all NOW
vzdump --all --compress lzo --storage local

# Check system health
pveversion -v
systemctl --failed
journalctl -p err -n 50
```

## Remember

1. **Always backup before major changes**
2. **Install QEMU Guest Agent in VMs**
3. **Use VirtIO drivers when possible**
4. **Monitor resource usage regularly**
5. **Keep system updated**
6. **Document your setup**
