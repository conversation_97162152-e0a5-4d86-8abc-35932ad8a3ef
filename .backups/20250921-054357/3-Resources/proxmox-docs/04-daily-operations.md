# Proxmox Daily Operations and Maintenance

## Daily Tasks Checklist

### Morning Routine (5 minutes)
```bash
# Quick health check
pvesh get /cluster/resources
qm list
pct list
pvesm status
df -h
```

### Weekly Tasks
- [ ] Check for updates
- [ ] Review backup logs
- [ ] Check disk usage
- [ ] Review system logs
- [ ] Test backup restoration
- [ ] Clean old snapshots

### Monthly Tasks
- [ ] Apply updates
- [ ] Review resource usage trends
- [ ] Clean old ISOs/templates
- [ ] Security audit
- [ ] Documentation update

## Essential Commands

### VM Operations
```bash
# List all VMs with status
qm list

# Start/Stop/Restart
qm start 100
qm shutdown 100  # Graceful
qm stop 100      # Force
qm reset 100     # Restart

# Configuration
qm config 100
qm set 100 -memory 4096
qm set 100 -cores 4

# Snapshots
qm snapshot 100 snap1 --description "Before upgrade"
qm rollback 100 snap1
qm delsnapshot 100 snap1

# Clone
qm clone 100 101 --name newvm --full

# Backup
vzdump 100 --compress lzo --storage local
```

### Container Operations
```bash
# List containers
pct list

# Start/Stop
pct start 200
pct shutdown 200
pct stop 200

# Enter container
pct enter 200

# Configuration
pct config 200
pct set 200 -memory 1024
pct set 200 -cores 2

# Snapshots
pct snapshot 200 snap1
pct rollback 200 snap1

# Backup
vzdump 200 --compress lzo
```

### Storage Management
```bash
# Check storage status
pvesm status

# List storage content
pvesm list local

# Disk usage
df -h
du -sh /var/lib/vz/*

# Clean old backups
find /var/lib/vz/dump/ -name "*.vma.lzo" -mtime +30 -delete
```

## Monitoring

### Resource Monitoring
```bash
# Overall cluster status
pvesh get /cluster/resources --type vm
pvesh get /nodes/pve/status

# CPU and Memory
top
htop
vmstat 1

# Disk I/O
iostat -x 1
iotop

# Network
iftop
nethogs
```

### Log Files
```bash
# System logs
tail -f /var/log/syslog
journalctl -f

# Proxmox specific
tail -f /var/log/pve/tasks/active
cat /var/log/pve-firewall.log

# VM logs
cat /var/log/pve/qemu-server/100.log
```

## Backup Management

### Manual Backup
```bash
# Single VM/Container
vzdump 100 --storage local --compress lzo

# All VMs and Containers
vzdump --all --storage local

# With specific options
vzdump 100 \
  --compress zstd \
  --mode snapshot \
  --mailto <EMAIL> \
  --notes "Weekly backup"
```

### Scheduled Backups
Via Web UI:
1. Datacenter → Backup
2. Add backup job
3. Configure schedule

Via CLI:
```bash
# View backup jobs
cat /etc/pve/vzdump.cron

# Add backup job (runs at 2 AM daily)
echo "0 2 * * * root vzdump 100 200 --quiet --compress lzo --storage backup --mode snapshot --mailto <EMAIL>" >> /etc/cron.d/vzdump
```

### Restore Operations
```bash
# List backups
ls -la /var/lib/vz/dump/

# Restore VM
qmrestore vzdump-qemu-100-2024_01_01-02_00_00.vma.lzo 101

# Restore Container
pct restore 201 vzdump-lxc-200-2024_01_01-02_00_00.tar.lzo

# Restore to different storage
qmrestore backup.vma.lzo 101 --storage local-lvm
```

## Updates and Maintenance

### System Updates
```bash
# Check for updates
apt update
apt list --upgradable

# Proxmox version
pveversion -v

# Apply updates (during maintenance window)
apt update && apt dist-upgrade -y

# Reboot if kernel updated
reboot
```

### Repository Management
```bash
# Non-subscription repository
echo "deb http://download.proxmox.com/debian/pve bookworm pve-no-subscription" > /etc/apt/sources.list.d/pve-no-subscription.list

# Disable enterprise repository
sed -i 's/^deb/#deb/' /etc/apt/sources.list.d/pve-enterprise.list

apt update
```

## Troubleshooting

### Common Issues

#### VM Won't Start
```bash
# Check error
qm start 100

# View logs
journalctl -xe | grep "VM 100"

# Check resources
free -h
pvesm status

# Remove lock
qm unlock 100
```

#### High CPU Usage
```bash
# Find culprit
top -b -n 1 | head -20

# Check VM CPU
qm monitor 100
info cpus
quit

# Limit VM CPU
qm set 100 --cpulimit 2
```

#### Disk Space Issues
```bash
# Find large files
du -h /var/lib/vz | sort -rh | head -20

# Clean package cache
apt clean

# Remove old kernels
apt autoremove --purge

# Clean old backups
ls -lah /var/lib/vz/dump/
# Remove old ones manually
```

#### Network Problems
```bash
# Check interfaces
ip addr show
brctl show

# Restart networking
systemctl restart networking

# Check VM network
qm config 100 | grep net

# Test connectivity
ping -c 4 *******
```

## Performance Optimization

### VM Optimization
```bash
# Set CPU type to host
qm set 100 --cpu host

# Enable NUMA
qm set 100 --numa 1

# Use VirtIO drivers
qm set 100 --scsihw virtio-scsi-pci
qm set 100 --net0 virtio,bridge=vmbr0

# Memory ballooning
qm set 100 --balloon 1024
```

### Storage Optimization
```bash
# Enable discard for SSDs
qm set 100 --scsi0 local-lvm:vm-100-disk-0,discard=on

# Thin provisioning check
lvs

# Cache settings
qm set 100 --scsi0 local-lvm:vm-100-disk-0,cache=writeback
```

## Security

### Firewall Management
```bash
# Enable datacenter firewall
pvesh set /cluster/firewall/options --enable 1

# Add rule
pvesh create /cluster/firewall/rules \
  --type in \
  --action ACCEPT \
  --source ***********/24 \
  --dport 8006 \
  --proto tcp

# VM specific firewall
qm set 100 --firewall 1
```

### User Management
```bash
# List users
pveum user list

# Add user
pveum user add john@pve --password

# Set permissions
pveum acl modify / --users john@pve --roles PVEVMUser

# Enable 2FA
pveum user modify john@pve --totp
```

### SSH Hardening
```bash
# Edit SSH config
nano /etc/ssh/sshd_config

# Key settings:
PermitRootLogin prohibit-password
PasswordAuthentication no
Port 22222

# Restart SSH
systemctl restart sshd
```

## Automation Scripts

### Daily Health Check
```bash
#!/bin/bash
# /root/daily-check.sh

echo "=== Proxmox Daily Report - $(date) ==="
echo ""
echo "--- System Resources ---"
free -h
df -h
echo ""
echo "--- VM Status ---"
qm list
echo ""
echo "--- Container Status ---"
pct list
echo ""
echo "--- Storage ---"
pvesm status
echo ""
echo "--- Failed Services ---"
systemctl --failed
echo ""
echo "--- Recent Errors ---"
journalctl -p err --since "24 hours ago" | tail -20
```

### Backup All VMs
```bash
#!/bin/bash
# /root/backup-all.sh

LOG="/var/log/backup-$(date +%Y%m%d).log"
echo "Starting backup - $(date)" >> $LOG

for vmid in $(qm list | awk 'NR>1 {print $1}'); do
    echo "Backing up VM $vmid" >> $LOG
    vzdump $vmid --compress lzo --storage local --mode snapshot >> $LOG 2>&1
done

for ctid in $(pct list | awk 'NR>1 {print $1}'); do
    echo "Backing up CT $ctid" >> $LOG
    vzdump $ctid --compress lzo --storage local --mode snapshot >> $LOG 2>&1
done

echo "Backup completed - $(date)" >> $LOG
```

### Resource Report
```bash
#!/bin/bash
# /root/resource-report.sh

echo "=== Resource Usage Report ==="
echo ""
echo "VMs:"
for vmid in $(qm list | awk 'NR>1 {print $1}'); do
    echo -n "VM $vmid: "
    qm status $vmid --verbose | grep -E "cpu|mem"
done

echo ""
echo "Containers:"
for ctid in $(pct list | awk 'NR>1 {print $1}'); do
    echo -n "CT $ctid: "
    pct status $ctid --verbose | grep -E "cpu|mem"
done
```

## Quick Tips

### Console Access
- Web console: noVNC
- SPICE: Better performance
- Serial: For debugging

### Migrations
```bash
# Online migration (shared storage)
qm migrate 100 node2 --online

# Offline migration
qm migrate 100 node2
```

### Templates
```bash
# Convert to template
qm template 100

# Clone from template
qm clone 100 101 --name new-vm
```

### API Usage
```bash
# Get VM status
curl -k -H "Authorization: PVEAPIToken=user@realm!token=UUID" \
  https://localhost:8006/api2/json/nodes/pve/qemu/100/status/current

# Start VM via API
curl -k -X POST -H "Authorization: PVEAPIToken=user@realm!token=UUID" \
  https://localhost:8006/api2/json/nodes/pve/qemu/100/status/start
```

## Emergency Procedures

### System Recovery
```bash
# Boot from Proxmox ISO
# Select "Advanced Options" → "Rescue Boot"

# Mount root filesystem
mount /dev/mapper/pve-root /mnt
chroot /mnt

# Fix issues
apt --fix-broken install
update-grub
```

### VM Recovery
```bash
# If VM config corrupted
cp /etc/pve/qemu-server/100.conf.bak /etc/pve/qemu-server/100.conf

# Force stop hung VM
ps aux | grep "kvm -id 100"
kill -9 [PID]

# Recover from snapshot
qm rollback 100 working-snapshot
```

## Important Files and Directories

```
/etc/pve/                    # Cluster configuration
/etc/pve/qemu-server/        # VM configs
/etc/pve/lxc/               # Container configs
/var/lib/vz/                # Storage root
/var/lib/vz/images/         # VM disks
/var/lib/vz/template/       # ISO and templates
/var/lib/vz/dump/           # Backups
/var/log/pve/               # Proxmox logs
```

## Remember

1. **Always backup before major changes**
2. **Document your configuration**
3. **Monitor resource usage**
4. **Keep system updated**
5. **Test restore procedures**
6. **Plan maintenance windows**
