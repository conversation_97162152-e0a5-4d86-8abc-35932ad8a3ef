# Proxmox VE Installation Guide

## ⚠️ IMPORTANT WARNING
**Installing Proxmox VE will COMPLETELY ERASE the target machine's hard drive(s) and replace the existing operating system. Make sure to backup all important data before proceeding!**

## Pre-Installation Checklist

### 1. Hardware Requirements Verification
```bash
# Check CPU virtualization support (run on target machine)
egrep -c '(vmx|svm)' /proc/cpuinfo  # Should return > 0

# Check system info
lscpu | grep -E 'Model name|Socket|Core|Thread|CPU MHz|Virtualization'
free -h  # Check RAM
lsblk    # Check storage devices
```

### 2. BIOS/UEFI Settings
Enter BIOS/UEFI (usually F2, F10, F12, or DEL during boot) and enable:
- Intel VT-x or AMD-V (CPU Virtualization)
- Intel VT-d or AMD IOMMU (for PCIe passthrough)
- Disable Secure Boot (may cause issues)
- Set boot mode (UEFI recommended for new systems)

### 3. Network Information
Gather before installation:
- IP address for Proxmox host
- Subnet mask
- Gateway IP
- DNS servers
- Hostname for the server

## Creating Installation Media

### Download Proxmox VE ISO
1. Visit: https://www.proxmox.com/en/downloads
2. Download latest Proxmox VE ISO installer
3. Verify checksum:
```bash
# On Linux/Mac
sha256sum proxmox-ve_*.iso
# Compare with checksum on download page
```

### Create Bootable USB (From Linux/Mac)
```bash
# Find USB device
lsblk  # or diskutil list on Mac

# Write ISO to USB (replace /dev/sdX with your USB device)
# WARNING: This will erase the USB drive!
sudo dd if=proxmox-ve_*.iso of=/dev/sdX bs=1M status=progress conv=fsync

# On Mac:
sudo dd if=proxmox-ve_*.iso of=/dev/rdiskX bs=1m
```

### Create Bootable USB (From Windows)
Use Rufus or balenaEtcher:
1. Download Rufus: https://rufus.ie
2. Select USB drive
3. Select Proxmox ISO
4. Use DD mode when prompted
5. Write the image

## Installation Process

### Step 1: Boot from USB
1. Insert USB into target machine
2. Power on and enter boot menu (F8, F11, F12, or ESC)
3. Select USB drive
4. Proxmox installer will load

### Step 2: Installation Wizard

#### Screen 1: Welcome
- Click "I agree" to EULA
- Click "Next"

#### Screen 2: Target Hard Disk
- Select installation disk
- Click "Options" for advanced settings:
  - **Filesystem**: 
    - ext4: Standard, reliable
    - xfs: Better for large files
    - ZFS: Advanced features (RAID, compression, snapshots)
  - **Disk partitioning**: Usually leave default
- Click "Next"

#### Screen 3: Location and Time Zone
- Select Country
- Select Time zone
- Select Keyboard layout
- Click "Next"

#### Screen 4: Administrator Password and Email
- Set root password (STRONG password!)
- Enter email for notifications
- Click "Next"

#### Screen 5: Network Configuration
- **Management Interface**: Select primary network card
- **Hostname**: Enter FQDN (e.g., pve.homelab.local)
- **IP Address**: Static IP (e.g., *************)
- **Netmask**: Usually ************* (/24)
- **Gateway**: Your router IP (e.g., ***********)
- **DNS Server**: Your DNS server (e.g., *********** or *******)
- Click "Next"

#### Screen 6: Summary
- Review all settings
- Click "Install"
- Installation begins (5-10 minutes)

### Step 3: First Boot
1. Remove USB drive
2. System will reboot
3. You'll see console with IP address
4. Note the URL: https://\[IP-ADDRESS\]:8006

## Post-Installation Access

### Web Interface Access
1. From any browser: https://\[IP-ADDRESS\]:8006
2. Accept security certificate warning
3. Login:
   - Username: root
   - Password: [password set during installation]
   - Realm: Linux PAM standard authentication

### SSH Access
```bash
# From your desktop/laptop
ssh root@[IP-ADDRESS]
```

### Console Access
- Physical keyboard/monitor on the server
- Username: root
- Password: [your password]

## Initial Configuration Tasks

### 1. Update System
```bash
# Via SSH or Shell in web UI
apt update && apt dist-upgrade -y
pveupdate  # Check for Proxmox updates
```

### 2. Configure Repositories
For non-subscription use:
```bash
# Disable enterprise repository
sed -i 's/^deb/#deb/g' /etc/apt/sources.list.d/pve-enterprise.list

# Add no-subscription repository
echo "deb http://download.proxmox.com/debian/pve bookworm pve-no-subscription" > /etc/apt/sources.list.d/pve-no-subscription.list

apt update
```

### 3. Configure Storage
Default storage locations:
- `/var/lib/vz/`: Default storage location
- `/var/lib/vz/template/iso/`: ISO images
- `/var/lib/vz/template/cache/`: Container templates
- `/var/lib/vz/images/`: VM disk images

### 4. Upload ISOs
Via Web UI:
1. Select node → local storage
2. Click "ISO Images"
3. Click "Upload"
4. Select ISO file

Via CLI:
```bash
# Copy ISO to Proxmox
scp ubuntu-*.iso root@[IP-ADDRESS]:/var/lib/vz/template/iso/
```

### 5. Configure Firewall (Optional)
Via Web UI:
1. Datacenter → Firewall → Options
2. Enable firewall
3. Add rules as needed

### 6. Create Backup Schedule
1. Datacenter → Backup
2. Add backup job
3. Select VMs/containers
4. Set schedule

## Troubleshooting Installation Issues

### Cannot Boot from USB
- Try different USB port
- Recreate USB with different tool
- Check BIOS boot order
- Disable Secure Boot

### Network Not Working
```bash
# Check network configuration
ip addr show
cat /etc/network/interfaces
systemctl status networking
```

### Cannot Access Web Interface
```bash
# Check if service is running
systemctl status pveproxy
# Check firewall
iptables -L -n
# Test locally
curl -k https://localhost:8006
```

### Storage Issues
```bash
# Check storage status
pvesm status
# Check disk space
df -h
# Check LVM
lvs
vgs
```

## Security Hardening (Post-Installation)

### 1. Create Non-Root User
```bash
# In Proxmox web UI
# Datacenter → Permissions → Users → Add
# Create user with PVEAdmin role
```

### 2. Configure 2FA
1. Datacenter → Permissions → Two Factor
2. Add TOTP or WebAuthn

### 3. SSL Certificate
Replace self-signed certificate:
1. Datacenter → NODE → System → Certificates
2. Order Let's Encrypt certificate (if public domain)
3. Or upload custom certificate

### 4. Regular Updates
```bash
# Create update script
cat > /root/update.sh << 'SCRIPT'
#!/bin/bash
apt update
apt dist-upgrade -y
pveupdate
SCRIPT
chmod +x /root/update.sh
```

## Next Steps

1. ✅ System installed and accessible
2. → Create first VM or container
3. → Configure networking (VLANs, bridges)
4. → Set up backups
5. → Install additional tools
6. → Configure monitoring

## Recovery Options

### If Installation Fails
1. Check hardware compatibility
2. Try different installation options
3. Check installation media integrity
4. Review BIOS settings
5. Consult Proxmox forums

### Rescue Boot
If system won't boot after installation:
1. Boot from Proxmox ISO
2. Select "Advanced Options"
3. Select "Rescue Boot"
4. Mount and repair system

## Important Notes

- **Backups**: Always maintain backups before major changes
- **Documentation**: Document your configuration
- **Updates**: Keep system updated
- **Community**: Join Proxmox forums for support
- **Subscription**: Consider subscription for production use
