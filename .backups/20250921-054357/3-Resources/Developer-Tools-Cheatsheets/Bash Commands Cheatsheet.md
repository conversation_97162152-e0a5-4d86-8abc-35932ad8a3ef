---
creation_date: 2025-04-27
modification_date: 2025-04-27
type: resource
source: Personal research
tags: [para/resources, guide, cheetsheet, bash, linux, command, line, terminal, scripts, run, shell, sh, zsh, bash, mac]
related: []
area: linux
difficulty: easy
keywords: [guide, reference, cheetsheet, bash, command, terminal, linux, mac, zsh, line]
last_used: 2025-04-27
url: 
author: jordan
---

# Bash Commands Cheatsheet

## Overview
<!-- Brief description of this resource -->

## Key Points
<!-- Main takeaways or important information -->

## Details
<!-- Detailed information -->

## Examples
<!-- Examples or code snippets if applicable -->
#### Move all files except for ignoredfile
##### Moving all files in current directory to it's sub directory
```bash
find . -maxdepth 1 -mindepth 1 -not -name ignoredfile -print0 | xargs -0 mv -t yendorcats-wp/
```
#### Rsync
```bash
rsync -pav -e "ssh -i $home/.ssh/somekey" username@hostname:/from/dir/ /to/dir/
```
alternatively, setup in the hostfile in ~/.ssh/config so the key will automatically be associated with the host:
```
Host yendor-wp
 HostName ************
 IdentityFile ~/.ssh/somekey
```
##### Rsync exclude
to exclude files from rsync, create a text file with the excluded files/directory names and specify this file in the --exclude-from= flag:
```bash
rsync -av --exclude-from={'list.txt'} sourcedir/ destinationdir/
```
for current project:
```bash
cd ~/Projects/yendorcats-wp
```
```bash
rsync -Pav --exclude-from="yendorcats-wp/exclude.txt" yendorcats-wp/ ubuntu@yendor-wp:~
```
## Use Cases
<!-- When and how to use this resource -->
- 

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(related, "Bash Commands Cheatsheet") OR contains(file.content, "[[Bash Commands Cheatsheet]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category",
  status as "Status"
FROM "2-Areas"
WHERE contains(related, "Bash Commands Cheatsheet") OR contains(file.content, "[[Bash Commands Cheatsheet]]")
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "guide") AND file.name != "Bash Commands Cheatsheet"
LIMIT 5
```

## Notes
<!-- Any additional notes -->

## Create Related Notes
- [[Bash Commands Cheatsheet Project|Create Related Project]]
- [[Bash Commands Cheatsheet Implementation|Create Implementation Guide]]
- [[Bash Commands Cheatsheet Reference|Create Quick Reference]]

## Related
- [[3-Resources]]
- [[Resources TOC]]
- [[Cheetsheets TOC]]
