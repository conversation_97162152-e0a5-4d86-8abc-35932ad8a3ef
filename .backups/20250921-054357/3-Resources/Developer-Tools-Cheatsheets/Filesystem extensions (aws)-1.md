---
creation_date: 2025-04-28
modification_date: 2025-04-28
type: resource
source: Personal research
tags:
  - para/resources
  - guide
  - filesystem
  - disk
  - increase
  - expand
  - volume
  - grow
related:
  - "[[Brain/Guides/Computer Education]]"
area: system
difficulty: medium
keywords:
  - guide
  - reference
  - linux
  - computer
  - disk
  - drive
  - extend
last_used: 2025-04-28
url: 
author: jordan
---

# Filesystem extensions (aws)

## Overview
<!-- Brief description of this resource -->

## Key Points
<!-- Main takeaways or important information -->
- growing a pysical volume's filesystem size 
- in aws, when extending a volume, you must go into thee instance and grow theee filesystem yourself. see the guide below on how to

## Details
<!-- Detailed information -->

1.  Your main disk (the EBS volume) is `/dev/xvda`, and its total size is **14G**.
2.  Your root filesystem (`/`) is on partition `/dev/xvda1`, which is currently only **7G**.
3.  There are other small partitions (`xvda14`, `xvda15` for EFI boot, `xvda16` for `/boot`).

It looks like you (or the system) extended the underlying volume (`xvda`) to 14G, but the main partition (`xvda1`) containing your root filesystem is still only using 7G of that space.

Here are the steps to resize the root filesystem (`/`) on `/dev/xvda1` to use the available space on `/dev/xvda`:

**Step 1: Check the Filesystem Type**

Find out if `/dev/xvda1` is using `ext4` or `xfs` (or something else).
```bash
df -hT /
```
Look at the `Type` column in the output.

**Step 2: Extend the Partition**

You need to tell the system to extend partition `1` on device `/dev/xvda` to fill the available space. Use the `growpart` command.

```bash
sudo growpart /dev/xvda 1
```
*(Remember the space between `xvda` and `1`)*

If you get `command not found`, install it:
* On Amazon Linux/RHEL/CentOS: `sudo yum install cloud-utils-growpart`
* On Debian/Ubuntu: `sudo apt update && sudo apt install cloud-guest-utils`

Run `lsblk` again after this command. You should see `/dev/xvda1` now reporting a size much closer to 14G (minus the space for the other small partitions).

**Step 3: Resize the Filesystem**

Now, use the command appropriate for the filesystem type you identified in Step 1:

* **If the filesystem type is `ext2`, `ext3`, or `ext4`:**
    ```bash
    sudo resize2fs /dev/xvda1
    ```

* **If the filesystem type is `xfs`:**
    ```bash
    sudo xfs_growfs -d /
    ```
    (Using the mount point `/` is preferred for `xfs_growfs`)

**Step 4: Verify the Result**

Check the size again using `df`:
```bash
df -h /
```
The `Size` column for `/` should now show the larger size (close to 14G, accounting for filesystem overhead and the space used by `/boot` and EFI partitions).
```bash
$ lsblk
NAME     MAJ:MIN RM  SIZE RO TYPE MOUNTPOINTS
loop0      7:0    0 26.3M  1 loop /snap/amazon-ssm-agent/9881
loop1      7:1    0 73.9M  1 loop /snap/core22/1908
loop2      7:2    0 73.9M  1 loop /snap/core22/1748
loop3      7:3    0 44.4M  1 loop /snap/snapd/23771
loop4      7:4    0 44.4M  1 loop /snap/snapd/23545
xvda     202:0    0   14G  0 disk 
├─xvda1  202:1    0    7G  0 part /
├─xvda14 202:14   0    4M  0 part 
├─xvda15 202:15   0  106M  0 part /boot/efi
└─xvda16 259:0    0  913M  0 part /boot
ubuntu@ip-172-31-6-30:~/yendorcats-wp$ df -hT /
Filesystem     Type  Size  Used Avail Use% Mounted on
/dev/root      ext4  6.8G  6.7G   73M  99% /
ubuntu@ip-172-31-6-30:~/yendorcats-wp$ sudo growpart /dev/xvda 1
CHANGED: partition=1 start=2099200 old: size=******** end=******** new: size=******** end=********
ubuntu@ip-172-31-6-30:~/yendorcats-wp$ lsblk
NAME     MAJ:MIN RM  SIZE RO TYPE MOUNTPOINTS
loop0      7:0    0 26.3M  1 loop /snap/amazon-ssm-agent/9881
loop1      7:1    0 73.9M  1 loop /snap/core22/1908
loop2      7:2    0 73.9M  1 loop /snap/core22/1748
loop3      7:3    0 44.4M  1 loop /snap/snapd/23771
loop4      7:4    0 44.4M  1 loop /snap/snapd/23545
xvda     202:0    0   14G  0 disk 
├─xvda1  202:1    0   13G  0 part /
├─xvda14 202:14   0    4M  0 part 
├─xvda15 202:15   0  106M  0 part /boot/efi
└─xvda16 259:0    0  913M  0 part /boot

$ sudo resize2fs /dev/xvda1
resize2fs 1.47.0 (5-Feb-2023)
Filesystem at /dev/xvda1 is mounted on /; on-line resizing required
old_desc_blocks = 1, new_desc_blocks = 2
The filesystem on /dev/xvda1 is now 3407611 (4k) blocks long.

ubuntu@ip-172-31-6-30:~/yendorcats-wp$ lsblk
NAME     MAJ:MIN RM  SIZE RO TYPE MOUNTPOINTS
loop0      7:0    0 26.3M  1 loop /snap/amazon-ssm-agent/9881
loop1      7:1    0 73.9M  1 loop /snap/core22/1908
loop2      7:2    0 73.9M  1 loop /snap/core22/1748
loop3      7:3    0 44.4M  1 loop /snap/snapd/23771
loop4      7:4    0 44.4M  1 loop /snap/snapd/23545
xvda     202:0    0   14G  0 disk 
├─xvda1  202:1    0   13G  0 part /
├─xvda14 202:14   0    4M  0 part 
├─xvda15 202:15   0  106M  0 part /boot/efi
└─xvda16 259:0    0  913M  0 part /boot
ubuntu@ip-172-31-6-30:~/yendorcats-wp$ df -h
Filesystem      Size  Used Avail Use% Mounted on
/dev/root        13G  6.7G  5.9G  53% /
tmpfs           479M     0  479M   0% /dev/shm
tmpfs           192M  912K  191M   1% /run
tmpfs           5.0M     0  5.0M   0% /run/lock
/dev/xvda16     881M  137M  683M  17% /boot
/dev/xvda15     105M  6.1M   99M   6% /boot/efi
tmpfs            96M   12K   96M   1% /run/user/1000

```

---
Connect to backblaze with master application key #backblaze #b2-master-key #s3 

![[Pasted image 20250527141021.png]]
## Examples
<!-- Examples or code snippets if applicable -->
```
// Code example
```

## Use Cases
<!-- When and how to use this resource -->
- 

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(related, "Filesystem extensions (aws)") OR contains(file.content, "[[Filesystem extensions (aws)]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category",
  status as "Status"
FROM "2-Areas"
WHERE contains(related, "Filesystem extensions (aws)") OR contains(file.content, "[[Filesystem extensions (aws)]]")
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "guide") AND file.name != "Filesystem extensions (aws)"
LIMIT 5
```

## Notes
<!-- Any additional notes -->

## Create Related Notes
- [[Filesystem extensions (aws) Project|Create Related Project]]
- [[Filesystem extensions (aws) Implementation|Create Implementation Guide]]
- [[Filesystem extensions (aws) Reference|Create Quick Reference]]

## Related
- [[3-Resources]]
- [[Resources TOC]]
- [[Guides TOC]]

