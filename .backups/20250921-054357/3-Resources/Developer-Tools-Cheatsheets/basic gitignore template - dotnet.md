---
creation_date: 2023-04-25
modification_date: 2025-07-14
description: copy and paste into a .gitignore file
tags:
  - gitignore
  - git
---
# IDE - Visual Studio / Rider
.vs/
.vscode/
.idea/
*.suo
*.user
*.userosscache
*.sln.docstates
.obsidian
wwwroot/
backend/YendorCats.API/wwwroot/

# Database
*.sqlite
*.sqlite-shm
*.sqlite-wal

# Frontend
frontend/node_modules/
frontend/.cache/

# wwwroot (static files served by backend)
backend/YendorCats.API/wwwroot/node_modules/
backend/YendorCats.API/wwwroot/package-lock.json
backend/YendorCats.API/wwwroot/**/*.bak

# Prevent cloud sync duplicates
**/* [0-9].*
**/* [0-9]/
**/*\ 2.*
**/*\ 3.*
**/*\ 4.*
**/*\ 2/
**/*\ 3/
**/*\ 4/

# Environment variables and secrets
.env
.env.local
.env.*.local
.env.production
.env.staging
*.env

# Security and credentials
appsettings.Production.json
appsettings.Staging.json
appsettings.Development.json
secrets.json
**/appsettings.*.json
!**/appsettings.json.template

# Backend
backend/**/bin/
backend/**/obj/
backend/**/*.sqlite
backend/**/*.sqlite-shm
backend/**/*.sqlite-wal

# Logs and sensitive runtime data
**/Logs/
**/*.log
**/logs/
*.log.*

# Certificates
*.key
*.crt
*.csr
backend/CabUCA.API/certs/

# Misc
.DS_Store
*.log
*.env
.env
.env.*
.env.local
.env.testing
.env.development
.crt
.csr
.key
.pfx
.p12
.pem

# Vault and secrets management
vault/
.vault-*
**/vault-*
**/*vault*token*
**/*unseal*keys*

# Backup directories (may contain sensitive data)
**/wwwroot-backup-*/
**/*backup*/

# Node.js dependencies
node_modules/
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
.npm
.yarn-integrity

# Test files and directories
test-data/
test-images/
**/test-*.py
**/test-*.js
**/test-*.sh
**/*test*/
**/*Test*/
**/*TEST*/

# Build and deployment artifacts
**/bin/
**/obj/
**/publish/
**/dist/
**/build/
*.dll
*.exe
*.pdb

# Scripts and automation (often contain sensitive data)
**/*setup*.sh
**/*deploy*.sh
**/*vault*.sh
**/*config*.sh
**/*install*.sh
**/*migration*.sh
**/*backup*.sh
**/*restore*.sh
bulk-metadata-update.sh
configure-b2-permissions.sh
deploy-vault.sh
quick-vault-setup.sh
setup-*.sh
test-*.sh

# Temporary and generated files
*.tmp
*.temp
**/tmp/
**/temp/
**/.cache/
**/.temp/
**/.tmp/

# Development tools and IDE history
.history/
**/.history/
.augment/
**/.augment/
.wakatime-project
.gitmodules

# Python files (often test/utility scripts)
**/*.pyc
**/__pycache__/
*.py
!**/essential-*.py
!**/required-*.py

# CSV and data files (may contain sensitive data)
*.csv
**/*.csv
!**/sample-*.csv
!**/template-*.csv


# Shell scoripts and automation
fix-merge-conflicts.sh
image-example-generator.sh
setup-aws-secrets.sh
test-b2-connection.sh
test-s3-connection.py

# Tools and utilities directories
tools/
**/tools/
scripts/
**/scripts/
utilities/
**/utilities/

# Docker and container files (keep main ones, ignore test/dev variants)
Dockerfile.test
Dockerfile.dev
Dockerfile.*
!Dockerfile
!docker-compose.yml
!docker-compose.production.yml
!docker-compose.testing.yml

# Workspace and project files (IDE specific)
*.code-workspace
*.sln.bak
*.csproj.bak

# Files with spaces in names (often duplicates from cloud sync)
**/* *
!**/node_modules/**/* *