---
creation_date: 2025-04-16
modification_date: 2025-04-21
type: resource
source: Personal research
tags: [para/resources, guide, vault-organization, obsidian]
area: Software-Development
difficulty: easy
url:
---

# Vault Organisation Guide

This guide explains how our vault is organised using the PARA method and how to effectively use it.

## PARA Structure

Our vault follows the PARA method (Projects, Areas, Resources, Archive):

1. **Projects** - Time-limited goals with defined outcomes
   - Located in the `1-Projects` folder
   - Each project has its own note with tasks, resources, and notes
   - Projects should have a clear objective and deadline

2. **Areas** - Ongoing responsibilities to maintain
   - Located in the `2-Areas` folder
   - Areas represent ongoing responsibilities with no end date
   - Examples: Administration, Website Maintenance, Software Development

3. **Resources** - Information and tools for reference
   - Located in the `3-Resources` folder
   - Resources are topics or themes of ongoing interest
   - Examples: Guides, Cheatsheets, Reference Materials

4. **Archive** - Completed or inactive items
   - Located in the `4-Archive` folder
   - Contains completed projects or inactive resources
   - Useful for future reference

## Navigation

- Use the [[Home]] dashboard as your starting point
- Use the PARA index pages ([[1-Projects]], [[2-Areas]], [[3-Resources]], [[4-Archive]]) to navigate to specific sections
- Use the [[Tags MOC]] to navigate by tags
- Use the [[People MOC]] to navigate contacts
- Use the [[Tasks]] page to view all tasks across the vault
- Use the [[Search]] page for advanced search techniques

## Creating New Notes

1. Use templates for consistent note structure:
   - Project template for new projects
   - Area template for new areas
   - Resource template for new resources
   - Person template for new contacts
   - Meeting template for meeting notes
   - Daily template for daily notes

2. Always add appropriate frontmatter:
   - `type` - The type of note (project, area, resource, etc.)
   - `tags` - Relevant tags for categorization
   - `status` - For projects (active, on-hold, completed)
   - `priority` - For projects (high, medium, low)
   - `deadline` - For projects

3. Place notes in the appropriate folder:
   - Projects in `1-Projects`
   - Areas in `2-Areas`
   - Resources in `3-Resources`
   - Archived items in `4-Archive`

## Using Dataview

Dataview is used throughout the vault to create dynamic lists and tables. Common queries include:

- List all active projects:
```dataview
LIST
FROM "1-Projects"
WHERE status = "active"
```

- Table of resources by type:
```dataview
TABLE
  type as "Type"
FROM "3-Resources"
SORT type ASC
```

- List notes with specific tags:
```dataview
LIST
FROM #software-dev
```

## Using Tags

Tags are used for categorization and filtering. Common tag categories include:

- PARA categories: #para/projects, #para/areas, #para/resources, #para/archive
- Content types: #software-dev, #admin, #guide, #cheatsheet
- Status: #status/active, #status/on-hold, #status/completed
- Priority: #priority/high, #priority/medium, #priority/low

## Best Practices

1. **Minimize Folder Nesting** - Keep the folder structure flat and use tags and links for organization
2. **Link Liberally** - Create connections between related notes
3. **Use Templates** - Ensure consistency across similar types of notes
4. **Regular Maintenance** - Archive completed projects and review areas regularly
5. **Use Dataview** - Create dynamic views instead of manual lists
6. **Consistent Tagging** - Use a consistent tagging system for better organization

## Related
- [[Home]]
- [[1-Projects]]
- [[2-Areas]]
- [[3-Resources]]
- [[4-Archive]]
- [[Tags MOC]]
