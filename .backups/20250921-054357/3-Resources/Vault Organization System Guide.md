---
# CORE METADATA
creation_date: 2025-07-14
modification_date: 2025-07-14
type: resource
status: active
priority: critical
area_category: Administration
owner: Jordan
tags: [para/resources, guide, obsidian, vault-organization, metadata, productivity]

# RESOURCE CLASSIFICATION
source: Personal research
difficulty: intermediate
resource_type: guide
url: ""
last_verified: 2025-07-14

# RELATIONSHIPS (Enhanced System)
related_projects: ["[[Vault Restructuring Project]]"]
related_areas: ["[[Task Management]]", "[[Administration]]"]
related_people: ["[[Jordan]]"]
references: ["[[Dataview Documentation]]", "[[Obsidian Best Practices]]"]
inspired_by: ["[[PARA Method]]", "[[Getting Things Done]]"]
---

# 🏗️ Vault Organization System Guide

## Overview
This guide documents the enhanced metadata schema, relationship system, and task management approach implemented to create a highly connected, efficient knowledge management system in Obsidian.

## 🎯 Core Principles

### 1. **Structured Metadata Schema**
- **Consistent field types** across all note types
- **Dataview-optimized** field formats
- **Hierarchical organization** with core + specific fields
- **Relationship-first** approach to linking

### 2. **Smart Relationship System**
- **Typed relationships** instead of generic 'related' field
- **Bidirectional linking** for better discoverability
- **Context-aware queries** that show relevant content only
- **Dependency tracking** for project management

### 3. **Priority-Based Task Management**
- **5-tier priority system** (Urgent → High → Medium → Routine → Someday)
- **Context-based batching** for efficient work sessions
- **Energy-aware scheduling** matching tasks to available energy
- **Smart visibility** preventing task overwhelm

---

## 📋 Metadata Schema Reference

### Core Fields (All Note Types)
```yaml
# CORE METADATA
creation_date: YYYY-MM-DD
modification_date: YYYY-MM-DD
type: [project|area|resource|daily|meeting|person|task]
status: [active|inactive|completed|archived|on-hold]
priority: [critical|high|medium|low]
area_category: [Software-Development|Administration|Personal|Church|Education|Finance]
owner: Jordan
tags: [array of strings]

# RELATIONSHIPS (Enhanced System)
related_projects: [["[[Project 1]]", "[[Project 2]]"]]
related_areas: [["[[Area 1]]", "[[Area 2]]"]]
related_resources: [["[[Resource 1]]", "[[Resource 2]]"]]
related_people: [["[[Person 1]]", "[[Person 2]]"]]
```

### Project-Specific Fields
```yaml
# PROJECT MANAGEMENT
project_client: [Personal|Church|University|Client Name]
completion_percentage: 0-100
estimated_hours: number
actual_hours: number
deadline: YYYY-MM-DD
start_date: YYYY-MM-DD
stakeholders: [["[[Person 1]]", "[[Person 2]]"]]
dependencies: [["[[Dependency 1]]", "[[Dependency 2]]"]]
deliverables: [["[[Deliverable 1]]", "[[Deliverable 2]]"]]
```

### Area-Specific Fields
```yaml
# AREA MANAGEMENT
responsibility_level: [critical|high|medium|low]
review_frequency: [daily|weekly|monthly|quarterly]
last_review_date: YYYY-MM-DD
next_review_date: YYYY-MM-DD
key_metrics: [["Metric 1", "Metric 2"]]
```

### Task Management Fields
```yaml
# TASK MANAGEMENT (for all note types)
task_priority: [urgent|high|medium|routine|someday]
task_context: [deep-work|admin|communication|maintenance|creative|research]
estimated_time: [15min|30min|1hr|2hr|4hr|8hr|1day|1week]
energy_required: [high|medium|low]
task_status: [not-started|in-progress|blocked|waiting|completed]
blocked_by: [["[[Blocking Item 1]]"]]
waiting_for: [["[[Person/Thing 1]]"]]
```

---

## 🔗 Relationship System

### Relationship Types

#### **related_projects**: Direct project connections
- From Areas: Projects within this area of responsibility
- From Resources: Projects that use this resource
- From People: Projects this person is involved in

#### **related_areas**: Area of responsibility connections
- From Projects: Which areas this project impacts
- From Resources: Which areas benefit from this resource

#### **related_resources**: Knowledge resource connections
- From Projects: Resources needed/used by project
- From Areas: Resources relevant to this area

#### **depends_on**: Dependency relationships
- From Projects: Other projects/resources needed first
- From Areas: Areas that must function for this to work

### Smart Query Examples
```dataview
# Bidirectional relationship detection
TABLE WITHOUT ID
  file.link as "Related Project"
FROM "1-Projects"
WHERE contains(related_areas, this.file.name) 
   OR contains(this.related_projects, file.name)
SORT priority ASC
```

---

## 🎯 Task Management System

### Priority Classification
- **🔥 URGENT**: Must be done today, blocks other work
- **⚡ HIGH**: Important for project/area success
- **📋 MEDIUM**: Regular planned work, moderate impact
- **🔄 ROUTINE**: Maintenance work, can be batched
- **💡 SOMEDAY**: Ideas, no immediate deadline

### Context Tags for Batching
- **#deep-work**: Requires uninterrupted focus
- **#admin**: Administrative/bureaucratic tasks
- **#communication**: Emails, calls, meetings
- **#maintenance**: System upkeep, routine checks
- **#creative**: Design, writing, brainstorming

### Smart Task Queries
```dataview
# Priority dashboard
TABLE WITHOUT ID
  "🔥" as "",
  file.link as "URGENT TASKS"
FROM ""
WHERE !completed AND task_priority = "urgent"
SORT file.path ASC
LIMIT 5
```

---

## 📊 Dataview Query Best Practices

### 1. **Always Use Limits**
Prevent overwhelming displays with reasonable limits (3-10 items)

### 2. **Conditional Display**
Only show sections when data exists to avoid empty tables

### 3. **Context-Aware Filtering**
Show different content based on note type and current status

### 4. **Performance Optimization**
- Filter early in queries
- Use specific paths when possible
- Avoid complex sorting on large datasets

---

## 🔄 Maintenance Procedures

### Weekly Review
1. **Update next_review_date** for areas
2. **Check relationship health** - fix broken links
3. **Review task priorities** - adjust based on changing needs
4. **Archive completed projects** - update status fields

### Monthly Audit
1. **Metadata consistency check** - ensure all notes follow schema
2. **Relationship validation** - verify bidirectional links work
3. **Template updates** - incorporate lessons learned
4. **Performance review** - optimize slow queries

---

## 🚀 Quick Reference

### Creating New Notes
- Use **Enhanced templates** for consistent metadata
- **Set relationships immediately** during creation
- **Choose appropriate priority** and context
- **Link to existing notes** where relevant

### Updating Existing Notes
- **Migrate metadata** to new schema gradually
- **Convert 'related' field** to typed relationships
- **Add task management fields** where applicable
- **Update dataview queries** to use new fields

### Best Practices
- **Be specific** with relationships - quality over quantity
- **Update modification_date** when making significant changes
- **Use consistent naming** for areas and projects
- **Regular maintenance** prevents system decay

---

**Tags**: #vault-organization #metadata #relationships #task-management #productivity #obsidian #knowledge-management

---
