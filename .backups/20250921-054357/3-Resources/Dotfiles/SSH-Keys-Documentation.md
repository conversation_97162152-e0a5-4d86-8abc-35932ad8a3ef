---
creation_date: 2025-07-14
modification_date: 2025-07-14
type: documentation
aliases: [SSH Keys Documentation, Obsidian Notes SSH]
tags: [para/resources, ssh, security, dotfiles, configuration]
resource: Dotfiles
related_resources: ["Dotfiles-Management", "Dotfiles-Components"]
---

# SSH Keys Documentation

This document provides information about the SSH keys used for accessing the Obsidian notes repository.

## Repository SSH Keys
**Aliases for adding keys to environment variables**
#auggie #agents #cli #claude #gemini #ssh #agent #session
useful for securely providing cli agents and ai ssh passwords and secrets

enter-ssh-pass ( ){
sshpass -p $1 ssh $2
}
enter-ssh-pw( ){
sshpass -p $1 ssh $2
}
ssh-password(){
sshpass -p $1 ssh $2
}
ssh-pass ( ){
read -s 'SSHPASS?SSH Password: ' && export SSHPASS
ssh-pwd ( ){
read -s 'SSHPASS?SSH Password: ' && export SSHPASS
}
ps-ssh-auth(){
ssh-add -K ~/. ssh/yendornew.pem
ssh-add -K ~/.ssh/orion-paceyspace
ssh-add -K ~/.ssh/sirius-ps
ssh-add -K ~/.ssh/canis-major-basestation
ssh-add -K ~/.ssh/fjord-an_github
｝

## Secure Secrets and Keys Usage Policies

These keys are stored in local Hashicorp Vault on macos. The hashicorp token is to be used in place of any tokens or secrets, and the application or agent should retrieve their desired token from hashicorp vault using the vault token instead.

## SSH Configuration

To use these keys with the repository, add the following configuration to your `~/.ssh/config` file:

```
Host ucanotes
  HostName github.com
  IdentityFile ~/.ssh/obsidian-notes-asus
```

## Key Management

1. **Backup**: Always keep a secure backup of these keys
2. **Permissions**: Ensure private key has restrictive permissions (600)
3. **Passphrase**: Use a strong passphrase to protect the private key
4. **Key Rotation**: Consider rotating keys every 6-12 months

## Related
- [[3-Resources]]
- [[3-Resources/Dotfiles/Dotfiles-Management]]
- [[3-Resources/Dotfiles/Dotfiles-Components]]
- [[3-Resources/README]]