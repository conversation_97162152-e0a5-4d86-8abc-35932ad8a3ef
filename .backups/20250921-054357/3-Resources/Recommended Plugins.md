---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: guide
tags: [para/resources, guide, plugins]
---

# Recommended Plugins

This guide lists recommended plugins for our Obsidian vault.

## Core Plugins

These are essential plugins that form the backbone of our vault organization:

### 1. Dataview
- **Purpose**: Create dynamic views of your notes based on properties and content
- **Usage**: Used throughout the vault for creating lists, tables, and task views
- **Documentation**: [Dataview Documentation](https://blacksmithgu.github.io/obsidian-dataview/)

### 2. Tasks
- **Purpose**: Enhanced task management
- **Usage**: Track and query tasks across the vault
- **Documentation**: [Tasks Documentation](https://obsidian-tasks-group.github.io/obsidian-tasks/)

### 3. Templater
- **Purpose**: Advanced template system
- **Usage**: Create and use templates with dynamic content
- **Documentation**: [Templater Documentation](https://silentvoid13.github.io/Templater/)

### 4. Omnisearch
- **Purpose**: Enhanced search capabilities
- **Usage**: Quickly find notes with fuzzy search
- **Documentation**: [Omnisearch GitHub](https://github.com/scambier/obsidian-omnisearch)

## Organization Plugins

These plugins help with vault organization:

### 5. Calendar
- **Purpose**: Calendar view for daily notes
- **Usage**: Navigate to daily notes using a calendar interface
- **Documentation**: [Calendar GitHub](https://github.com/liamcain/obsidian-calendar-plugin)

### 6. Periodic Notes
- **Purpose**: Create daily, weekly, monthly, and quarterly notes
- **Usage**: Manage time-based notes
- **Documentation**: [Periodic Notes GitHub](https://github.com/liamcain/obsidian-periodic-notes)

### 7. Tag Wrangler
- **Purpose**: Manage and organize tags
- **Usage**: Rename, merge, and organize tags
- **Documentation**: [Tag Wrangler GitHub](https://github.com/pjeby/tag-wrangler)

## Productivity Plugins

These plugins enhance productivity:

### 8. QuickAdd
- **Purpose**: Quickly add content to your vault
- **Usage**: Create macros for common actions
- **Documentation**: [QuickAdd GitHub](https://github.com/chhoumann/quickadd)

### 9. Kanban
- **Purpose**: Create Kanban boards
- **Usage**: Visual project management
- **Documentation**: [Kanban GitHub](https://github.com/mgmeyers/obsidian-kanban)

### 10. Natural Language Dates
- **Purpose**: Parse natural language dates
- **Usage**: Type @today, @tomorrow, etc. to insert dates
- **Documentation**: [Natural Language Dates GitHub](https://github.com/argenos/nldates-obsidian)

## Visual Enhancement Plugins

These plugins improve the visual experience:

### 11. Admonition
- **Purpose**: Create styled callout boxes
- **Usage**: Highlight important information
- **Documentation**: [Admonition GitHub](https://github.com/valentine195/obsidian-admonition)

### 12. Advanced Tables
- **Purpose**: Enhanced table editing
- **Usage**: Easily create and edit tables
- **Documentation**: [Advanced Tables GitHub](https://github.com/tgrosinger/advanced-tables-obsidian)

### 13. Outliner
- **Purpose**: Enhanced outlining and list management
- **Usage**: Work with nested lists more effectively
- **Documentation**: [Outliner GitHub](https://github.com/vslinko/obsidian-outliner)

## Integration Plugins

These plugins integrate with external tools:

### 14. Obsidian Git
- **Purpose**: Git integration for Obsidian
- **Usage**: Backup and sync your vault with Git
- **Documentation**: [Obsidian Git GitHub](https://github.com/denolehov/obsidian-git)

### 15. Readwise Official
- **Purpose**: Import highlights from Readwise
- **Usage**: Bring in highlights from books, articles, etc.
- **Documentation**: [Readwise Official GitHub](https://github.com/readwiseio/obsidian-readwise)

## Installation Instructions

1. Open Obsidian Settings
2. Go to "Community Plugins"
3. Turn off "Safe Mode" if it's enabled
4. Click "Browse" to open the plugin browser
5. Search for the plugin name
6. Click "Install"
7. Enable the plugin after installation

## Plugin Configuration Recommendations

### Dataview
- Enable JavaScript Queries
- Set Inline JavaScript to "Enable"

### Tasks
- Enable "Group by Path"
- Set "Default Due Date Format" to "YYYY-MM-DD"

### Templater
- Set "Template folder location" to "Templates"
- Enable "Trigger Templater on new file creation"

### Omnisearch
- Adjust search weights to prioritize titles and headings

## Related
- [[Vault Organization Guide]]
- [[Dataview Guide]]
- [[3-Resources]]
- [[Home]]
