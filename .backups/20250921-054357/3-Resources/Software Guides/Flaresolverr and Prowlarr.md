---
creation_date: 2025-04-06
modification_date: 2025-04-06
type: note
aliases: []
tags: []
area: 
project: 
resource: 
archive: 
status: active
priority: 
links: []
related: []
---

# Install Flaresolvarr with docker

Setting up FlareSolverr to work with Prowlarr on Linux (specifically Pop!_OS) involves a few steps. Here's a comprehensive guide:

Prerequisites:

    Docker (Recommended): This is the easiest and most isolated way to run FlareSolverr.
    Docker Compose (Recommended): Simplifies managing Docker containers.
    Prowlarr Installed: You should already have Prowlarr running on your Pop!_OS system.

Method 1: Using Docker and Docker Compose (Recommended)

This is the preferred method due to its ease of setup and management.

Step 1: Install Docker and Docker Compose (if not already installed)

Open your terminal and run the following commands:
Bash

sudo apt update
sudo apt install docker.io
sudo systemctl start docker
sudo systemctl enable docker

# Install Docker Compose
sudo apt install docker-compose

Verify the installations:
Bash

docker --version
docker-compose --version

Step 2: Create a docker-compose.yml file

Create a new directory for your FlareSolverr setup (optional but recommended for organization):
Bash

mkdir ~/flaresolverr
cd ~/flaresolverr

Create a docker-compose.yml file within this directory using a text editor (like nano or gedit):
```Bash
vim docker-compose.yml
```

Paste the following content into the file:

```YAML

version: '3.8'
services:
  flaresolverr:
    image: flaresolverr/flaresolverr:latest
    container_name: flaresolverr
    restart: unless-stopped
    ports:
      - "8080:8080" # Maps host port 8080 to container port 8080
    environment:
      - LOG_LEVEL=info # Optional: Set log level (debug, info, warning, error)
      - TZ=Australia/Brisbane # Optional: Set your timezone (replace with your actual timezone)
```
Explanation:

    version: '3.8': Specifies the Docker Compose file format version.
    services:: Defines the services to run.
    flaresolverr:: Defines the FlareSolverr service.
    image: flaresolverr/flaresolverr:latest: Specifies the Docker image to use (the latest stable version).
    container_name: flaresolverr: Sets a name for the Docker container.
    restart: unless-stopped: Ensures the container restarts automatically unless explicitly stopped.
    ports:: Maps ports between your host machine and the container. 8080:8080 means that requests to port 8080 on your Pop!_OS machine will be forwarded to port 8080 inside the FlareSolverr container. You can change the host port (8080 on the left) if needed, but make sure to update Prowlarr accordingly.
    environment:: Sets environment variables for the FlareSolverr container.
        LOG_LEVEL: Controls the verbosity of FlareSolverr logs.
        TZ: Sets the timezone inside the container. It's good practice to set this to your local timezone. You can find your timezone using timedatectl.

Step 3: Start FlareSolverr using Docker Compose

In the same directory where you created docker-compose.yml, run the following command in your terminal:
Bash

docker-compose up -d

    docker-compose up: Creates and starts the services defined in the docker-compose.yml file.
    -d: Runs the containers in detached mode (in the background).

Step 4: Verify FlareSolverr is running

You can check if the FlareSolverr container is running with:
Bash

docker ps

You should see a container named flaresolverr in the output.

You can also try accessing the FlareSolverr health endpoint in your web browser:

http://localhost:8080/

You should see a JSON response indicating that FlareSolverr is running.

Step 5: Configure Prowlarr to use FlareSolverr

    Open your Prowlarr web interface.

    Navigate to Settings > Indexers.

    Edit an indexer that requires Cloudflare bypassing (you'll usually know this if you encounter issues with that indexer).

    Look for a section related to Cloudflare Solver or similar. It might be within advanced settings.

    Enable the Cloudflare Solver option.

    Enter the FlareSolverr URL. This will be the address and port where your FlareSolverr instance is running. If you used the docker-compose.yml as is, this will likely be:

    http://localhost:8080/

    If FlareSolverr is running on a different machine on your network, replace localhost with the IP address or hostname of that machine.

    You might have options for FlareSolverr API Key or other settings. FlareSolverr doesn't require an API key by default.

    Click Save on the indexer settings.

Step 6: Test the Indexer

After configuring FlareSolverr in Prowlarr, test the affected indexer. Prowlarr should now be able to use FlareSolverr to bypass Cloudflare protection and successfully query the indexer.

Method 2: Running FlareSolverr Directly (Less Recommended)

This method involves downloading and running FlareSolverr directly on your system. It's generally more complex to manage dependencies and keep updated.

Step 1: Install Node.js and npm

FlareSolverr is built with Node.js. If you don't have it installed, open your terminal and run:
Bash

sudo apt update
sudo apt install nodejs npm

Verify the installations:
Bash

node -v
npm -v

Step 2: Download FlareSolverr

You can download the latest release from the FlareSolverr GitHub repository (https://github.com/FlareSolverr/FlareSolverr). Go to the "Releases" section and download the source code (usually a .zip or .tar.gz file).

Alternatively, you can clone the repository:
Bash

git clone https://github.com/FlareSolverr/FlareSolverr.git
cd FlareSolverr

Step 3: Install Dependencies

Navigate to the FlareSolverr directory in your terminal and install the required Node.js modules:
Bash

npm install

Step 4: Configure FlareSolverr (Optional)

You can configure FlareSolverr by creating a config.json file or using environment variables. Refer to the FlareSolverr documentation for details on configuration options.

Step 5: Run FlareSolverr

Start FlareSolverr from the terminal:
Bash

node server.js

By default, FlareSolverr will run on http://localhost:8080. You might see some output in the terminal.

Step 6: Configure Prowlarr (Same as Step 5 in Method 1)

Follow the same steps as in Method 1 to configure Prowlarr to use the FlareSolverr URL (likely http://localhost:8080/).

Step 7: Keep FlareSolverr Running (Important for Direct Installation)

When running FlareSolverr directly, you need to ensure the node server.js process keeps running in the background. You can achieve this using tools like:

    nohup: Runs a command immune to hangups, with output redirected to a file.
    Bash

    nohup node server.js &

    screen or tmux: Terminal multiplexers that allow you to detach and reattach terminal sessions.
    Systemd Service: Create a systemd service unit file to manage FlareSolverr as a service that starts on boot and can be easily controlled. This is the most robust approach for long-term use.

Creating a Systemd Service (for direct installation):

    Create a service file (e.g., flaresolverr.service) in /etc/systemd/system/:
    Bash

sudo nano /etc/systemd/system/flaresolverr.service

Paste the following content (adjust paths as needed):
Ini, TOML

[Unit]
Description=FlareSolverr
After=network.target

[Service]
User=<your_username>  # Replace with your actual username
WorkingDirectory=/path/to/FlareSolverr # Replace with the actual path to your FlareSolverr directory
ExecStart=/usr/bin/node server.js
Restart=on-failure

[Install]
WantedBy=multi-user.target

Enable and start the service:
Bash

sudo systemctl enable flaresolverr.service
sudo systemctl start flaresolverr.service

Check the service status:
Bash

    sudo systemctl status flaresolverr.service

Choosing a Method:

The Docker method is highly recommended for its simplicity, isolation, and ease of updates. Running FlareSolverr directly can be more involved in terms of dependency management and ensuring it runs reliably in the background.

Remember to replace placeholders like <IP_ADDRESS>, <PORT_NUMBER>, <your_username>, and /path/to/FlareSolverr with your actual values. Good luck!

## Content


## References


## Tasks
- [ ] 

## Metadata
- **Original Creation**: 2025-04-06
- **Source**: 
- **Context**: