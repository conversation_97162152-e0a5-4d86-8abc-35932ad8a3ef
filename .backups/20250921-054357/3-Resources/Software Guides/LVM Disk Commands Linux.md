---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
source: Various Linux documentation
tags: [para/resources, guide, linux, lvm, system-administration]
---

# LVM Disk Commands Linux

## Overview
A comprehensive guide to safely reduce an LVM partition and extend another on Linux systems.

## Steps to Reduce and Extend LVM Partitions

1. **Check Current LVM Setup**

   First, identify the current logical volumes and their sizes.

   ```bash
   lvdisplay
   vgdisplay
   pvdisplay
   ```

2. **Backup Important Data**

   Before making any changes, ensure that all important data is backed up to prevent accidental loss.

3. **Unmount the Logical Volume to Be Reduced**

   Suppose you want to reduce `lv_home`. Unmount it using:

   ```bash
   sudo umount /dev/your_vg/lv_home
   ```

4. **Check the Filesystem**

   It's crucial to check the filesystem for errors before resizing.

   ```bash
   sudo e2fsck -f /dev/your_vg/lv_home
   ```

5. **Resize the Filesystem**

   Reduce the filesystem size to a value smaller than the desired new size of the logical volume. For example, to resize to 20G:

   ```bash
   sudo resize2fs /dev/your_vg/lv_home 20G
   ```

6. **Reduce the Logical Volume Size**

   After resizing the filesystem, reduce the logical volume size accordingly.

   ```bash
   sudo lvreduce -L 20G /dev/your_vg/lv_home
   ```

   Alternatively, you can use the interactive mode:

   ```bash
   sudo lvreduce -r /dev/your_vg/lv_home
   ```

   The `-r` flag resizes the filesystem along with the logical volume.

7. **Verify the Reduction**

   Ensure that the logical volume has been resized correctly.

   ```bash
   lvdisplay /dev/your_vg/lv_home
   ```

8. **Extend the Target Logical Volume**

   Now, extend the logical volume you intend to enlarge, for example, `lv_data`.

   ```bash
   sudo lvextend -L +20G /dev/your_vg/lv_data
   ```

9. **Resize the Filesystem on the Extended Volume**

   After extending the logical volume, resize the filesystem to utilize the new space.

   ```bash
   sudo resize2fs /dev/your_vg/lv_data
   ```

10. **Remount the Filesystems**

    Finally, remount the reduced logical volume.

    ```bash
    sudo mount /dev/your_vg/lv_home /home
    ```

## Example Commands

Here is a summary of the commands used in the process:

```bash
#!/bin/bash

# Unmount the logical volume
sudo umount /dev/your_vg/lv_home

# Check the filesystem
sudo e2fsck -f /dev/your_vg/lv_home

# Resize the filesystem
sudo resize2fs /dev/your_vg/lv_home 20G

# Reduce the logical volume size
sudo lvreduce -L 20G /dev/your_vg/lv_home

# Extend the target logical volume
sudo lvextend -L +20G /dev/your_vg/lv_data

# Resize the filesystem on the extended volume
sudo resize2fs /dev/your_vg/lv_data

# Remount the logical volume
sudo mount /dev/your_vg/lv_home /home
```

Make sure to replace `your_vg`, `lv_home`, and `lv_data` with your actual volume group and logical volume names.

## Important Considerations

- **Backup Data:** Always back up your data before performing disk operations.
- **Ensure Adequate Space:** Verify that there is enough free space in the volume group to extend the target logical volume.
- **Use Correct Filesystem Tools:** The above example uses `resize2fs` for ext4 filesystems. If you are using a different filesystem (e.g., XFS), use the appropriate tools (`xfs_growfs` for XFS, etc.).

## Common LVM Commands

### Display Commands
- `pvs` - Display physical volumes
- `vgs` - Display volume groups
- `lvs` - Display logical volumes
- `pvdisplay` - Detailed information about physical volumes
- `vgdisplay` - Detailed information about volume groups
- `lvdisplay` - Detailed information about logical volumes

### Creation Commands
- `pvcreate /dev/sdX` - Create a physical volume
- `vgcreate vg_name /dev/sdX` - Create a volume group
- `lvcreate -L size -n lv_name vg_name` - Create a logical volume

### Removal Commands
- `lvremove /dev/vg_name/lv_name` - Remove a logical volume
- `vgremove vg_name` - Remove a volume group
- `pvremove /dev/sdX` - Remove a physical volume

## Related
- [[3-Resources]]
- [[Unix Filesystem guides]]
- [[Computer Education]]
