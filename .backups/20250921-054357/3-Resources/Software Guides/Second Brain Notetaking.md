dotfiles for setup:
https://github.com/omerxx/dotfiles

neovim for writing:
https://www.youtube.com/watch?v=DgKI4hZ4EEI

obsidian for viewing/writing:
https://www.youtube.com/watch?v=5ht8NYkU9wQ&t=480s


cleanup of files with cursor. Prompt rules:
I need assistance with cleaning out my obsidian vault. use obsidian features. I want to make use of obsidians graph (with jugl global graph plugin) and dataview plugin. I primarily search my notes and make queries rather than following a folder structure, finding folders is a waste of time for me. filters are great. i need you to add plenty of metadata for queries and searches like everyone else does for using these plugins. I have a backup of this vault so we can to some cleaning. go ahead and make changes, we may have to prompt several times to get through everything. I have a rough template file in the Template/ folder, however you may nave a better template than i do. Just make sure the metadata can be automated (like date for example, could be set to todays date) and other examples. You can even see that the creations date of google keep notes are preserved and date back many years ago. Can you please add the note files date metadata to the obsidian notes property field too?