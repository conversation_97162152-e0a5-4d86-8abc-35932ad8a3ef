---
title: "Omnivore is Dead: Where to Go Next"
source: "https://molodtsov.me/2024/10/omnivore-is-dead-where-to-go-next/"
author:
  - "[[<PERSON><PERSON>]]"
published: 2024-10-30
created: 2025-09-05
description: "Communications expert and an amateur researcher of productivity."
tags:
  - "clippings"
---
![](https://molodtsov.me/images/cleanshot-2024-10-30-at-13.04.34-2x.png)

[Omnivore](https://molodtsov.me/2023/08/omnivore-review-an-underrated-read-later-app/) was the best read-later app for most people. It was quite modern yet had a generous free tier. Yesterday the team [informed](https://blog.omnivore.app/p/omnivore-is-joining-elevenlabs) the world that they got acquihired by ElevenLabs. The app will go offline and all the data will be deleted by November 15th.

It’s sad but almost should have been expected. Read-later apps are an extremely niche product, so you must be able to monetize a very small customer base successfully. I suppose the Omnivore team couldn’t, precisely because most people were drawn to the app because it was “free”.

Omnivore famously was open source. This should work as a cautionary tale for blindly trusting the “open source” label. All it means is that the code repo is out there. For this to become a viable service, someone needs to invest resources into developing it, paying server costs, and shipping mobile apps. And while subreddits are filled with people asking anyone to do this, I doubt that it will happen or be more successful than the Omnivore itself (why would it?).

Productivity apps exist in a limbo. The basic functionality is usually covered by the OS and the browser, such as the Safari Reading List. And there are only so many people who require more, and even fewer of them will be ready to pay you $5/$10/$15 in perpetuity.

What are your options if you’re looking for another app?

## Readwise Reader

![](https://molodtsov.me/images/cleanshot-2024-10-30-at-11.20.45-2x.jpeg)

[Readwise Reader](https://readwise.io/read) is a powerful read-later app from [Readwise](http://readwise.io/). It is a service that collects all the highlights you left in Kindle, Apple Books, and various apps to send daily or weekly digests and remind you about the stuff you read and found important or interesting.

The biggest downside for most people is it costs $10 a month. At the same time, Readwise seems to be the only company in this game that has built a viable business you can expect to have at least some longevity. Ultimately, ==if you aren’t paying for a service, don’t be surprised when it dies==.

This is the best app of this kind. If anything, it might have too many features for my liking, although it’s highly customizable. And the integration with Readwise is the best part. Hoarding text like a squirrel is pretty useless unless you learn something. Readwise constantly reminds you about your highlights. Sometimes I get an email and reread the article where it came from, often with a fresh perspective.

Readwise also added the import support for the Omnivore format, so request that backup before November 15th.

## Matter

![](https://molodtsov.me/images/cleanshot-2024-10-30-at-11.34.21-2x.jpeg)

[Matter](https://getmatter.com/) is another read-later app closer to a modern take on minimalist Instapaper. It’s pretty good and has a free tier. In 2022, they moved most of the features behind a paid plan, but it’s really hard to decipher what they offer right now. There’s a $15-a-month subscription (with a steep annual discount) that offers advanced AI transcription, better speech generation for articles, and integrations with other services.

Unfortunately, it always seemed like the app would follow the Omnivore path, but it is still chugging along five years in. You might like it if you want something simple.

## GoodLinks

![](https://molodtsov.me/images/cleanshot-2024-10-30-at-11.27.05-2x.jpeg)

[GoodLinks](https://goodlinks.app/) is a read-later app for the Apple ecosystem and this is its entire differentiator. First, you get native apps for MacOS and iOS, which is awesome. Another advantage of the Apple ecosystem is that instead of running servers, developers can simply use CloudKit, which absolves a lot of costs and complexity, enabling single developers and small teams to build apps like these.

It was recently updated and now supports highlights, which was the biggest problem for me. However, it also shifted from a one-time payment to a subscription model. You pay $9.99 for the base app, and then some features are gated behind GoodLinks Premium, which adds $4.99.

## Pocket or Instapaper

For the love of God, stop using these old dinosaurs. Well, I’ve actually heard that Instapaper has received meaningful updates, but I definitely wouldn’t trust Mozilla to make Pocket a great service.

## Built-in Apps

Finally, you can simply use built-in apps. Safari has Reading List that [integrates](https://molodtsov.me/2024/09/the-ode-to-apple-notes/) with Apple Notes and allows you to create your own free Readwise if needed. Chrome and other browsers also have rudimentary reading lists you can use.

[Comment on X](https://x.com/y_molodtsov/status/1851607256485277857)

---

---

---

---

---