---
title: "iCloud Mail server settings for other email client apps – Apple Support (AU)"
source: "https://support.apple.com/en-au/102525"
author:
  - "[[Apple Support]]"
published: August
created: 2025-09-13
description: "Use these iCloud Mail server settings to manually set up your iCloud Mail account in an email client app."
tags:
  - "clippings"
---
## iCloud Mail server settings for other email client apps

## Do you need these email server settings?

With these iCloud Mail server settings, you can set up your email client app to send and receive email with your iCloud Mail account. You will need these server settings to hand when you manually configure your email client app.

You won't need these email server settings if you meet [iCloud system requirements](https://support.apple.com/en-au/HT204230) and can use any of these setup methods:

- [Use iCloud Preferences](http://www.apple.com/au/icloud/setup/mac.html) on your Mac in OS X Lion 10.7.4 or later.
- [Use iCloud for Windows](http://www.apple.com/au/icloud/setup/pc.html) on your PC in Microsoft Windows with Outlook 2010 to Outlook 2016. [Set up two-factor authentication](https://support.apple.com/en-au/HT204915) and generate an app-specific password to use for iCloud Mail.
- [Use iCloud settings](http://www.apple.com/au/icloud/setup/ios.html) on your iPhone, iPad or iPod touch with iOS 7 or later.

## iCloud Mail server settings

- iCloud Mail uses the IMAP and SMTP standards that are supported by most modern email client apps. iCloud Mail does not support POP.
- If you've set up an account using iCloud System Preferences or macOS Mail in 10.7.4 or later, you won't see these settings because they're automatically configured.
- Refer to your email client app's documentation for information about how to use these settings.

### IMAP information for the incoming iCloud Mail server

- Server name: imap.mail.me.com
- SSL Required: Yes
	- *If you see an error message when using SSL, try using TLS instead.*
- Port: 993
- Username: This is usually the name of your iCloud Mail email address (e.g. johnappleseed, not <EMAIL>). If your email client app can't connect to iCloud Mail using just the name of your email address, try using the full address.
- Password: [Generate an app-specific password](https://support.apple.com/en-au/HT204397).

### SMTP information for the outgoing iCloud Mail server

- Server name: smtp.mail.me.com
- SSL Required: Yes
	- *If you see an error message when using SSL, try using TLS or STARTTLS instead.*
- Port: 587
- SMTP Authentication Required: Yes
- Username: Your full iCloud Mail email address (e.g. <EMAIL>, not johnappleseed)
- Password: Use the app-specific password that you generated when you set up the incoming mail server.

Published Date:

Thanks for your feedback.