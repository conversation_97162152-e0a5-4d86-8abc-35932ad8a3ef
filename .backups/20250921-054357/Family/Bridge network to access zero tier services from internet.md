---
creation_date: 2025-04-06
modification_date: 2025-04-06
type: note
aliases:
  - project
tags: 
area: 
project: 
resource: 
archive: 
status: active
priority: 
links: 
related:
---
	*where do i point my domain name? on the bridge instance? how can i use https for prowlarr and qbitorent webui?*


You will point your domain name to the public IP address (or preferably the Elastic IP address) of your EC2 bridge instance.

Here's a breakdown:

Pointing Your Domain Name:

**Identify Your EC2 Instance's Public IP:** In the AWS Management Console, navigate to your EC2 instances and find the public IPv4 address of your bridge instance.   
**Allocate an Elastic IP (Recommended):** For production or stable setups, it's highly recommended to allocate an Elastic IP address and associate it with your bridge instance. This ensures that your IP address doesn't change if you stop and restart your instance.
**Configure DNS Records:** Go to the DNS management settings for your domain name registrar (e.g., GoDaddy, Namecheap, AWS Route 53). You'll need to create DNS records that point your domain (and any subdomains) to the public IP (or Elastic IP) of your EC2 bridge instance. The most common record types for this are:
A Record: To point your domain (e.g., yourdomain.com) or a subdomain (e.g., www.yourdomain.com) directly to the IP address.
CNAME Record: To point a subdomain (e.g., www.yourdomain.com) to another domain name. You could potentially point a CNAME to the public DNS name of your EC2 instance, but using an A record with an Elastic IP is generally more straightforward and reliable for this purpose.
  
## **Using HTTPS for Prowlarr and qBittorrent Web UI:**

Since you're forwarding traffic through your EC2 bridge to your local server running Nginx Proxy Manager, you'll leverage NPM to handle HTTPS for your internal services like Prowlarr and qBittorrent Web UI. Here's the typical approach:

Internal HTTP Configuration: Ensure that Prowlarr and qBittorrent Web UI are configured to listen on their default HTTP ports (e.g., Prowlarr on 9696, qBittorrent on 8080) on the ZeroTier IP address of your local server. You don't necessarily need to configure HTTPS directly within these applications.

### **Nginx Proxy Manager Configuration:**

A**dd Proxy Hosts:** In your Nginx Proxy Manager interface, create new proxy host entries for Prowlarr and qBittorrent.
**Domain Names:** For each service, enter the public-facing domain name or subdomain you want to use (e.g., prowlarr.yourdomain.com, qbittorrent.yourdomain.com).
**Forward To IP/Hostname**: In the "Forward To" section, enter the ZeroTier IP address of your local server and2 the respective HTTP port for each service (e.g., ***********:9696 for Prowlarr, ***********:8080 for qBittorrent).
Request Certificates: Enable the "SSL" option for each proxy host and request a Let's Encrypt SSL certificate (or use your own if you have one). NPM will handle the SSL certificate issuance and renewal for your public-facing domain names.
iptables Forwarding (If Not Already Done): Make sure your iptables rules on the EC2 bridge instance are forwarding both HTTP (port 80) and HTTPS (port 443) traffic to the ZeroTier IP of your local server. NPM will then listen on these standard ports and route traffic based on the domain name in the request.

In summary:

You point your domain name to the public IP of your EC2 bridge instance.
HTTPS is handled by Nginx Proxy Manager running on your local server, which receives the forwarded HTTP and HTTPS traffic from the bridge and secures the connection with SSL certificates for your public-facing domain names. Your internal services (Prowlarr, qBittorrent) can remain configured for HTTP on their ZeroTier IPs.
# Untitled

## Content


## References


## Tasks
- [ ] 

## Metadata
- **Original Creation**: 2025-04-06
- **Source**: 
- **Context**: