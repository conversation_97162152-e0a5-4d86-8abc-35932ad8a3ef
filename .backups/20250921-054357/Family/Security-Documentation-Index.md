# Network Security Documentation

Created: 16:39
Last modified: 16:39
Tags: #index #security #documentation #zerotier #waf

## Overview

This is the main index for all security documentation related to the ZeroTier network, ModSecurity WAF, and EC2 bridge implementation.

# Cursor AI Rules:
I need assistance setting up a securely accessable network for my software services from the internet using 2 zerotier-one networks and an ec2 bridge instance forwarding to nginx proxy manager for the services in the private, secure home network. I'm in the security hardening phase now that things are more exposed. I need to have access logs and a convenient and effective WAF or firewall. Please give me security recommendations for hardening these networks as they are sensitive. Please add documentation to my obsidian vault for crucial steps for me to refer to in the future when managing the network and mainting it. Please make sure documentation adheres to the templates/layout and add obsidian links.  note formatting in accordance to Home.md and the 1, 2, 3, 4, PARA templates. Please ensure notes go in the Brain/ folder somewhere so it is tracked by git. Make sure to create and update the table of contents for the project, or a links note which will list all .md documents for the given topic so notes are easier to find and mapped out better. making tighter connections when querying and search graph in obsidian.tp.time.now("HH:MM").

## Documentation Index

| Document                                                                                       | Description                                                          | Tags                                  |
| ---------------------------------------------------------------------------------------------- | -------------------------------------------------------------------- | ------------------------------------- |
| [ZeroTier Network Security](/home/<USER>/Programs/torrents/ZeroTier-Security-Setup.md)         | Overall security architecture for dual ZeroTier networks             | #network #security #zerotier #waf     |
| [ModSecurity WAF Configuration](/home/<USER>/Programs/torrents/ModSecurity-WAF-Setup.md)       | Detailed guide for ModSecurity WAF configuration and troubleshooting | #security #waf #modsecurity #nginx    |
| [EC2 ZeroTier Bridge Security](/home/<USER>/Programs/torrents/EC2-ZeroTier-Bridge-Security.md) | Security hardening for EC2 instance acting as ZeroTier bridge        | #security #zerotier #ec2 #aws #bridge |

## Key Security Components

1. **Network Architecture**
   - Dual ZeroTier networks (isolation)
   - EC2 bridge (gateway)
   - Private home network (services)

2. **Security Layers**
   - Network isolation (ZeroTier)
   - Access control (EC2 security groups)
   - Application filtering (ModSecurity WAF)
   - TLS encryption (Nginx Proxy Manager)

3. **Monitoring and Logging**
   - ModSecurity audit logs
   - CloudWatch monitoring
   - System logs
   - Network traffic analysis

## Maintenance Schedule

| Frequency | Task | Documentation |
|-----------|------|---------------|
| Weekly | Check WAF logs for attacks | [ModSecurity WAF Configuration](/home/<USER>/Programs/torrents/ModSecurity-WAF-Setup.md#monitoring) |
| Weekly | Update container images | N/A |
| Monthly | Review and tune WAF rules | [ModSecurity WAF Configuration](/home/<USER>/Programs/torrents/ModSecurity-WAF-Setup.md#advanced-configuration) |
| Monthly | Rotate credentials | [EC2 ZeroTier Bridge Security](/home/<USER>/Programs/torrents/EC2-ZeroTier-Bridge-Security.md#system-hardening) |
| Quarterly | Full security audit | [EC2 ZeroTier Bridge Security](/home/<USER>/Programs/torrents/EC2-ZeroTier-Bridge-Security.md#security-auditing) |

## Common Tasks

- [Fixing ModSecurity Read-only Errors](ModSecurity-WAF-Setup.md#issue-read-only-filesystem-error)
- [Creating Custom WAF Rules](ModSecurity-WAF-Setup.md#custom-rule-sets)
- [Hardening EC2 SSH Configuration](EC2-ZeroTier-Bridge-Security.md#ssh-hardening)
- [ZeroTier Network Traffic Routing](EC2-ZeroTier-Bridge-Security.md#traffic-routing)
- [Monitoring WAF Attacks](ModSecurity-WAF-Setup.md#monitoring)
- [[Setup and Bug Challenge 10 april - modsecurity setup, location and routes]]

## Security Improvement Plan

| Priority | Improvement                 | Status  |
| -------- | --------------------------- | ------- |
| High     | Increase WAF paranoia level | Pending |
| High     | Implement log rotation      | Pending |
| Medium   | Add GeoIP filtering         | Planned |
| Medium   | Implement rate limiting     | Planned |
| Low      | Set up centralized logging  | Future  |

## Emergency Contacts

- AWS Support: https://aws.amazon.com/premiumsupport/
- ZeroTier Support: https://support.zerotier.com/
- ModSecurity Community: https://github.com/SpiderLabs/ModSecurity/issues 