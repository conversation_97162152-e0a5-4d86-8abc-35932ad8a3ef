---
creation_date: 2025-04-06
modification_date: 2025-04-06
type: note
aliases:
  - ZeroTier Network Documentation
  - Network Setup Index
tags:
  - networking
  - zerotier
  - documentation
  - index
  - links
  - docker
  - troubleshooting
area: Infrastructure
project: Bridge Network
status: active
priority: medium
---

# ZeroTier Network Documentation Links

This document serves as an index to all ZeroTier network related documentation in the vault.

## Network Architecture & Setup

- [[Family/Network Architecture Overview]] - Comprehensive overview of the network design, diagrams, and data flow.
- [[Simplified ZeroTier Network Setup]] - Step-by-step guide for the current single-network implementation, including Docker, UFW, and iptables.
- [[Minimal Gateway For ZeroTier Networks]] - Advanced security setup with dedicated gateway (future reference).

## Implementation Guides & Maintenance

- [[EC2 Bridge Server Maintenance]] - Setting up and maintaining the EC2 bridge server.
- [[ZeroTier Flow Rules Placement]] - Configuring network access control rules in ZeroTier Central.

## Troubleshooting

- [[Troubleshooting ZeroTier and Docker Networking]] - Diagnostic steps for common connectivity issues between ZeroTier nodes, Docker containers, and backend services.
- [[Simplified ZeroTier Network Setup#Troubleshooting Summary]] - Quick summary of common problems and solutions.

## Security Considerations

- [[Secure ZeroTier Network Separation]] - Best practices for network isolation (more relevant for dual-network setup).
- [[Bridge network to access zero tier services from internet]] - Initial network bridge documentation.

## Services

- [[Media Streaming Server]] - Setting up media services behind ZeroTier.

## Diagrams

*See [[Family/Network Architecture Overview]] for detailed diagrams of both the current simplified and the alternative dual-network architectures.*

## Implementation Options Comparison

| Approach | Pros | Cons | Documentation |
|----------|------|------|---------------|
| **Single ZeroTier Network** | • Simplicity<br>• Easier to set up<br>• Less infrastructure<br>• Lower resources | • Less isolation<br>• Higher risk if bridge compromised | [[Simplified ZeroTier Network Setup]] |
| **Dual Networks with Gateway** | • Better isolation<br>• Defense in depth<br>• Expendable gateway<br>• Limited exposure | • More complex<br>• More infrastructure<br>• Higher resources | [[Minimal Gateway For ZeroTier Networks]] |

## Key Network Parameters (Current Setup)

| Component | Network | Interface | IP Address/Subnet | Purpose |
|-----------|---------|-----------|-------------------|---------|
| EC2 Bridge | Internet | `enX0` | Public IP | Internet entry point |
| EC2 Bridge | ZeroTier | `ztmjfcpubr` | ************/24 | Connects to ZT Network |
| Nginx Host | ZeroTier | `ztmjfcpubr` | *************/24 | Hosts Nginx/Services |
| Nginx Host | Local LAN | `enp2s0` | 192.168.0.x/16 | Local Network Access |
| Nginx Host | Docker | `br-fc93a4406e3f` | **********/16 | Docker Bridge Network |
| Nginx Container | Docker | `eth0` | ********** | Nginx Proxy Manager IP |

## Common Tasks

- [[Port Forwarding on EC2 Bridge]] - How to forward specific ports using iptables.
- [[Adding New Services to Nginx Proxy Manager]] - Exposing new internal services via the reverse proxy.
- [[Configuring UFW for Docker and ZeroTier]] - Setting up firewall rules on the Nginx host.

## Security Checklist

- [x] Regular updates on all servers (Unattended Upgrades configured)
- [x] Restrictive ZeroTier flow rules applied
- [x] Proper UFW firewall configuration on Nginx host
- [x] Proper iptables configuration on EC2 Bridge and Nginx host
- [x] Limited ZeroTier network membership
- [ ] Regular security audits (Logs, Rules, Members)
- [ ] Fail2ban on critical services (e.g., SSH)
- [ ] Review of access logs (Nginx, Auth, UFW)
- [ ] Regular backups (Nginx Proxy Manager data, system configs) 