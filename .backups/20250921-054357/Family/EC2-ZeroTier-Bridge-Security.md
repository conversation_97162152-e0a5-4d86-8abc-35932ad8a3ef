---
creation_date: 2025-04-10
modification_date: 2025-04-10
type: index
aliases: [Resources]
tags: [para/resources, index]
---
# 


##
- [ ] 


###
- # EC2 ZeroTier Bridge Security Guide

Created: 16:38
Last modified: 16:38
Tags: #security #zerotier #ec2 #aws #bridge

## Overview

This guide details security best practices for configuring and hardening an EC2 instance serving as a bridge between public internet and a private ZeroTier network.

## Architecture

```
Internet (Port 80/443) → EC2 Bridge → ZeroTier Network → Home Network
```

## Initial EC2 Setup

### 1. AMI Selection

- Use Amazon Linux 2 or Ubuntu LTS
- Keep the instance updated:
  ```bash
  # Amazon Linux 2
  sudo yum update -y
  
  # Ubuntu
  sudo apt update && sudo apt upgrade -y
  ```

### 2. Instance Sizing

- t3.micro or t3.small sufficient for bridge traffic
- Enable detailed monitoring for security events

### 3. Network Configuration

- Use a dedicated VPC with private and public subnets
- Place EC2 in public subnet with Elastic IP
- Configure security groups strictly (see below)

## Security Group Configuration

Restrict inbound traffic to only necessary ports:

```
Inbound Rules:
- HTTP (80): From 0.0.0.0/0
- HTTPS (443): From 0.0.0.0/0 
- SSH (22): From [Your Admin IP]/32 only
- ZeroTier (9993/UDP): From 0.0.0.0/0 (required for ZeroTier)

Outbound Rules:
- HTTP (80): To 0.0.0.0/0
- HTTPS (443): To 0.0.0.0/0
- ZeroTier (9993/UDP): To 0.0.0.0/0
- DNS (53/UDP): To 0.0.0.0/0
```

## ZeroTier Network Hardening

### 1. Create Dedicated ZeroTier Network

```bash
# Install ZeroTier on EC2
curl -s https://install.zerotier.com | sudo bash

# Join your ZeroTier network
sudo zerotier-cli join <network-id>

# Verify connection
sudo zerotier-cli status
sudo zerotier-cli listnetworks
```

### 2. Advanced ZeroTier Security Settings

- Enable authentication for all members
- Use private addressing only
- Enable flow rules to restrict traffic:

```
# Allow only HTTP/HTTPS traffic to pass through 
drop
  not ethertype ipv4
  or not ip_proto tcp
  or not (dst_port 80 or dst_port 443 or dst_port 8080);
accept;
```

### 3. Traffic Routing

For secure forwarding between networks:

```bash
# Enable IP forwarding
echo "net.ipv4.ip_forward = 1" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Set up NAT for return traffic
sudo iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
sudo iptables -A FORWARD -i zt+ -o eth0 -j ACCEPT
sudo iptables -A FORWARD -i eth0 -o zt+ -m state --state RELATED,ESTABLISHED -j ACCEPT

# Make iptables rules persistent
sudo yum install -y iptables-services
sudo service iptables save
sudo systemctl enable iptables
```

## System Hardening

### 1. User Management

```bash
# Create a non-root user
sudo adduser secadmin
sudo usermod -aG wheel secadmin

# Set up SSH key authentication only
sudo mkdir -p /home/<USER>/.ssh
sudo nano /home/<USER>/.ssh/authorized_keys
# [Paste your public key]

sudo chmod 700 /home/<USER>/.ssh
sudo chmod 600 /home/<USER>/.ssh/authorized_keys
sudo chown -R secadmin:secadmin /home/<USER>/.ssh
```

### 2. SSH Hardening

Edit `/etc/ssh/sshd_config`:

```
PermitRootLogin no
PasswordAuthentication no
ChallengeResponseAuthentication no
UsePAM yes
X11Forwarding no
AllowAgentForwarding no
AllowTcpForwarding yes
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2
```

Then restart SSH:
```bash
sudo systemctl restart sshd
```

### 3. Firewall Configuration (UFW)

```bash
# For Ubuntu
sudo apt install -y ufw
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 9993/udp
sudo ufw enable
```

### 4. Automated Security Updates

```bash
# For Amazon Linux 2
sudo yum install -y yum-cron
sudo nano /etc/yum/yum-cron.conf
# Set apply_updates = yes
sudo systemctl enable yum-cron
sudo systemctl start yum-cron

# For Ubuntu
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

## Monitoring and Logging

### 1. CloudWatch Integration

```bash
# Install CloudWatch agent
sudo yum install -y amazon-cloudwatch-agent

# Configure CloudWatch agent
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-config-wizard
```

### 2. Log Management

Configure `/etc/awslogs/awslogs.conf` to monitor key logs:

```ini
[/var/log/auth.log]
datetime_format = %b %d %H:%M:%S
file = /var/log/auth.log
buffer_duration = 5000
log_stream_name = {instance_id}/auth.log
initial_position = start_of_file
log_group_name = EC2/ZeroTierBridge/Logs

[/var/log/zerotier-one/zerotier-one.log]
datetime_format = %Y-%m-%d %H:%M:%S
file = /var/log/zerotier-one/zerotier-one.log
buffer_duration = 5000
log_stream_name = {instance_id}/zerotier.log
initial_position = start_of_file
log_group_name = EC2/ZeroTierBridge/Logs
```

### 3. DDoS Protection

- Enable AWS Shield Standard (free with AWS account)
- Configure CloudWatch alarms for unusual traffic patterns

## Security Auditing

### 1. Regular Pentesting

Run weekly security checks:

```bash
# Install Lynis security scanner
cd /tmp
wget https://downloads.cisofy.com/lynis/lynis-3.0.8.tar.gz
tar xvfz lynis-3.0.8.tar.gz
cd lynis
sudo ./lynis audit system
```

### 2. Network Scanning

```bash
# Install nmap
sudo yum install -y nmap

# Scan for open ports
sudo nmap -sT -O localhost

# Check for listening services
sudo netstat -tulpn
```

## Incident Response Plan

### 1. Prepare Isolation Procedure

In case of compromise:
```bash
# Disconnect from ZeroTier
sudo zerotier-cli leave <network-id>

# Block all forwarding
sudo iptables -P FORWARD DROP
```

### 2. Recovery Steps

```bash
# Create AMI backup regularly
aws ec2 create-image --instance-id <instance-id> --name "ZT-Bridge-Backup-$(date +%Y%m%d)"

# Setup automatic snapshots
# Use AWS Backup service or Lambda functions with CloudWatch Events
```

## References

- [ZeroTier Manual](https://docs.zerotier.com/)
- [AWS EC2 Security Best Practices](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-security.html)
- [Linux Server Hardening Guide](https://linuxhint.com/linux-server-hardening-guide/) 