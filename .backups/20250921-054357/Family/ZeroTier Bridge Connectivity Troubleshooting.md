---
creation_date: 2025-04-06
modification_date: 2025-04-06
type: note
aliases:
  - ZeroTier Bridge Connectivity
  - Bridge Network Troubleshooting
tags:
  - networking
  - zerotier
  - bridge
  - troubleshooting
  - flow-rules
area: Infrastructure
project: Bridge Network
resource: 
archive: 
status: active
priority: high
links: 
  - "[[Bridge network to access zero tier services from internet]]"
  - "[[EC2 Bridge Server Maintenance]]"
  - "[[EC2 Bridge Server Quick Setup Guide]]"
related:
  - "[[Media Streaming Server]]"
  - "[[Media Server]]"
---

# ZeroTier Bridge Connectivity Troubleshooting

This document addresses common connectivity issues between an EC2 bridge server and your local network over ZeroTier, focusing on flow rules optimization and connectivity patterns.

## Current Flow Rules Analysis

The current flow rules are configured to:

1. Allow HTTP/HTTPS traffic to a specific Nginx server (*************)
2. Allow return traffic from that server on the same ports
3. Allow ICMP (ping) for troubleshooting
4. Allow ARP for address resolution
5. Drop all other traffic

While this configuration is secure, it's too restrictive for general connectivity between the bridge and your local network. It only allows access to a single specific host (the Nginx server) on specific ports (80/443).

## Improved Flow Rules Options

Here are options to improve connectivity while maintaining security, from most restrictive to most permissive:

### Option 1: Allow Access to Specific Additional Services

This approach maintains strict security by explicitly allowing only necessary services.

```
# Default: Drop non-IP traffic
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;

# Allow HTTP traffic to Nginx server (Port 80)
accept
  ipprotocol tcp
  and dport 80
  and ipdest *************/32
;

# Allow HTTPS traffic to Nginx server (Port 443)
accept
  ipprotocol tcp
  and dport 443
  and ipdest *************/32
;

# Allow DNS traffic (UDP and TCP)
accept
  ipprotocol udp
  and dport 53
;

accept
  ipprotocol tcp
  and dport 53
;

# Allow SSH to all local servers (for maintenance)
accept
  ipprotocol tcp
  and dport 22
  and ipdest **********/16
;

# Allow Jellyfin traffic (port 8096)
accept
  ipprotocol tcp
  and dport 8096
  and ipdest **********/16
;

# Add rules for other necessary services here
# Example for a service on port 9696:
# accept
#   ipprotocol tcp
#   and dport 9696
#   and ipdest **********/16
# ;

# Allow return traffic from local servers
accept
  ipprotocol tcp
  and ipsrc **********/16
  and not tand dport 22
  and not tand dport 80
  and not tand dport 443
;

# Allow ICMP Echo Reply (ping response)
accept
  icmp 0 -1
;

# Allow ICMP Echo Request (ping request)
accept
  icmp 8 -1
;

# Allow ARP for address resolution
accept
  ethertype arp
;

# Drop everything else
drop;
```

### Option 2: Allow All Traffic to/from Local Subnet (More Permissive)

This approach allows all traffic between the bridge and your local network, which is simpler but less secure.

```
# Default: Drop non-IP traffic
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;

# Allow all traffic to local ZeroTier subnet
accept
  ipdest **********/16
;

# Allow all return traffic from local ZeroTier subnet
accept
  ipsrc **********/16
;

# Allow ICMP for troubleshooting
accept
  ipprotocol icmp
;

# Allow ARP for address resolution
accept
  ethertype arp
;

# Drop everything else
drop;
```

### Option 3: Use Tags to Control Access (Most Flexible)

For more advanced configurations, you can use ZeroTier tags to categorize devices and control access based on tags.

```
# Define tags
tag server
  id 1000
  default 0
  flag 1
;

tag bridge
  id 1001
  default 0
  flag 1
;

# Default: Drop non-IP traffic
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;

# Allow traffic from bridge to servers 
accept
  tand bridge
  and tdest server
;

# Allow return traffic from servers to bridge
accept
  tand server
  and tdest bridge  
;

# Allow ICMP for troubleshooting
accept
  ipprotocol icmp
;

# Allow ARP for address resolution
accept
  ethertype arp
;

# Drop everything else
drop;
```

## Common Connectivity Issues

### 1. DNS Resolution Problems

**Symptoms:**
- Can ping local servers by IP but cannot resolve hostnames
- Applications fail to connect to services by name

**Solutions:**
- Ensure flow rules allow DNS traffic (UDP and TCP port 53)
- Check if the bridge server is using a DNS server that can resolve local ZeroTier hostnames
- Consider running a local DNS server or using /etc/hosts entries

### 2. One-Way Connectivity

**Symptoms:**
- Bridge can initiate connections to local network but not vice versa
- Or local network can initiate connections to bridge but not vice versa

**Solutions:**
- Ensure flow rules allow bi-directional traffic
- Check for asymmetric routing issues (traffic taking different paths for request vs. response)
- Verify both hosts are authorized in ZeroTier Central

### 3. Port-Specific Issues

**Symptoms:**
- Some applications work but others don't
- Specific ports cannot be reached

**Solutions:**
- Add flow rules to allow traffic on the specific ports needed
- Check for any application-level firewall blocking traffic
- Verify the service is actually listening on the expected port

## Troubleshooting Steps

### 1. Verify Basic Connectivity

```bash
# From bridge to local network
ping *************  # Ping your Nginx server
traceroute *************  # Trace the route

# From local network to bridge
ping <bridge_zerotier_ip>  # Ping your bridge
traceroute <bridge_zerotier_ip>  # Trace the route
```

### 2. Test Service Connectivity

```bash
# From bridge to local network
telnet ************* 80    # Test HTTP connectivity
telnet ************* 443   # Test HTTPS connectivity

# Test other services
nc -zv ************* 8096  # Test Jellyfin connectivity
```

### 3. Check ZeroTier Status

```bash
# On both bridge and local servers
sudo zerotier-cli info
sudo zerotier-cli listnetworks
```

### 4. Analyze Traffic with tcpdump

```bash
# On the bridge server
sudo tcpdump -i ztmjfcpubr host *************

# On the local Nginx server
sudo tcpdump -i <zerotier_interface> host <bridge_zerotier_ip>
```

### 5. Test with Temporary Flow Rules

Temporarily use more permissive rules to isolate whether the issue is with the flow rules or something else:

1. Go to ZeroTier Central
2. Backup your current rules
3. Apply the simplest rules (Option 2 above)
4. Test connectivity
5. If it works, gradually add restrictions back until you find the problem

## Optimal Configuration Examples

### Basic Bridge to Local Network Configuration

Here's a balanced configuration that provides good security while allowing necessary traffic:

```
# Default: Drop non-IP traffic
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;

# Allow all TCP/UDP traffic to specific subnet
accept
  ipprotocol tcp
  and ipdest ************/24
;

accept
  ipprotocol udp
  and ipdest ************/24
;

# Allow return traffic
accept
  ipsrc ************/24
;

# Allow ICMP for troubleshooting
accept
  icmp 0 -1
;

accept
  icmp 8 -1
;

# Allow ARP for address resolution
accept
  ethertype arp
;

# Drop everything else
drop;
```

### Media Server Specific Configuration

For a setup focused on accessing media servers:

```
# Default: Drop non-IP traffic
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;

# Allow HTTP/HTTPS to Nginx
accept
  ipprotocol tcp
  and dport 80
  and ipdest *************/32
;

accept
  ipprotocol tcp
  and dport 443
  and ipdest *************/32
;

# Allow Jellyfin traffic
accept
  ipprotocol tcp
  and dport 8096
  and ipdest **********/16
;

# Allow Jellyfin discovery (UDP)
accept
  ipprotocol udp
  and dport 1900
  and ipdest **********/16
;

accept
  ipprotocol udp
  and dport 7359
  and ipdest **********/16
;

# Allow return traffic
accept
  ipsrc **********/16
;

# Allow ICMP for troubleshooting
accept
  icmp 0 -1
;

accept
  icmp 8 -1
;

# Allow ARP for address resolution
accept
  ethertype arp
;

# Drop everything else
drop;
```

## Network Architecture Considerations

### Bridge Server Placement

The EC2 bridge server should ideally:
1. Be a member of both your ZeroTier network and the public internet
2. Not have access to sensitive internal resources unless necessary
3. Be dedicated to the bridge function to limit attack surface

### Security Best Practices

1. Use a dedicated ZeroTier network for the bridge-to-local connection
2. Implement the principle of least privilege in flow rules
3. Regularly monitor access logs on both the bridge and local servers
4. Keep all systems updated with security patches

## Related Documentation

- [[EC2 Bridge Server Maintenance]] - Comprehensive guide to maintaining your bridge server
- [[Bridge network to access zero tier services from internet]] - Overview of the network architecture
- [[Media Streaming Server]] - Specific configuration for media streaming services

## Tasks

- [ ] Test and implement improved flow rules
- [ ] Document all services that need to be accessible through the bridge
- [ ] Set up monitoring for bridge-to-local network connectivity
- [ ] Create regular backup of working flow rules configuration

## Metadata
- **Original Creation**: 2025-04-06
- **Last Modified**: 2025-04-06