---
title: "mxbai-embed-large:335m"
source: "https://ollama.com/library/mxbai-embed-large:335m"
author:
published:
created: 2025-06-09
description: "State-of-the-art large embedding model from mixedbread.ai"
tags:
  - "clippings"
---
[mxbai-embed-large](https://ollama.com/library/mxbai-embed-large "mxbai-embed-large"):335m

3.6M 1 year ago

## State-of-the-art large embedding model from mixedbread.ai

embedding 335m

1 year ago

468836162de7 · 670MB

[model](https://ollama.com/library/mxbai-embed-large:335m/blobs/819c2adf5ce6)

bert

·

334M

·

F16

[license](https://ollama.com/library/mxbai-embed-large:335m/blobs/c71d239df917)

Apache License Version 2.0, January 2004

[params](https://ollama.com/library/mxbai-embed-large:335m/blobs/b837481ff855)

{ "num\_ctx": 512 }

## Readme

## mxbai-embed-large

![](https://github.com/ollama/ollama/assets/251292/215cfb6a-8efa-4e9b-824d-e5f466b58c49)

As of March 2024, this model archives SOTA performance for Bert-large sized models on the MTEB. It outperforms commercial models like OpenAIs `text-embedding-3-large` model and matches the performance of model 20x its size.

`mxbai-embed-large` was trained with no overlap of the MTEB data, which indicates that the model generalizes well across several domains, tasks and text length.

## Usage

### REST API

```
curl http://localhost:11434/api/embeddings -d '{
  "model": "mxbai-embed-large",
  "prompt": "Represent this sentence for searching relevant passages: The sky is blue because of Rayleigh scattering"
}'
```

### Python library

```
ollama.embeddings(model='mxbai-embed-large', prompt='Represent this sentence for searching relevant passages: The sky is blue because of Rayleigh scattering')
```

### Javascript library

```
ollama.embeddings({ model: 'mxbai-embed-large', prompt: 'Represent this sentence for searching relevant passages:  The sky is blue because of Rayleigh scattering' })
```

## References

[Blog post](https://www.mixedbread.ai/blog/mxbai-embed-large-v1)

[Hugging Face](https://huggingface.co/mixedbread-ai/mxbai-embed-large-v1)