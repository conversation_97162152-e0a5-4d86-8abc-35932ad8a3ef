---
title: "How to get Notion-AI-like Autocomplete with LLMs in Obsidian, offline"
source: "https://blog.myli.page/how-to-get-notion-ai-like-autocomplete-with-llms-in-obsidian-offline-8ad98a8ecc81"
author:
  - "[[<PERSON>]]"
published: 2023-07-03
created: 2025-06-13
description: "Notion AI is cool, but I use Obsidian. Also, I prefer to keep my notes offline. How do I mimic the write-for-you experience as close as possible? This is just the API server; the models themselves…"
tags:
  - "clippings"
---
[Sitemap](https://blog.myli.page/sitemap/sitemap.xml)

[Mastodon](https://me.dm/@lmy)

[Notion AI](https://www.notion.so/product/ai) is cool, but I use **Obsidian**. Also, I prefer to keep my notes **offline**. How do I mimic the **write-for-you experience** as close as possible?

In this post, I introduce my setup of enabling **LLM-powered autocomplete feature** in Obsidian.

Today’s recipe only involves 3 **ingredients**:

- [Alpaca](https://crfm.stanford.edu/2023/03/13/alpaca.html), a large language model (LLM) that you can use [for free (non-commercial)](https://github.com/tatsu-lab/stanford_alpaca/blob/main/DATA_LICENSE). It’s **Stanford’s spin on GPT-3.5**. I’m using the 13-billion-parameter edition.
- [LocalAI](https://github.com/go-skynet/LocalAI), which serves a LLM of your choice with **a web server that implements** [**OpenAI’s API**](https://platform.openai.com/docs/api-reference).
- [Obsidian Text Generator Plugin](https://github.com/nhaouari/obsidian-textgenerator-plugin) for **the autocomplete experience**.

It takes just 3 lines to spin up a LocalAI server:

```c
git clone https://github.com/go-skynet/LocalAI
# Compile with metal, because I'm on a M2 Max computer.
make BUILD_TYPE=metal build
./local-ai --models-path ./models/
```

This is just the API server; the models themselves are too big (~13 GiB) to be served from GitHub. HuggingFace is the right place to find large model files.

Caveat: Under the hood, LocalAI employs `[llama.cpp](https://github.com/ggerganov/llama.cpp)` to serve LLaMA-style models. `llama.cpp` has gone through some breaking changes recently, rendering old model files unusable. As of the time of writing, if you search for LLaMA-style models on HuggingFace, the most popular downloads would be in the old format. Today, `llama.cpp` requires models prepared in the [GGML V3](https://github.com/ggerganov/ggml) format, which you can tell by the file name following the pattern `*ggmlv3*.bin`.

Let’s get Alpaca. Download [alpaca.13b.ggmlv3.q8\_0.bin](https://huggingface.co/westseer/alpaca.13b.ggmlv3/blob/main/alpaca.13b.ggmlv3.q8_0.bin) to `./models/` of the cloned repo. Rename it to `gpt-3.5-turbo` (no extension). This is because the Text Generation Plugin has a hardcoded list of model names, so we have to pretend that we have one from the list.

In the settings of this plugin, point the endpoint to `http://localhost:8080`. Now you should be ready to go. The default trigger is double whitespace, so tap away.

Don’t be too excited about the performance, though. On my MacBook pro (`Mac14,5`), it took 20s to give me this:

![](https://miro.medium.com/v2/resize:fit:640/format:webp/1*K4jky6pbLDwmZXYmbLCE1A.png)

Apparently, Alpaca wasn’t a *Portal* fan.

Tech writer with creative analogies. | Donate: [https://ko-fi.com/mingyli](https://ko-fi.com/mingyli)

## Responses (2)

Write a response[What are your thoughts?](https://medium.com/m/signin?operation=register&redirect=https%3A%2F%2Fblog.myli.page%2Fhow-to-get-notion-ai-like-autocomplete-with-llms-in-obsidian-offline-8ad98a8ecc81&source=---post_responses--8ad98a8ecc81---------------------respond_sidebar------------------)

```c
Is this work also on windows? Thank you
```

```c
I'm curious, is there any similar tool available on the MacOS platform that allows for universal use across all applications?
```

## More from Ming

## Recommended from Medium

[

See more recommendations

](https://medium.com/?source=post_page---read_next_recirc--8ad98a8ecc81---------------------------------------)