# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Repository Overview

This is an Obsidian vault organized using the PARA method (Projects, Areas, Resources, Archive) with a focus on personal knowledge management and documentation workflows.

- **Repository**: https://github.com/fjord-an/brain-preservatives.git
- **Type**: Obsidian Knowledge Vault
- **Organization**: PARA Method

## Essential Commands

### Git Backup Commands

Sync with remote repository (matches obsidian-git plugin settings):
```bash
# Set vault path
export VAULT="/Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives"

# Manual backup matching plugin format
cd "$VAULT"
git add -A
git pull --rebase origin main
git commit -m "vault backup: $(date '+%Y-%m-%d %H:%M:%S')" || echo "Nothing to commit"
git push origin main

# One-liner variant
git -C "$VAULT" add -A && git -C "$VAULT" pull --rebase && git -C "$VAULT" commit -m "vault backup: $(date '+%Y-%m-%d %H:%M:%S')" && git -C "$VAULT" push
```

### Smart Environment Commands

The vault uses Smart Connections plugin with environment data stored in `.smart-env/`:
```bash
# View smart environment status
ls -la "$VAULT/.smart-env"

# Check vector database status
ls -lh "$VAULT/.smtcmp_vector_db.tar.gz"
```

## Vault Structure (PARA Method)

- **1-Projects/** - Active projects with defined outcomes
  - CRUCA Website/
  - Self-Hosted-AI/
  - Document-Store/
  - Note-Flows/
  - PaceySpace-Cluster/
- **2-Areas/** - Ongoing responsibilities
- **3-Resources/** - Reference materials and documentation
  - proxmox-docs/
- **4-Archive/** - Completed/inactive items
- **0-Daily Notes/** - Daily journal entries (131 files)
- **0.1-Clippings/** - Web clippings and captures
- **0.2-Tasks/** - Task management
- **Templates/** - Note templates for consistency
- **Clippings/** - Additional clipping storage
- **Family/** - Family-related notes and scripts
- **Website/** - Website documentation and scripts

## Key Obsidian Plugins

### Core Plugins
- **obsidian-git**: Automated Git backup
  - Commit message: `vault backup: {{date}}`
  - Date format: `YYYY-MM-DD HH:mm:ss`
  - Auto-backup: Disabled (manual only)
  - Pull before push: Enabled

### Task Management
- **obsidian-tasks-plugin**: Task tracking with custom statuses
  - Todo: `[ ]`
  - Done: `[x]`
  - In Progress: `[/]`
  - Cancelled: `[-]`
  - Global filter: `path does not include _templates/`

### Data & Query
- **dataview**: Dynamic content views and queries
- **omnisearch**: Enhanced search capabilities
- **templater-obsidian**: Advanced templating

### AI & Smart Features
- **smart-connections**: AI-powered note connections
- **smart-composer**: AI writing assistance
- **smart-templates**: Intelligent template system

### Other Notable Plugins
- **calendar**: Navigate daily notes
- **periodic-notes**: Daily/weekly/monthly notes
- **kanban**: Visual project boards
- **todoist-sync-plugin**: External task sync
- **metadata-menu**: Structured metadata editing

## Repository Scripts

### Infrastructure Scripts
- `3-Resources/proxmox-docs/decision-helper.sh` - Proxmox installation decision helper
- `Family/attachments/ec2-bridge-maintenance.sh` - EC2 bridge server setup for ZeroTier traffic forwarding
- `Website/cruca/setup-aws-parameter-store.sh` - AWS Parameter Store setup for CabUCA website

All AWS scripts default to region: `ap-southeast-2`

## Task Management System

### Task Locations
- Tasks are captured in daily notes and project files
- Central task dashboard: `Tasks.md`
- Task templates in `Templates/` directory

### Common Dataview Queries
```dataview
task from "1-Projects"
where !completed
sort priority desc
```

### Tasks Plugin Queries
```
not done
path does not include _templates/
show urgency
```

## Technical Notes

### Git Configuration
- Remote: `origin` → `https://github.com/fjord-an/brain-preservatives.git`
- Default branch: `main`
- `.gitignore` excludes:
  - `.obsidian/workspace*`
  - `.smart-env/`
  - `.DS_Store`
  - Sensitive files (*password*, *credential*, *secret*, *private*)

### Environment Details
- Platform: macOS
- Smart environment data: `.smart-env/` (excluded from Git)
- Vector database archive: `.smtcmp_vector_db.tar.gz` (excluded from Git)
- AWS default region: `ap-southeast-2` (per user rules)

### Important Files
- `README.md` - Repository documentation with PARA structure guide
- `Home.md` - Vault dashboard/starting point
- Daily note template: `Templates/Daily.md` or `Templates/Daily Note.md`

## Development Workflow

1. **Creating Notes**: Use templates from `Templates/` for consistency
2. **Git Sync**: Use the git backup commands above or obsidian-git plugin
3. **Task Management**: Create tasks with `- [ ]` syntax, query with Tasks plugin
4. **Smart Features**: Smart Connections plugin maintains vector embeddings in `.smart-env/`
5. **AWS Scripts**: Always use `ap-southeast-2` as default region

## Notes for AI Assistants

- This is a personal knowledge vault, not a software project
- Focus on note organization, linking, and knowledge management
- Respect PARA method folder structure
- Maintain consistent frontmatter (type, tags, status, priority, deadline)
- Preserve existing plugin configurations when making changes
- Smart environment files are local-only and should not be committed