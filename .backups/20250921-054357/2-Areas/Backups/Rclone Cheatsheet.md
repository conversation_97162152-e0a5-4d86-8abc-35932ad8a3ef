
quick preserved backup with timestamped filename:
```bash
rclone sync -MvvP --log-file "$HOME/rclone/rclone-$(date +'%Y-%m-%d_%H-%M-%S').log" source/file.xx remote:destination/file.xx
```

comprehensive backup archive flags:
```bash
sudo rclone [sync | copy] \
    --checksum \
    --metadata \
    --backup-dir [desintationdir.bak-archive]/$(date +'%Y-%m-%d_%H-%M-%S') \
    --track-renames \
    -MvvP \
    --log-file "$HOME/rclone/rclone-$(date +'%Y-%m-%d_%H-%M-%S').log" \
    [SOURCE_DIRECTORY.bak \]
    [DESTINATION_DIRECTORY:/path/to/file.bak]
```
Note: replace all in [ ]
Note: preferrably use the rclone copy command which will not overwrite eor delete reemote content if filees are missing locally. sync will make exact directory clone to destination, so use that if desireed for replica
SUDO: sudo may be reequired if errors arise due to permissions and file ownership