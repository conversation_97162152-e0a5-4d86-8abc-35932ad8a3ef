---
creation_date: 2025-09-16
modification_date: 2025-09-16
tags:
  - terminal
  - cli
  - ai-agents
  - agent
  - dev
  - code
  - claude
  - gemini
  - auggie
  - augment
area: Software-Development
mood: "7"
difficulty: intermediate
energy_required: low
key_servers:
  - sirius
  - orion
---
---
# **TABLE OF CONTENTS**
- [[#NVM Installation|NVM Installation]]
		- [[#Installing in Docker for CICD-Jobs|Installing in Docker for CICD-Jobs]]
- [[#Vector Storage for Embedding|Vector Storage for Embedding]]
- [[#Vector Storage for Embedding#`Qdrant`|`Qdrant`]]
- [[##SSHPASS|#SSHPASS]]
- [[#Option 1: Using sshpass with password prompt|Option 1: Using sshpass with password prompt]]
- [[#Option 2: Using sshpass with environment variable|Option 2: Using sshpass with environment variable]]
- [[#Option 3: Install sshpass if needed|Option 3: Install sshpass if needed]]
- [[#Security considerations:|Security considerations:]]
- [[#Better alternative: SSH key authentication|Better alternative: SSH key authentication]]
- [[#Step 1: Generate an SSH key pair (if you don't have one)|Step 1: Generate an SSH key pair (if you don't have one)]]
- [[#Step 2: Copy the public key to the root user|Step 2: Copy the public key to the root user]]
- [[#Step 2: Copy the public key to the root user#Option A: Using ssh-copy-id with sshpass|Option A: Using ssh-copy-id with sshpass]]
- [[#Step 2: Copy the public key to the root user#Option B: Manual copy (if ssh-copy-id doesn't work)|Option B: Manual copy (if ssh-copy-id doesn't work)]]
- [[#Step 3: Test the key authentication|Step 3: Test the key authentication]]
- [[#Step 4: Configure SSH client for convenience|Step 4: Configure SSH client for convenience]]

---

> *Skip to * [[#Agents Command Usage]] *once these dependancies have been setup, to run and update the Agents!*
# Dependancies
#dependancies #packages #system #library #libraries #npm #npx #environment #nvm

### NVM Installation
	For Installing AND Updating
> with curl
```bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | bash
```
> with wget
```bash
wget -qO- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | bash
```
> Installing in docker
	*When invoking bash as a non-interactive shell, like in a Docker container, none of the regular profile files are sourced. In order to use `nvm`, `node`, and `npm` like normal, you can instead specify the special `BASH_ENV`variable, which bash sources when invoked non-interactively.*	
```dockerfile
# Use bash for the shell
SHELL ["/bin/bash", "-o", "pipefail", "-c"]

# Create a script file sourced by both interactive and non-interactive bash shells
ENV BASH_ENV /home/<USER>/.bash_env
RUN touch "${BASH_ENV}"
RUN echo '. "${BASH_ENV}"' >> ~/.bashrc

# Download and install nvm
RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | PROFILE="${BASH_ENV}" bash
RUN echo node > .nvmrc
RUN nvm install
```

> *Skip to * [[#Agents Command Usage]] *once these dependancies have been setup, to run and update the Agents!*
##### Installing in Docker for CICD-Jobs
[NVM_Docs](https://github.com/nvm-sh/nvm#installing-in-docker-for-cicd-jobs)
More robust, works in CI/CD-Jobs. Can be run in interactive and non-interactive containers. See [#3531](https://github.com/nvm-sh/nvm/issues/3531).
```dockerfile
FROM ubuntu:latest
ARG NODE_VERSION=20

# install curl
RUN apt update && apt install curl -y

# install nvm
RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | bash

# set env
ENV NVM_DIR=/root/.nvm

# install node
RUN bash -c "source $NVM_DIR/nvm.sh && nvm install $NODE_VERSION"

# set ENTRYPOINT for reloading nvm-environment
ENTRYPOINT ["bash", "-c", "source $NVM_DIR/nvm.sh && exec \"$@\"", "--"]

# set cmd to bash
CMD ["/bin/bash"]

```
This example defaults to installation of nodejs version 20.x.y. Optionally you can easily override the version with docker build args like:
```
docker build -t nvmimage --build-arg NODE_VERSION=19 .
```
After creation of the image you can start container interactively and run commands, for example:
```
docker run --rm -it nvmimage

root@0a6b5a237c14:/# nvm -v
0.40.3

root@0a6b5a237c14:/# node -v
v19.9.0

root@0a6b5a237c14:/# npm -v
9.6.3
```

Noninteractive example:
```
user@host:/tmp/test $ docker run --rm -it nvmimage node -v
v19.9.0
user@host:/tmp/test $ docker run --rm -it nvmimage npm -v
9.6.3
```

# Agents Command Usage
*Skip to * [[#MCP]] *For the Agentic special sauce*
>ordered by my *current* preferences

CLI Agent Rankings
1. ## Claude Code (Anthropic)
	```bash
	?
	```
2. ## Auggie CLI Agent (Augment Code)
	``` bash
	npm install -g @augmentcode/auggie
	```
3. ## Qwen Coder (Alibaba)
	```bash
	npm install -g @qwen-code/qwen-code@latest
	qwen –version
	qwen
	```
4. ## Gemini CLI (Google)
```bash

```




# MCP

## Vector Storage for Embedding
### `Qdrant`
> 	[[Qdrant info]]


# SSH sessions with cli agents

## #SSHPASS
you can use `sshpass` to pass #passwords to SSH connections. Here are a few approaches:

## Option 1: Using sshpass with password prompt
```bash
sshpass -p "$(read -s -p 'Password: '; echo $REPLY)" ssh user@hostname
```
## Option 2: Using sshpass with environment variable
First set the password as an environment variable:
```bash
export SSHPASS="your_password_here"
sshpass -e ssh user@hostname
```
## Option 3: Install sshpass if needed
On macOS, you'll need to install `sshpass` first:
```bash
brew install hudochenkov/sshpass/sshpass
```
## Security considerations:
- The password will be visible in process lists temporarily
- Consider using SSH keys instead for better security
- If you must use passwords, the environment variable approach (Option 2) is slightly more secure than passing it directly

## Better alternative: SSH key authentication
If possible, I'd recommend setting up SSH key authentication instead:
```bash
ssh-keygen -t ed25519 -C "<EMAIL>"
ssh-copy-id user@hostname
```
>You can definitely set up SSH key authentication for the root user! This is actually a common and recommended practice for server administration. Here's how:

## Step 1: Generate an SSH key pair (if you don't have one)
```bash
ssh-keygen -t ed25519 -C "root-access-$(hostname)"
```
- Save it with a descriptive name like `~/.ssh/id_ed25519_root`
- Use a strong passphrase for the private key

## Step 2: Copy the public key to the root user
You have a few options:

### Option A: Using ssh-copy-id with sshpass
```bash
sshpass -p "$(read -s -p 'Root password: '; echo $REPLY)" ssh-copy-id -i ~/.ssh/id_ed25519_root.pub root@your-server
```
### Option B: Manual copy (if ssh-copy-id doesn't work)
1. First, get your public key content:
```bash
cat ~/.ssh/id_ed25519_root.pub
```
2. Then connect with password and add it manually:
```bash
sshpass -p "$(read -s -p 'Root password: '; echo $REPLY)" ssh root@your-server "mkdir -p ~/.ssh && chmod 700 ~/.ssh && echo 'YOUR_PUBLIC_KEY_CONTENT_HERE' >> ~/.ssh/authorized_keys && chmod 600 ~/.ssh/authorized_keys"
```
## Step 3: Test the key authentication
```bash
ssh -i ~/.ssh/id_ed25519_root root@your-server
```
## Step 4: Configure SSH client for convenience
Add to `~/.ssh/config`:
```
Host your-server-name
    HostName your-server-ip
    User root
    IdentityFile ~/.ssh/id_ed25519_root
    IdentitiesOnly yes
```
Then you can simply use: `ssh your-server-name`

Would you like me to help you set this up step by step?
