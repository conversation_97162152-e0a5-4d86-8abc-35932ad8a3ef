---
creation date: 2025-04-27 02:26
modification date: Sunday 27th April 2025 02:26:44
type: daily
date: 2025-04-27
day_of_week: Sunday
week: 2025-W18
month: 2025-04
tags: [daily, 2025-04]
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-04-27 - Sunday

<< [[2025-04-26]] | [[2025-04-28]] >>

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
- replaced text with marg's requested update on studs [page for rhydian:
	- This is my latest stud boy now growing up and working with the girls His name is Yendo<PERSON> and his mum is <PERSON> and his dad is <PERSON><PERSON> He is a red smoke and is growing into a very handsome big boy with a very friendly temperament
- fixed studs
- fixed queens

## Tasks
- [ ] replace the JWT in appsettings of yendorcats backen to a stronger key before production

## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-04-27)
SORT time ASC
```

## Today's Tasks
```tasks
not done
due on 2025-04-27
```

## Upcoming Tasks
```tasks
not done
due after 2025-04-27
due before 2025-05-04
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-04-27 Meeting|Create New Meeting]]
- [[2025-04-27 Task|Create New Task]]
- [[2025-04-27 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-04|Monthly Overview]]
- [[2025-W18|Weekly Overview]]
- [[Tasks]]
- [[Home]]
