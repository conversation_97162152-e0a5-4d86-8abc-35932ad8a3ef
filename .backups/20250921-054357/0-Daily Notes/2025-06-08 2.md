---
creation date: 2025-04-21 23:12
modification date: Monday 21st April 2025 23:12:09
type: daily
date: 2025-04-21
day_of_week: Monday
week: 2025-W17
month: 2025-04
tags: [daily, 2025-04]
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-04-21 - Monday

<< [[2025-04-20]] | [[2025-04-22]] >>

## Quick Capture

- [x] rent;  [completion:: 2025-06-08]
	- [x] &470.00 - paid J  [completion:: 2025-06-08]

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment
	*could not afford (ringing about partial payment tomorrow (09-06-25)*

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25
		*could not pay either $111.40 nor $109.00 this week

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;
	• have paid over $30 so far

- [ ] license renewal7[[]()]()
	• - $55.12 - paid V

- [ ] groceries + %:
- ﻿﻿paid $49.00 on groceries
- ﻿﻿O was $150.00

- ﻿plan to get more today - 08.06.25 (aiming for $100.00 if you could put in)*
- ﻿have NOT done proper grocery shop (school + dinner) yet - doing online / uber today)*

- [ ] $ currently after :

= $280.41
## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-04-21)
SORT time ASC
```

## Today's Tasks
```tasks
not done
due on 2025-04-21
```

## Upcoming Tasks
```tasks
not done
due after 2025-04-21
due before 2025-04-28
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-04-21 Meeting|Create New Meeting]]
- [[2025-04-21 Task|Create New Task]]
- [[2025-04-21 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-04|Monthly Overview]]
- [[2025-W17|Weekly Overview]]
- [[Tasks]]
- [[Home]]
