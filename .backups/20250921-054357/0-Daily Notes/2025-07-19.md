---
creation_date: <% tp.date.now("YYYY-MM-DD HH:mm") %>
modification_date: <% tp.date.now("dddd Do MMMM YYYY HH:mm:ss") %>
type: daily
status: active
priority: medium
area_category: Personal
owner: Jordan
tags:
  - daily
  - <%
  - tp.date.now("YYYY-MM")
  - "%>"
date: <% tp.date.now("YYYY-MM-DD") %>
day_of_week: <% tp.date.now("dddd") %>
week: <% tp.date.now("YYYY-[W]WW") %>
month: <% tp.date.now("YYYY-MM") %>
mood: ""
energy_level: 
weather: ""
location: ""
related_projects: 
related_areas: 
related_resources: 
related_people:
  - "[[Jordan]]"
task_priority: medium
task_context: admin
---
 %%  %% 
# <% tp.date.now("YYYY-MM-DD") %> - <% tp.date.now("dddd") %>

<< [[<% tp.date.now("YYYY-MM-DD", -1) %>]] | [[<% tp.date.now("YYYY-MM-DD", 1) %>]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
-

## Tasks for Today
#todo #tasks #outstanding
<%*
// Function to get unfinished tasks from ALL daily notes
async function getAllUnfinishedTasks() {
    const today = tp.date.now("YYYY-MM-DD");
    const dailyNotesFolder = "cruca-documentation/0-Journals-Daily Notes";
    let unfinishedTasks = [];

    try {
        // Get the daily notes folder
        const folder = app.vault.getAbstractFileByPath(dailyNotesFolder);

        if (folder && folder.children) {
            // Process all files in the daily notes folder
            for (const file of folder.children) {
                // Skip if it's not a markdown file or if it's today's note
                if (!file.name.endsWith('.md') || file.name === `${today}.md` || file.name === 'Daily Notes TOC.md') {
                    continue;
                }

                // Extract date from filename (assuming YYYY-MM-DD.md format)
                const dateMatch = file.name.match(/^(\d{4}-\d{2}-\d{2})\.md$/);
                if (!dateMatch) continue;

                const fileDate = dateMatch[1];

                try {
                    const content = await app.vault.read(file);
                    // Match various task formats: - [ ], * [ ], + [ ]
                    // Also handle indented tasks
                    const tasks = content.match(/^[\s]*[-\*\+] \[ \] .+$/gm);

                    if (tasks && tasks.length > 0) {
                        unfinishedTasks.push({
                            date: fileDate,
                            fileName: file.name,
                            tasks: tasks,
                            taskCount: tasks.length
                        });
                    }
                } catch (error) {
                    // File might not be readable, skip it
                    continue;
                }
            }
        }
    } catch (error) {
        // Folder might not exist or be accessible
        console.log("Could not access daily notes folder:", error);
    }

    // Sort by date (most recent first)
    unfinishedTasks.sort((a, b) => b.date.localeCompare(a.date));

    return unfinishedTasks;
}

// Get and display unfinished tasks
const allUnfinishedTasks = await getAllUnfinishedTasks();

if (allUnfinishedTasks.length > 0) {
    const totalTasks = allUnfinishedTasks.reduce((sum, day) => sum + day.taskCount, 0);
    tR += `### 📋 Carried Forward Tasks (${totalTasks} tasks from ${allUnfinishedTasks.length} days)\n`;

    // Group by recent vs older tasks
    const recentTasks = allUnfinishedTasks.filter(day => {
        const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(day.date)) / (1000 * 60 * 60 * 24));
        return daysDiff <= 7;
    });

    const olderTasks = allUnfinishedTasks.filter(day => {
        const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(day.date)) / (1000 * 60 * 60 * 24));
        return daysDiff > 7;
    });

    // Show recent tasks first (last 7 days)
    if (recentTasks.length > 0) {
        tR += "#### 🔥 Recent Tasks (Last 7 Days)\n";
        recentTasks.forEach(dayTasks => {
            const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(dayTasks.date)) / (1000 * 60 * 60 * 24));
            const daysAgo = daysDiff === 1 ? "yesterday" : `${daysDiff} days ago`;
            tR += `##### From ${dayTasks.date} (${daysAgo}) - ${dayTasks.taskCount} tasks\n`;
            dayTasks.tasks.forEach(task => {
                tR += task + "\n";
            });
            tR += "\n";
        });
    }

    // Show older tasks in collapsed format
    if (olderTasks.length > 0) {
        const olderTaskCount = olderTasks.reduce((sum, day) => sum + day.taskCount, 0);
        tR += `#### 📚 Older Tasks (${olderTaskCount} tasks from ${olderTasks.length} days)\n`;
        tR += "<details><summary>Click to expand older tasks</summary>\n\n";

        olderTasks.forEach(dayTasks => {
            const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(dayTasks.date)) / (1000 * 60 * 60 * 24));
            tR += `##### From ${dayTasks.date} (${daysDiff} days ago) - ${dayTasks.taskCount} tasks\n`;
            dayTasks.tasks.forEach(task => {
                tR += task + "\n";
            });
            tR += "\n";
        });

        tR += "</details>\n\n";
    }

    tR += "### ✨ New Tasks for Today\n";
} else {
    tR += "### ✨ Tasks for Today\n";
}
%>
- [ ]
- [ ]
- [ ]

## CI/CD
<!-- Administrative tasks, correspondence, documentation -->
-

## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
-

## Maintainence
<!-- Compliance, training, documentation -->
-

## Development/Updates
<!-- Items requiring follow-up action -->
- [ ]
- [ ]

## Journal
<!-- How was your day? What happened? What did you learn? -->
-

## Notes
<!-- Any other notes or information -->
-

## 🎯 Today's Focus
<!-- Show only the most important tasks -->

### 🔥 Must Do Today
<%*
const urgentQuery = `
TASK
FROM "1-Projects" OR "2-Areas" OR "0-Daily Notes"
WHERE !completed
  AND task_priority = "urgent"
  AND (due <= date(${tp.date.now("YYYY-MM-DD")}) OR contains(tags, "#today"))
SORT file.path ASC
LIMIT 3
`;
tR += "```dataview\n" + urgentQuery + "\n```\n\n";
%>

### ⚡ High Impact Work
<%*
const highQuery = `
TASK
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND task_priority = "high"
  AND (due <= date(${tp.date.now("YYYY-MM-DD")}) + dur(2 days) OR !due)
SORT due ASC
LIMIT 5
`;
tR += "```dataview\n" + highQuery + "\n```\n\n";
%>

### 📋 If Time Permits
<%*
const mediumQuery = `
TASK
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND task_priority = "medium"
  AND estimated_time <= "30min"
SORT estimated_time ASC
LIMIT 4
`;
tR += "```dataview\n" + mediumQuery + "\n```\n\n";
%>

## 🚀 Active Project Status
<%*
const projectQuery = `
TABLE WITHOUT ID
  file.link as "🚀 Project",
  choice(priority = "critical", "🔥", choice(priority = "high", "⚡", "📋")) as "P",
  completion_percentage + "%" as "Done",
  choice(deadline < date(${tp.date.now("YYYY-MM-DD")}), "⚠️ OVERDUE", choice(deadline <= date(${tp.date.now("YYYY-MM-DD")}) + dur(7 days), "🔜 Soon", "📅 " + string(deadline))) as "Deadline"
FROM "1-Projects"
WHERE status = "active"
  AND (file.mtime >= date(${tp.date.now("YYYY-MM-DD")}) - dur(3 days)
   OR deadline <= date(${tp.date.now("YYYY-MM-DD")}) + dur(14 days)
   OR priority = "critical"
   OR priority = "high")
SORT choice(deadline < date(${tp.date.now("YYYY-MM-DD")}), 1, choice(priority = "critical", 2, 3)) ASC
LIMIT 5
`;
tR += "```dataview\n" + projectQuery + "\n```\n\n";
%>

## 📅 Today's Meetings
<%*
const meetingQuery = `
TABLE WITHOUT ID
  file.link as "📅 Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(${tp.date.now("YYYY-MM-DD")})
SORT time ASC
`;
tR += "```dataview\n" + meetingQuery + "\n```\n\n";
%>

## ⚠️ Overdue & Urgent
<%*
const overdueQuery = `
TABLE WITHOUT ID
  "⚠️" as "",
  file.link as "OVERDUE TASKS",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  due as "Was Due"
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND due < date(${tp.date.now("YYYY-MM-DD")})
SORT task_priority ASC, due ASC
LIMIT 8
`;
tR += "```dataview\n" + overdueQuery + "\n```\n\n";
%>

## 🔄 Context-Based Quick Tasks
<%*
const quickQuery = `
TABLE WITHOUT ID
  choice(task_priority = "high", "⚡", choice(task_priority = "medium", "📋", "🔄")) as "P",
  file.link as "QUICK WINS (15min)",
  choice(energy_required, energy_required, "medium") as "Energy"
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND estimated_time = "15min"
SORT task_priority ASC, energy_required DESC
LIMIT 6
`;
tR += "```dataview\n" + quickQuery + "\n```\n\n";
%>

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[<% tp.date.now("YYYY-MM-DD") %> Meeting|Create New Meeting]]
- [[<% tp.date.now("YYYY-MM-DD") %> Task|Create New Task]]
- [[<% tp.date.now("YYYY-MM-DD") %> Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[<% tp.date.now("YYYY-MM") %>|Monthly Overview]]
- [[<% tp.date.now("YYYY-[W]WW") %>|Weekly Overview]]
- [[Tasks]]
- [[Home]]
