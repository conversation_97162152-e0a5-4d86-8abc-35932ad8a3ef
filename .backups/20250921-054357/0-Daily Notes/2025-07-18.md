---
# CORE METADATA
creation_date: 2025-07-18 16:18
modification_date: Friday 18th July 2025 16:18:12
type: daily
status: active
priority: medium
area_category: Personal
owner: Jordan
tags: [daily, 2025-07]

# DAILY SPECIFICS
date: 2025-07-18
day_of_week: Friday
week: 2025-W29
month: 2025-07
mood: ""
energy_level: ""
weather: ""
location: ""

# RELATIONSHIPS (Enhanced System)
related_projects: []
related_areas: []
related_resources: []
related_people: ["[[Jordan]]"]

# TASK MANAGEMENT
task_priority: medium
task_context: admin
---

# 2025-07-18 - Friday

<< [[2025-07-17]] | [[2025-07-19]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=brain-preservatives&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
-

## Tasks for Today
#todo #tasks #outstanding
### ✨ Tasks for Today

- [ ] 
- [ ] 
- [ ] 

## CI/CD
<!-- Administrative tasks, correspondence, documentation -->
-

## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
-

## Maintainence
<!-- Compliance, training, documentation -->
-

## Development/Updates
<!-- Items requiring follow-up action -->
- [ ]
- [ ]

## Journal
<!-- How was your day? What happened? What did you learn? -->
-

## Notes
<!-- Any other notes or information -->
-

## 🎯 Today's Focus
<!-- Show only the most important tasks -->

### 🔥 Must Do Today
```dataview

TASK
FROM "1-Projects" OR "2-Areas" OR "0-Daily Notes"
WHERE !completed
  AND task_priority = "urgent"
  AND (due <= date(2025-07-18) OR contains(tags, "#today"))
SORT file.path ASC
LIMIT 3

```



### ⚡ High Impact Work
```dataview

TASK
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND task_priority = "high"
  AND (due <= date(2025-07-18) + dur(2 days) OR !due)
SORT due ASC
LIMIT 5

```



### 📋 If Time Permits
```dataview

TASK
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND task_priority = "medium"
  AND estimated_time <= "30min"
SORT estimated_time ASC
LIMIT 4

```



## 🚀 Active Project Status
```dataview

TABLE WITHOUT ID
  file.link as "🚀 Project",
  choice(priority = "critical", "🔥", choice(priority = "high", "⚡", "📋")) as "P",
  completion_percentage + "%" as "Done",
  choice(deadline < date(2025-07-18), "⚠️ OVERDUE", choice(deadline <= date(2025-07-18) + dur(7 days), "🔜 Soon", "📅 " + string(deadline))) as "Deadline"
FROM "1-Projects"
WHERE status = "active"
  AND (file.mtime >= date(2025-07-18) - dur(3 days)
   OR deadline <= date(2025-07-18) + dur(14 days)
   OR priority = "critical"
   OR priority = "high")
SORT choice(deadline < date(2025-07-18), 1, choice(priority = "critical", 2, 3)) ASC
LIMIT 5

```



## 📅 Today's Meetings
```dataview

TABLE WITHOUT ID
  file.link as "📅 Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-07-18)
SORT time ASC

```



## ⚠️ Overdue & Urgent
```dataview

TABLE WITHOUT ID
  "⚠️" as "",
  file.link as "OVERDUE TASKS",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  due as "Was Due"
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND due < date(2025-07-18)
SORT task_priority ASC, due ASC
LIMIT 8

```



## 🔄 Context-Based Quick Tasks
```dataview

TABLE WITHOUT ID
  choice(task_priority = "high", "⚡", choice(task_priority = "medium", "📋", "🔄")) as "P",
  file.link as "QUICK WINS (15min)",
  choice(energy_required, energy_required, "medium") as "Energy"
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND estimated_time = "15min"
SORT task_priority ASC, energy_required DESC
LIMIT 6

```



## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-07-18 Meeting|Create New Meeting]]
- [[2025-07-18 Task|Create New Task]]
- [[2025-07-18 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-07|Monthly Overview]]
- [[2025-W29|Weekly Overview]]
- [[Tasks]]
- [[Home]]
