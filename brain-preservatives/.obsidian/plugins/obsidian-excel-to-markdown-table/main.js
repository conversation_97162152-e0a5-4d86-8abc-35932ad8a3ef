/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
var __export = (target, all) => {
  __markAsModule(target);
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __reExport = (target, module2, desc) => {
  if (module2 && typeof module2 === "object" || typeof module2 === "function") {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && key !== "default")
        __defProp(target, key, { get: () => module2[key], enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable });
  }
  return target;
};
var __toModule = (module2) => {
  return __reExport(__markAsModule(__defProp(module2 != null ? __create(__getProtoOf(module2)) : {}, "default", module2 && module2.__esModule && "default" in module2 ? { get: () => module2.default, enumerable: true } : { value: module2, enumerable: true })), module2);
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/main.ts
__export(exports, {
  default: () => ExcelToMarkdownTablePlugin
});
var import_obsidian = __toModule(require("obsidian"));

// src/table-alignment-syntax.ts
var ALIGNED_LEFT_SYNTAX = {
  prefix: "",
  postfix: "",
  adjust: 0
};
var ALIGNED_RIGHT_SYNTAX = {
  prefix: "",
  postfix: ":",
  adjust: 1
};
var ALIGNED_CENTER_SYNTAX = {
  prefix: ":",
  postfix: ":",
  adjust: 2
};

// src/excel-markdown-helpers.ts
var ALIGNED_LEFT = "l";
var ALIGNED_RIGHT = "r";
var ALIGNED_CENTER = "c";
var EXCEL_COLUMN_DELIMITER = "	";
var MARKDOWN_NEWLINE = "<br/>";
var UNESCAPED_DOUBLE_QUOTE = '"';
var EXCEL_ROW_DELIMITER_REGEX = /[\n\u0085\u2028\u2029]|\r\n?/g;
var COLUMN_ALIGNMENT_REGEX = /^(\^[lcr])/i;
var EXCEL_NEWLINE_ESCAPED_CELL_REGEX = /"([^\t]*(?<=[^\r])\n[^\t]*)"/g;
var EXCEL_NEWLINE_REGEX = /\n/g;
var EXCEL_DOUBLE_QUOTE_ESCAPED_REGEX = /""/g;
function addMarkdownSyntax(rows, columnWidths) {
  return rows.map(function(row, rowIndex) {
    return "| " + row.map(function(column, index) {
      column = column.replace("|", "\\|");
      return column + Array(columnWidths[index] - column.length + 1).join(" ");
    }).join(" | ") + " |";
  });
}
function addAlignmentSyntax(markdownRows, columnWidths, colAlignments) {
  let result = Object.assign([], markdownRows);
  result.splice(1, 0, "|" + columnWidths.map(function(width, index) {
    let { prefix, postfix, adjust } = calculateAlignmentMarkdownSyntaxMetadata(colAlignments[index]);
    return prefix + Array(columnWidths[index] + 3 - adjust).join("-") + postfix;
  }).join("|") + "|");
  return result;
}
function calculateAlignmentMarkdownSyntaxMetadata(alignment) {
  switch (alignment) {
    case ALIGNED_LEFT:
      return ALIGNED_LEFT_SYNTAX;
    case ALIGNED_CENTER:
      return ALIGNED_CENTER_SYNTAX;
    case ALIGNED_RIGHT:
      return ALIGNED_RIGHT_SYNTAX;
    default:
      return ALIGNED_LEFT_SYNTAX;
  }
}
function getColumnWidthsAndAlignments(rows) {
  let colAlignments = [];
  return {
    columnWidths: rows[0].map(function(column, columnIndex) {
      let alignment = columnAlignment(column);
      colAlignments.push(alignment);
      column = column.replace(COLUMN_ALIGNMENT_REGEX, "");
      rows[0][columnIndex] = column;
      return columnWidth(rows, columnIndex);
    }),
    colAlignments
  };
}
function columnAlignment(columnHeaderText) {
  var m = columnHeaderText.match(COLUMN_ALIGNMENT_REGEX);
  if (m) {
    var alignChar = m[1][1].toLowerCase();
    return columnAlignmentFromChar(alignChar);
  }
  return ALIGNED_LEFT;
}
function columnAlignmentFromChar(alignChar) {
  switch (alignChar) {
    case ALIGNED_LEFT:
      return ALIGNED_LEFT;
    case ALIGNED_CENTER:
      return ALIGNED_CENTER;
    case ALIGNED_RIGHT:
      return ALIGNED_RIGHT;
    default:
      return ALIGNED_LEFT;
  }
}
function columnWidth(rows, columnIndex) {
  return Math.max.apply(null, rows.map(function(row) {
    return row[columnIndex] && row[columnIndex].length || 0;
  }));
}
function splitIntoRowsAndColumns(data) {
  var rows = data.split(EXCEL_ROW_DELIMITER_REGEX).map(function(row) {
    return row.split(EXCEL_COLUMN_DELIMITER);
  });
  return rows;
}
function replaceIntraCellNewline(data) {
  let cellReplacer = (_) => _.slice(1, -1).replace(EXCEL_DOUBLE_QUOTE_ESCAPED_REGEX, UNESCAPED_DOUBLE_QUOTE).replace(EXCEL_NEWLINE_REGEX, MARKDOWN_NEWLINE);
  return data.replace(EXCEL_NEWLINE_ESCAPED_CELL_REGEX, cellReplacer);
}

// src/excel-markdown-tables.ts
var LINE_ENDING = "\n";
function excelToMarkdown(rawData) {
  let data = rawData.trim();
  var intraCellNewlineReplacedData = replaceIntraCellNewline(data);
  var rows = splitIntoRowsAndColumns(intraCellNewlineReplacedData);
  var { columnWidths, colAlignments } = getColumnWidthsAndAlignments(rows);
  const markdownRows = addMarkdownSyntax(rows, columnWidths);
  return addAlignmentSyntax(markdownRows, columnWidths, colAlignments).join(LINE_ENDING);
}
function getExcelRows(rawData) {
  let data = rawData.trim();
  var intraCellNewlineReplacedData = replaceIntraCellNewline(data);
  return splitIntoRowsAndColumns(intraCellNewlineReplacedData);
}
function excelRowsToMarkdown(rows) {
  var { columnWidths, colAlignments } = getColumnWidthsAndAlignments(rows);
  const markdownRows = addMarkdownSyntax(rows, columnWidths);
  return addAlignmentSyntax(markdownRows, columnWidths, colAlignments).join(LINE_ENDING);
}
function isExcelData(rows) {
  return rows && rows[0] && rows[0].length > 1 ? true : false;
}

// src/main.ts
var ExcelToMarkdownTablePlugin = class extends import_obsidian.Plugin {
  constructor() {
    super(...arguments);
    this.pasteHandler = (evt, editor) => {
      if (evt.clipboardData === null) {
        return;
      }
      if (evt.clipboardData.types.length === 1 && evt.clipboardData.types[0] === "text/plain") {
        return;
      }
      const rawData = evt.clipboardData.getData("text");
      const rows = getExcelRows(rawData);
      if (isExcelData(rows)) {
        const markdownData = excelRowsToMarkdown(rows);
        editor.replaceSelection(markdownData + "\n");
        evt.preventDefault();
      }
    };
  }
  onload() {
    return __async(this, null, function* () {
      this.addCommand({
        id: "excel-to-markdown-table",
        name: "Excel to Markdown",
        hotkeys: [
          {
            modifiers: ["Mod", "Alt"],
            key: "v"
          }
        ],
        editorCallback: (editor, view) => __async(this, null, function* () {
          const text = yield navigator.clipboard.readText();
          editor.replaceSelection(excelToMarkdown(text));
        })
      });
      this.app.workspace.on("editor-paste", this.pasteHandler);
    });
  }
  onunload() {
    this.app.workspace.off("editor-paste", this.pasteHandler);
  }
};

/* nosourcemap */