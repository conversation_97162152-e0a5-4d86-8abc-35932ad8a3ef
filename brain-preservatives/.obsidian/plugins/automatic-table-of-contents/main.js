// Automatic Table Of Contents 1.7.3 (https://github.com/johansatge)
var c=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var d=c((ee,k)=>{var g=class{},p={},v=class{},w=t=>t;if(A()){let t=require("obsidian");g=t.Plugin,p=t.<PERSON><PERSON><PERSON>,v=t.<PERSON>down<PERSON>hild,w=t.htmlToMarkdown}k.exports={Plugin:g,MarkdownRenderer:p,MarkdownRenderChild:v,htmlToMarkdown:w};function A(){return typeof process!="object"?!0:!process.env||!process.env.JEST_WORKER_ID}});var y=c((re,b)=>{var s={title:{type:"string",default:"",comment:""},style:{type:"value",default:"nestedList",values:["nestedList","nestedOrderedList","inlineFirstLevel"],comment:"TOC style (nestedList|nestedOrderedList|inlineFirstLevel)"},minLevel:{type:"number",default:0,comment:"Include headings from the specified level"},maxLevel:{type:"number",default:0,comment:"Include headings up to the specified level"},include:{type:"regexp",default:null,comment:""},exclude:{type:"regexp",default:null,comment:""},includeLinks:{type:"boolean",default:!0,comment:"Make headings clickable"},hideWhenEmpty:{type:"boolean",default:!1,comment:"Hide TOC if no headings are found"},debugInConsole:{type:"boolean",default:!1,comment:"Print debug info in Obsidian console"}};b.exports={getOptionsDocs:P,parseOptionsFromSourceText:E};function P(){let t=[];for(let e of Object.keys(s)){let n=s[e],r=n.comment.length>0?` # ${n.comment}`:"",o=n.default!==null?n.default:"";t.push(`${e}: ${o}${r}`)}return t.join(`
`)}function E(t=""){let e={};for(let n of Object.keys(s))e[n]=s[n].default;for(let n of t.split(`
`)){let r=R(n);r!==null&&(e[r.name]=r.value)}return e}function R(t){let e=t.match(/([a-zA-Z0-9._ ]+):(.*)/);if(t.startsWith("#")||!e)return null;let n=e[1].trim(),r=s[n],o=e[2].trim();(!r||!["string","regexp"].includes(r.type))&&(o=o.replace(/#[^#]*$/,"").trim());let a=new Error(`Invalid value for \`${n}\``);if(r&&r.type==="number"){let i=Number.parseInt(o);if(i<0)throw a;return{name:n,value:i}}if(r&&r.type==="boolean"){if(!["true","false"].includes(o))throw a;return{name:n,value:o==="true"}}if(r&&r.type==="value"){if(!r.values.includes(o))throw a;return{name:n,value:o}}if(r&&r.type==="string")return{name:n,value:o};if(r&&r.type==="regexp"){if(o==="null"||o.length===0)return null;try{let i=/^\/(.*)\/([a-z]*)/.exec(o);if(!i)throw new Error("Invalid regexp");let l=new RegExp(i[1],i[2]);return{name:n,value:l}}catch{throw a}}return null}});var x=c((oe,L)=>{var{htmlToMarkdown:S}=d();L.exports={isHeadingAllowed:_,getFormattedMarkdownHeading:W};function _(t,e){return e.include?e.include.test(t):e.exclude?!e.exclude.test(t):!0}function W(t,e){if(e.includeLinks){let n=t;n=$(n),n=j(n),n=M(n,!1);let r=t;return r=M(r,!0),r=q(r),`[[#${r}|${n}]]`}return t}function $(t){return t.replaceAll("*","").replaceAll(/(\W|^)_+(\S)(.*?\S)?_+(\W|$)/g,"$1$2$3$4").replaceAll("`","").replaceAll("==","").replaceAll("~~","").replace(/\[([^\]]+)\]\([^)]+\)/g,"$1")}function j(t){return $(S(t))}function M(t,e){return t.replace(/\[\[([^\]]+)\|([^\]]+)\]\]/g,e?"$1 $2":"$2").replace(/\[\[([^\]]+)\]\]/g,"$1").replaceAll("[[","").replaceAll("| ",e?"":"- ").replaceAll("|",e?" ":"-")}function q(t){return t.replaceAll("#"," ")}});var F=c((le,H)=>{var{isHeadingAllowed:C,getFormattedMarkdownHeading:I}=x();H.exports={getMarkdownFromHeadings:N};function N(t,e){let n={nestedList:D,nestedOrderedList:B,inlineFirstLevel:z},r="";if(e.title&&e.title.length>0){let a=e.style==="inlineFirstLevel"?" ":`
`;r+=`${e.title}${a}`}let o=n[e.style](t,e);return o===null?e.hideWhenEmpty?"":`${r}_Table of contents: no headings found_`:r+o}function D(t,e){return O(t,!1,e)}function B(t,e){return O(t,!0,e)}function O(t,e,n){let r=e?"1.":"-",o=[],a=n.minLevel>0?n.minLevel:Math.min(...t.map(l=>l.level)),i=0;for(let l of t)if(!(i>0&&l.level>i)){if(l.level<=i&&(i=0),!C(l.heading,n)){i=l.level;continue}l.level<a||n.maxLevel>0&&l.level>n.maxLevel||l.heading.length!==0&&o.push(`${"	".repeat(l.level-a)}${r} ${I(l.heading,n)}`)}return o.length>0?o.join(`
`):null}function z(t,e){let n=e.minLevel>0?e.minLevel:Math.min(...t.map(o=>o.level)),r=t.filter(o=>o.level===n).filter(o=>o.heading.length>0).filter(o=>C(o.heading,e)).map(o=>I(o.heading,e));return r.length>0?r.join(" | "):null}});var{Plugin:J,MarkdownRenderer:T,MarkdownRenderChild:K}=d(),{getOptionsDocs:V,parseOptionsFromSourceText:Z}=y(),{getMarkdownFromHeadings:G}=F(),h="table-of-contents",Q="toc",m=class extends J{async onload(){let e=(n,r,o)=>{o.addChild(new f(this.app,r,o.sourcePath,n))};this.registerMarkdownCodeBlockProcessor(h,e),this.registerMarkdownCodeBlockProcessor(Q,e),this.addCommand({id:"insert-automatic-table-of-contents",name:"Insert table of contents",editorCallback:U}),this.addCommand({id:"insert-automatic-table-of-contents-docs",name:"Insert table of contents (with available options)",editorCallback:X})}};function U(t){let e=`\`\`\`${h}
\`\`\``;t.replaceRange(e,t.getCursor())}function X(t){let e=[`\`\`\`${h}
${V()}
\`\`\``];t.replaceRange(e.join(`
`),t.getCursor())}var f=class extends K{constructor(e,n,r,o){super(n),this.app=e,this.element=n,this.sourcePath=r,this.sourceText=o}onload(){this.render(),this.registerEvent(this.app.metadataCache.on("changed",this.onMetadataChange.bind(this)))}onMetadataChange(){this.render()}render(){try{let e=Z(this.sourceText);e.debugInConsole&&u("Options",e);let n=this.app.metadataCache.getCache(this.sourcePath),r=n?.headings?n.headings:[];e.debugInConsole&&u("Headings",r);let o=G(r,e);e.debugInConsole&&u("Markdown",o),this.element.empty(),T.renderMarkdown(o,this.element,this.sourcePath,this)}catch(e){let n=`_\u{1F4A5} Could not render table of contents (${e.message})_`;T.renderMarkdown(n,this.element,this.sourcePath,this)}}};function u(t,e){console.log(`%cAutomatic Table Of Contents %c${t}:
`,"color: orange; font-weight: bold","font-weight: bold",e)}module.exports=m;

/* nosourcemap */