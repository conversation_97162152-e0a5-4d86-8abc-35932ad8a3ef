---
id: sensitive-data-audit-report
title: "Sensitive Data Audit Report - Obsidian Vault Security Assessment"
description: "Comprehensive security audit identifying all sensitive information in the Obsidian vault that requires redaction before sharing or publishing"
creation_date: 2025-09-18
modification_date: 2025-09-18
type: security-audit
area: security
status: active
priority_score: 95
author: "Augment Agent"
audit_scope: "Complete vault analysis"
risk_assessment: "High - Multiple critical exposures identified"
tags:
  - security
  - audit
  - sensitive-data
  - redaction
  - privacy
  - compliance
  - vault-security
related:
  depends-on: []
  blocks: ["Public sharing", "Documentation publishing"]
  area-overlap: ["Privacy & Security", "Documentation"]
  references: ["AGENTS.md", "Vault structure"]
  supports: ["Data protection", "Compliance"]
  relates-to: ["Security policies", "Data governance"]
---

# Sensitive Data Audit Report - Obsidian Vault Security Assessment

---

## Executive Summary

This comprehensive security audit has identified **CRITICAL SECURITY EXPOSURES** in the Obsidian vault that must be addressed before any sharing or publishing. The vault contains highly sensitive information including SSH private keys, AWS credentials, network infrastructure details, business registration numbers, and personal system information.

**IMMEDIATE ACTION REQUIRED**: This vault contains sensitive data that could compromise security if exposed publicly.

## Risk Assessment Overview

| Risk Level | Count | Categories |
|------------|-------|------------|
| **HIGH** | 8 | SSH Keys, AWS Credentials, Network IDs, Business Numbers |
| **MEDIUM** | 12 | IP Addresses, System Paths, Infrastructure Details |
| **LOW** | 6 | Personal References, File Paths |

---

## Critical Findings (HIGH RISK)

### 1. SSH Private Key Exposure
**File**: `brain-preservatives/obsidian-notes-asus`
**Risk Level**: 🔴 **CRITICAL**
**Location**: Lines 1-8

```
-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABC7k+9G2Y
[TRUNCATED PRIVATE KEY CONTENT]
-----END OPENSSH PRIVATE KEY-----
```

**Impact**: Complete system compromise if exposed
**Recommendation**: 
- Immediately remove this file from the vault
- Regenerate SSH key pair
- Update all systems using this key

### 2. SSH Public Key with Personal Information
**File**: `brain-preservatives/obsidian-notes-asus.pub`
**Risk Level**: 🔴 **HIGH**
**Location**: Line 1

```
ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIDRzBssvade/ckF9rOmKAXOU3JG0i5xsWA/0dIvL4tZd jordan@jordan-x550ld
```

**Sensitive Data**: 
- SSH public key fingerprint
- Personal username: `jordan`
- System hostname: `jordan-x550ld`

**Recommendation**: Remove or redact personal identifiers

### 3. AWS Account Credentials
**File**: `brain-preservatives/3-Resources/AWS/AWS CLI and account Details.md`
**Risk Level**: 🔴 **CRITICAL**
**Location**: Lines 73-85

**Exposed Information**:
- AWS Account ID: `************`
- AWS Access Key ID: `AKIAQLVQQ7GZRJOR7PGK`
- User ID: `************`

**Impact**: Full AWS account access and potential financial liability
**Recommendation**: 
- Immediately rotate AWS credentials
- Remove all AWS identifiers from documentation
- Implement proper secrets management

### 4. Git Repository SSH Configuration
**File**: `brain-preservatives/README.md`
**Risk Level**: 🟡 **MEDIUM**
**Location**: Lines 8-18

**Exposed Information**:
- Git repository: `git@ucanotes:fjord-an/notes-uca-obsidian.git`
- SSH config path: `C:\Users\<USER>\.ssh\notes-uca-obsidian`
- GitHub hostname configuration

**Recommendation**: Replace with generic examples

### 5. Business Registration Information
**File**: `brain-preservatives/4-Archive/ASIC Details for PaceySpace.md`
**Risk Level**: 🔴 **HIGH**
**Location**: Line 43

**Exposed Information**:
- ASIC Key: `1-***********`
- Business registration links with authentication tokens

**Impact**: Business identity exposure and potential fraud
**Recommendation**: Redact all business registration numbers and secure links

---

## Network Infrastructure Exposures (MEDIUM RISK)

### 6. ZeroTier Network Configuration
**File**: `brain-preservatives/Family/EC2 Bridge Server Quick Setup Guide.md`
**Risk Level**: 🟡 **MEDIUM**
**Location**: Lines 80-82

**Exposed Information**:
- ZeroTier Network ID: `8056c2e21ce5322d`
- Internal IP addresses: `*************`
- Network interface names

**Recommendation**: Replace with placeholder values

### 7. Internal Network Architecture
**File**: `brain-preservatives/Family/Network Architecture Overview.md`
**Risk Level**: 🟡 **MEDIUM**
**Location**: Multiple lines (49, 60, 143, 148, 159, etc.)

**Exposed Information**:
- ZeroTier subnet: `***********/24`
- Docker network: `**********/16`
- Specific device IPs: `************`, `*************`
- Service endpoints and domain mappings

**Recommendation**: Use generic network ranges in documentation

### 8. Service Domain Mappings
**File**: `brain-preservatives/Family/Network Architecture Overview.md`
**Risk Level**: 🟡 **MEDIUM**
**Location**: Lines 188-195

**Exposed Information**:
- Domain names: `get.spacey.my`, `prowl.spacey.my`, `watch.spacey.my`
- Internal service ports and configurations

**Recommendation**: Replace with example domains

---

## Personal Information Exposures (LOW-MEDIUM RISK)

### 9. Personal System Information
**File**: `brain-preservatives/archlinuxNoobslayerFixPrompt230425.txt`
**Risk Level**: 🟡 **MEDIUM**
**Location**: Line 1

**Exposed Information**:
- Personal username: `Jordan`
- System configuration details
- Personal troubleshooting context

**Recommendation**: Remove personal identifiers

### 10. Financial Transaction Data
**File**: `brain-preservatives/2-Areas/Finances/Cryptocurrency-Trading/Crypto Holdings - Transactions.md`
**Risk Level**: 🟡 **MEDIUM**
**Location**: Lines 51-57

**Exposed Information**:
- Cryptocurrency transaction amounts
- Trading dates and rates
- Financial activity patterns

**Recommendation**: Remove or anonymize financial data

---

## System Path Exposures (LOW RISK)

### 11. Windows System Paths
**File**: `brain-preservatives/README.md`
**Risk Level**: 🟢 **LOW**
**Location**: Line 17

**Exposed Information**:
- Windows user path: `C:\Users\<USER>\.ssh\`

**Recommendation**: Replace with generic path examples

### 12. Personal Directory References
**File**: `brain-preservatives/Users/<USER>/Documents/notes`
**Risk Level**: 🟢 **LOW**

**Exposed Information**:
- Personal directory structure
- Username references

**Recommendation**: Remove or anonymize directory names

---

## Redaction Recommendations by Category

### 🔴 Critical Actions (Immediate)
1. **Remove SSH private key file entirely**
2. **Rotate AWS credentials immediately**
3. **Redact business registration numbers**
4. **Remove authentication tokens and secure links**

### 🟡 Medium Priority Actions
1. **Replace network IDs with placeholders**
   - `8056c2e21ce5322d` → `[ZEROTIER_NETWORK_ID]`
   - `***********/24` → `10.0.0.0/24`
   - `**********/16` → `**********/16`

2. **Replace domain names with examples**
   - `spacey.my` → `example.com`
   - `get.spacey.my` → `service.example.com`

3. **Anonymize IP addresses**
   - `*************` → `*************`
   - `*************` → `*********`

### 🟢 Low Priority Actions
1. **Replace personal usernames**
   - `jordan` → `user`
   - `Jordan` → `User`

2. **Generalize system paths**
   - `C:\Users\<USER>\.ssh\` → `~/.ssh/`
   - `/home/<USER>/` → `/home/<USER>/`

---

## Automated Redaction Script Template

```bash
#!/bin/bash
# Sensitive Data Redaction Script

# AWS Credentials
sed -i 's/************/[AWS_ACCOUNT_ID]/g' **/*.md
sed -i 's/AKIAQLVQQ7GZRJOR7PGK/[AWS_ACCESS_KEY]/g' **/*.md

# Network IDs
sed -i 's/8056c2e21ce5322d/[ZEROTIER_NETWORK_ID]/g' **/*.md
sed -i 's/10\.147\.20\./10.0.0./g' **/*.md
sed -i 's/172\.27\.244\./192.168.1./g' **/*.md

# Business Information
sed -i 's/1-***********/[ASIC_KEY]/g' **/*.md

# Personal Information
sed -i 's/jordan@jordan-x550ld/user@hostname/g' **/*.md
sed -i 's/jordan/user/g' **/*.md

# Domains
sed -i 's/spacey\.my/example.com/g' **/*.md
```

---

## Files Requiring Complete Review

### High Priority Files
1. `brain-preservatives/obsidian-notes-asus` - **DELETE IMMEDIATELY**
2. `brain-preservatives/obsidian-notes-asus.pub` - **REDACT**
3. `brain-preservatives/3-Resources/AWS/AWS CLI and account Details.md` - **REDACT**
4. `brain-preservatives/4-Archive/ASIC Details for PaceySpace.md` - **REDACT**

### Medium Priority Files
1. `brain-preservatives/Family/EC2 Bridge Server Quick Setup Guide.md`
2. `brain-preservatives/Family/Network Architecture Overview.md`
3. `brain-preservatives/Family/ZeroTier-Security-Setup.md`
4. `brain-preservatives/2-Areas/Finances/Cryptocurrency-Trading/Crypto Holdings - Transactions.md`

### Low Priority Files
1. `brain-preservatives/README.md`
2. `brain-preservatives/archlinuxNoobslayerFixPrompt230425.txt`
3. All files in `brain-preservatives/Users/<USER>/`

---

## Compliance Considerations

### Data Protection Requirements
- **Personal Data**: Remove all personal identifiers per privacy regulations
- **Financial Data**: Anonymize or remove financial transaction details
- **Business Data**: Protect business registration and identification numbers
- **Technical Data**: Sanitize network configurations and credentials

### Security Best Practices
- Implement proper secrets management for credentials
- Use environment variables for sensitive configuration
- Establish data classification policies
- Regular security audits and reviews

---

## Next Steps

1. **Immediate**: Remove SSH private key and rotate AWS credentials
2. **Short-term**: Implement automated redaction script
3. **Medium-term**: Establish data governance policies
4. **Long-term**: Implement proper secrets management system

---

### Tags
#security #audit #sensitive-data #redaction #privacy #compliance #vault-security #aws #ssh #network #business #personal #financial #infrastructure

---
