


You are working on the YendorCats project - a production ASP.NET Core 8.0 web application for a specialized Maine Coon cat breeder in Queensland, Australia (one of the rarest breeders in the region). This is a nearly complete project with specific technical requirements and business context.  
  
**Project Location**: `~/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src`  
  
**Business Context & Core Features**:  
- **Cat Family Tree System**: Interactive genealogy display showing kitten ancestors to attract customers  
- **Individual Cat Profiles**: Each cat has a dedicated profile page with ancestral lineage links  
- **Gallery System Architecture**:  
- **Queens Gallery**: Female Maine Coon breeding cats  
- **Studs Gallery**: Male Maine Coon breeding cats  
- **Three Carousel Galleries**: Horizontally scrolling image displays  
- **Mixed Community Gallery**: User-submitted photos at the end  
- **Dynamic Content Loading**: All galleries load content dynamically from storage  
  
**Technical Stack & Architecture**:  
- **Backend**: ASP.NET Core 8.0 (production-ready)  
- **Storage**: Backblaze B2 S3-compatible API (primary data source)  
- **Data Strategy**: S3 object metadata serves as the primary database for image data and queries  
- **Frontend**: Dynamic gallery rendering with carousel functionality and lightbox modals  
- **Admin System**: Backend uploader with form fields for metadata management and gallery editing  
  
**Current Development Status**:  
- Project is ~95% complete and production-ready  
- **Active Issue**: Image metadata not displaying correctly in carousel galleries and lightbox modals  
- **Specific Problem**: S3 object metadata retrieval/display in frontend carousel and lightbox components  
  
**Key Technical Nuances**:  
- S3 metadata is the primary data source (not traditional database)  
- Carousel galleries require horizontal scrolling functionality  
- Lightbox modals must display rich metadata alongside images  
- Admin interface allows real-time metadata editing and gallery management  
- Brisbane, Australia hosting considerations for local client performance  
  
**Development Context**: Focus troubleshooting efforts on the metadata display pipeline from S3 storage through to frontend carousel and lightbox rendering components.