# Migration Report

- Root: `/Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives`
- Processed: 580
- Changed: 580
- Frontmatter-only: 0
- Structure-only: 0
- Both: 580
- Backup dir: `.backups/20250921-054357`

## Changed Files
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/WARP.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Search.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2025-04-25.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Vault Restructuring Complete - Summary.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Untitled.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/User Guidelines and Rules for all AI prompts (Cursor, Augment Code).md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Welcome.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Wise Fintech Bank Features and Capabilities.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Home.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/README.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Tasks.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/"0-Daily Notes".md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.AGENTS-TEMPLATE.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/4-Archive.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/ai-model-comparison-for-vault.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Test.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/vault-data-quality-improvement.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Tasks Plugin - Review and check your Statuses 2025-06-08 06-57-05.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.1-Clippings/How to get Notion-AI-like Autocomplete with LLMs in Obsidian, offline.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.1-Clippings/5 tools to improve your terminal productivity with LLMs.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.1-Clippings/Taio a complete environment which is Clipboard, powerful Markdown editor and Actions.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.1-Clippings/Embedding data in Obsidian - Reuse content and make your vault a WIKI.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.1-Clippings/mxbai-embed-large335m.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.1-Clippings/DaisyDisk, the most popular disk space analyzer.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/Omnivore.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/St. Clair Software Checkout.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/Plan your journey  Translink.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/What is a balance alert or balance notification, and how can I set them up?.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/How to get Notion-AI-like Autocomplete with LLMs in Obsidian, offline.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/Contact the ABR.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/5 tools to improve your terminal productivity with LLMs.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/Netlify MCP Server.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/Open WebUI Network Access Information.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/iCloud Mail server settings for other email client apps – Apple Support (AU).md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/MailMate.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/What is a balance alert or balance notification, and how can I set them up? 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/chrishayukmcp-cli.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/Taio a complete environment which is Clipboard, powerful Markdown editor and Actions.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/Case – Xero Central.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/Omnivore is Dead Where to Go Next.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/Embedding data in Obsidian - Reuse content and make your vault a WIKI.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/dengcaoQwen3-Embedding-4B.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/Dashboards for developers.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/mxbai-embed-large335m.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/Plan your journey  Translink 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/DaisyDisk, the most popular disk space analyzer.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Clippings/orion - Proxmox Virtual Environment.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/inbox/s3 metadata entry scripts batch editing.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Church Administration.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/PaceySpace About Us Message for advertisement and External networking.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Areas TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/Optimising requests and caching with nginx.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/Website Domains.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/Docker.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/Nginx Administration benefits.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/Production Deployment TODO.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Managing Docker Folder Permissions.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Troubleshooting ZeroTier and Docker Networking.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Simplified ZeroTier Network Setup.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Media Server.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/EC2 Bridge Server Quick Setup Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/problematic commits of accidentally deleted files.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Minimal Gateway For ZeroTier Networks.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Bridge network to access zero tier services from internet.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Secure ZeroTier Network Separation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Roster Reiteration.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/ZeroTier Bridge Connectivity Troubleshooting.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/ZeroTier Flow Rules Placement.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Table of Contents ~ The Paceys-media Streaming Server.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Media Streaming Server.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Security-Documentation-Index.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Network Architecture Overview.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Chores Roster.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/ModSecurity-WAF-Setup.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/ZeroTier Network Documentation Links.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Bad Media Influences.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/EC2-ZeroTier-Bridge-Security.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/ZeroTier-Security-Setup.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Links.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/EC2 Bridge Server Maintenance.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/split tunelling with mullvad.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Chores Roster_OLD.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Secure ZeroTier Network Separation]],[[Bridge network to access zero tier services from internet.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Docker Configuration Files.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Setup and Bug Challenge 10 april - modsecurity setup, location and routes.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Family/Improving Communication Session 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Smart Task Dashboard V2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Task Automation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Enhanced Task System Implementation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Task Linking Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Tasks TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Tasks Dashboard.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Daily Task Sync.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/UNI/UQ - Uni.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/UNI/USQ - Important dates and info - TPP.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/UNI/USC - Important dates and info - TPP.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/4-Archive/ASIC Details for PaceySpace.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/4-Archive/Ghost Browser Terms and Conditions.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Resource Index.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/YendorCats Deployment Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Para Notes Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/YendorCats S3 Metadata Implementation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/popserver or laptop server pre april (before wipe and installation of enhance control panel).md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/YendorCats Cloudflare Integration.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Dataview Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/YendorCats File Uploader Service.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/System-Configuration.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Google Keep Notes.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/README.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Hall Hire Procedures.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/References.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Guides TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Social Media Automation tools.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Recommended Plugins.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Website Design Best Practices.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Development Resources.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Keyboard shortcuts and gestures in Safari on Mac.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Devices and Contracts.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Tags MOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Vault Organization Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Vault Organization System Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Resources TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Vault Migration Checklist.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/People MOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Scripts/Vault Maintenance Scripts.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Tools&Ogranisation/AI tools for note and task automations.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Security Planning.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/2025-06-04.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Templates.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/2025-06-25.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Tfn tax file number.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Untitled.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/2025-08-19.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Untitled 1 5.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Bugfixes.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Untitled 3.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Untitled 1 4.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Untitled 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Untitled 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Untitled 1 3.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Untitled 1 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/test bm.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Untitled 4.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Embed files 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Untitled Kanban.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Computer Education.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/2025-06-26 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/2025-07-18.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Capstone test notes.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/2025-06-26.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/2025-06-02.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/2025-06-03.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-24.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/27th April 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-13.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-10.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/12-04-2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-03.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-04-14.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-04-20.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/Daily Notes TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-19.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-14.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-17.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-23.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/23rd April 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/21st April 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-07.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-04.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-09.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-18.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-15.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-16.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/06-04-2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-22.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-21.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-04-25.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-06.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-05.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/Daily Note Folder. Must be cleaned Daily.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-25.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-26.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-12.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-11.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-04-15.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-02.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/10-04-2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/3rd May 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-20.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-19.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-14.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-17.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-07.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-04.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-09.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-30.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/07-04-2025 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-10.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-29.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/11th May 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-24.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/31st May 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-03.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-12.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/26th April 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-11.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-02.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/22nd April 2026.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/30th April 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/4th May 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-18.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-15.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-16.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-08.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-31.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-08 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/29th April 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-11.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-05-06.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-02-Kanban.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/07-04-2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-26.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-28.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-04-18.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-01.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-02.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/22nd April 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/Crashing issue sound driver conflicts.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/Nimble Commander Keybinds.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-22.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-21.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-15.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-16.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-18.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-08.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-06.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-05.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/5th May 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-23.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-19.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-09.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/6th May 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-04.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/13-04-2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/24th April 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-09-10.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-27.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-29.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-18.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-21.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/25th April 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/Virus File - Agent AI Spawn.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-31.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/05-04-2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-06.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-05.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-26.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-28.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/14-04-2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-12.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/8th May 2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-13 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-04-16.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-01.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-04-22.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-24.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-27.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-13.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/11-04-2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-03.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-17.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-07-19.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-06-23.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-20.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-08-04.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Website Redesign Meeting 2023-06-01.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Projects TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Website Redesign.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Lawson Fridge Repairs.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Document-Store/Document Storage Applications Research.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Note-Flows/Notion quick capture setup.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/VPC-Configurations/PaceySpace Cluster - Galaxy Architecture.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/VPC-Configurations/Proxmox Installations.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/VPC-Configurations/={0Planning}= VPC Hardware.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/VPC-Configurations/Proxmox Pre-Authorisation Checklist.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/VPC-Configurations/CPU.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/Vault-Multi-Client-Setup 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/HashiCorp-Vault-Setup-Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/Vault-Security-Configuration 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/VaultSharp-Integration-Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/YendorCats-Vault-Integration-Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/Enhance-Control-Panel-Vault-Deployment.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/Vault-Multi-Client-Setup.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/Vault-Backup-Recovery 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/Vault-Secret-Rotation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/VaultSharp-Integration-Guide 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/Vault-Security-Configuration.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/Vault-Secret-Rotation 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/Vault-Backup-Recovery.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/YendorCats-Secrets-Management-Overview.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Secrets-Management/Secure-Vault-Backup-Procedures.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Self-Hosted-AI/VibeTunnel Setup for Zerotier One.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/Paceyspace cluster server-architecture.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/PaceySpace Web Host 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/Playing Audio on thinlinc device locally.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/Prompting the Cluster installation suite.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/PaceySpace Web Host.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/Checkmate 2.3.1 released.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/Self Hosted Installation Roadmap.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/Checkmate 2.3.1 released 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/Self Hosted Installation Roadmap 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/vps-home-connectivity 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/cloudflare-tunnels-guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/vps-home-connectivity.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/Paceyspace cluster server-architecture 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/cloudflare-tunnels-guide 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/CRUCA Website/Procedure to renew ssl certificates with certbot - Auto or Manually.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/CRUCA Website/CRUCA Website Updates 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/CRUCA Website/CRUCA Website Updates.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/deployments/<media streaming and indexing cluster> Network Architecture Overview.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/deployments/Enhanced CP Deployment Architecture.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/deployments/Self-Hosted AI Options.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/deployments/Docker volume Backups in terminal from WARP.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Table of Contents - YendorCats.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Performance improvements for metadata and consistency.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Deployment with github actions.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/YendorCats Deployment Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Admin authentication user for s3 object endpoints and photo uploader.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Roadmap-YendorCats_Dev.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/1-Correspondence 19-05-2025.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Email to cancel williams services.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/YendorCats S3 Metadata Implementation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/yendorcats.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Testing.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/YendorCats File Uploader Service.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/YendorCats Testing Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Deployment with github actions pt. 2 - Manual Deployments.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Yendorcats Project Finance.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/YendorCats Project Documentation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/S3 Metadata Management scripts and editor.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/2-Services Agreement and Pricing Consultation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/YendorCats_Service_Level_Agreement.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Untitled Kanban.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/YendorCats Testing Infrastructure Analysis.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Project Development Journal and Notes.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/YendorCats-Secrets-Management-Overview.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/New  admin or account control features 2025-07-18.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/S3 Connection and HashiCorp Vault Secrets Manager.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Bug investigation Report - S3 image load failing.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Yendor Queensland Business and Registration details;.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/TaskList-Correspondance/About YendorCats - _ABOUT Section_.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/TaskList-Correspondance/About YendorCats.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/TaskList-Correspondance/All_Requested_Changes.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/TaskList-Correspondance/_KanBan-yendorcats-content.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/TaskList-Correspondance/Yendor Cat Image Metadata tabulation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/TaskList-Correspondance/README_S3_METADATA_UPDATE.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/TaskList-Correspondance/Introductory Content.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/TaskList-Correspondance/S3_Metadata_Configuration.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/TaskList-Correspondance/Backblaze_B2_Metadata_Configuration.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/docker/Environment Variables Reference.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/docker/Docker Troubleshooting.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/docker/README.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/docker/Docker Commands Cheatsheet.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/docker/Docker Scripts Reference.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/docker/Docker Deployment Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/docker/Docker Quick Reference.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/deployment-cicd/Remote Deployment Script Usage.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/legacy-site/yendorcats.com legacy website s3 bucket deletion recovery.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/bugfixes/-CRITICAL PROGRESS HALT- Build Errors from performance caching spriiint.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/bugfixes/Initial Unit Testing Plan.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/bugfixes/bug1.  runtime auth errors sprint Resources.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/Meetings/30th August Meeting - Pre-release meeting - Phone.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/billing/Untitled.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/TaskList-Correspondance/venv/lib/python3.13/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/idna/LICENSE.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/PaceySpace-LLM-Gateway/tensorzerotensorzero TensorZero is an open-source stack for industrial-grade LLM applications. It unifies an LLM gateway, observability, optimisation, evaluation, and experimentation..md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/HashiCorp-Vault/vault-quick-setup-instructions 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/HashiCorp-Vault/HashiCorp Overview and  Ecxample configurations.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/HashiCorp-Vault/ARCHIVE-README.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/HashiCorp-Vault/ARCHIVE-vault-setup-instructions.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/HashiCorp-Vault/vault-advanced-setup-instructions.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/MCP-Server/MCP Server Project Planning Phase.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/MCP-Server/MCP Server Project.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/MCP-Server/Untitled Kanban 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/MCP-Server/MCP-Server Start Project.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/MCP-Server/Checkmarx2ms Too many secrets (2MS) helps people protect their secrets on any file or on systems like CMS, chats and git.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/MCP-Server/Local Qdrant Vector database for Embedding.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/MCP-Server/Untitled Kanban.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/MCP-Server/MCP Server Configurations - Implementations and Usage.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/MCP-Server/Untitled Kanban 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/Media-Server/Thinlinc.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/Media-Server/Media Streaming Server Setup.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/PaceySpace-Cluster/Network-Connectivity/92-120 alma road dakabin.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-04-16/Untitled.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-04-16/2025-04-16 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-04-16/2025-04-16 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0-Daily Notes/2025-04-16/2025-04-16.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Optus/optus_account-recovery.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/.trash/Optus 2/optus_account-recovery.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/Untitled.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/Untitled 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/Emailing.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Filesystem extensions (aws)-1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Senior Developer Coding Rules.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Finding a Specific port using SS on linux.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Cursor rules from a senior developer.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Email Server Settings for Email Clients 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Arc.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Rsync Guide for network file syncing.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/i3wm Keybindings Cheetsheet.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Bluetooth Remote  Control from SSH on cli.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/1 Git Command Cheatsheet.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Rsync.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Email Server Settings for Email Clients.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/archlinuxNoobslayerFixPrompt.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Windows Shortcuts.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/2 Git command cheatsheet V2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/Bash Commands Cheatsheet.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Developer-Tools-Cheatsheets/basic gitignore template - dotnet.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Dotfiles/SSH-Keys-Documentation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Dotfiles/Dotfiles-Management.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Dotfiles/Dotfiles-Backup-Script.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Dotfiles/Dotfiles-Summary.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Dotfiles/Dotfiles-Components.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Dotfiles/Dotfiles-TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Dotfiles/Fjord-an's Kitty terminal configuration.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Dotfiles/Dotfiles-Installation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Flaresolverr and Prowlarr.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/LVM Disk Commands Linux 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Flaresolverr and Prowlarr Setup.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Managing Docker Folder Permissions.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/VPN.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Unix Filesystem guides.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Untitled.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Computer Education 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Second Brain Notetaking.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/LVM Disk Commands Linux.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Ranger File Manager Guide (neovim).md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Computer Education.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Filesystem and disk commands in linux guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Docker Configuration Files.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/Note Taking with Obsidian Guides.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Network/Minimal Gateway For ZeroTier Networks.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Network/Secure ZeroTier Network Separation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Network/Ubuntu Server 24.04 CLI Cheatsheet.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Network/ModSecurity-WAF-Setup.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Network/hosting from laptp.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Network/Network and IP addresses List.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Network/EC2 Bridge Server Maintenance.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Network/Network TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Developer Profile.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/MCP Setup Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Tech Stack Reference.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Code Generation Prompts.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Untitled.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/3System Prompt Templates.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/MCP Server Setup and Utilization.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Prompt Engineering TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Chain of Thought Prompting.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Prompt Rules for Technical Markdown Documentation  Documents.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/AI Prompt Guidelines.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Terminal Productivity Agent - Systems Administration expert.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Detailed Plan for Setting Up a Personalized MCP Server.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Detailed Plan for Setting Up a Personalized MCP Server - Python with MCP code tools.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Prompt Engineering Fundamentals.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/AI Assistant Memory Management Guidelines.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Saved Prompts.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Few-Shot Learning Examples.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/MCP Customization.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/AI Prompt Engineering.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Prompt Engineering/Qdrant MCP Startup and User Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Family/Media Influences on Children 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Family/Family TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Family/Family TOC 2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Family/Mental Health Research.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Family/Media Influences on Children.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/University/University Preparation Programs.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/University/University TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Mac/Mac 101_ the best way to clean your MacBook's screen [Video].md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Contracts-Services/Telstra One.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/AWS/AWS CLI and account Details.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/proxmox-docs/01-proxmox-overview.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/proxmox-docs/ZFS-Installation-Settings.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/proxmox-docs/05-quick-reference.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/proxmox-docs/00-IMPORTANT-README.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/proxmox-docs/02-installation-guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/proxmox-docs/INDEX.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/proxmox-docs/04-daily-operations.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/proxmox-docs/03-first-vm-guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/HashiCorp-Vault-Setup-Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/VaultSharp-Integration-Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/YendorCats-Vault-Integration-Guide.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/Enhance-Control-Panel-Vault-Deployment.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/Vault-Multi-Client-Setup.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/Vault-Secret-Rotation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/Enhance-Control-Panel-Vault-Deployment-2.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/Enhance-Control-Panel-Vault-Deployment-1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/Vault-Security-Configuration.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/Vault-Backup-Recovery.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/YendorCats-Secrets-Management-Overview.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/Secure-Vault-Backup-Procedures.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/Software Guides/HashiCorp-Vault/HashiCorp-Vault-How-It-Works.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/test/05-04-2025 Resource/05-04-2025 Resource.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/People/John Smith.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/People/People TOC.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Australian-Business-Registration/ABN Details and Historic Data for PaceySpace (Previously - Jordan Phillip Pacey).md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Australian-Business-Registration/When should i Register for PAYG or Tax Creditations?.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Finance/Invoices.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Legal/Contract.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Legal/Service Level Agreement.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Legal/Service Agreements & Contracts/YendorCats_Financial_Tracking.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Legal/Service Agreements & Contracts/YendorCats_Service_Agreement.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Legal/Service Agreements & Contracts/YendorCats_Financial_Dashboard.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Legal/Service Agreements & Contracts/YendorCats_Service_Level_Agreement.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Legal/Service Agreements & Contracts/YendorCats_Client_Presentation.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Legal/Service Agreements & Contracts/YendorCats_Project_Quote.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/3-Resources/PaceySpace Wiki/2-Areas/Finance/Yendor/Untitled.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Completed/Completed Tasks Index.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Active/2025-07-14-001-Cloudflare-Domain-Removal.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Active/2025-07-14-003-Church-Hall-Booking-System.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Active/2025-08-07-004-Enhance-Control-Panel-Backup-Fix.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Active/2025-07-14-002-YendorCats-Documentation-Update.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/0.2-Tasks/Active/Active Tasks Index.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/zpersonal/Projects/Self hosted ai project.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/yendor/AI Prompt for cursor - yendorcats.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/yendor/08-04-25 call to margeret.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/cruca/Information to add.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/cruca/Cursor for cruca website caboolture uniting church.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/cruca/Augment for cruca website caboolture uniting church 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/cruca/cline_task_apr-16-2025_12-36-54-am.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/cruca/README.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/cruca/secrets-management.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/NGINX LOGS/Nginx.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/Website/NGINX LOGS/NGINX Logs.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Website-Maintenance/Website Maintenance.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Website-Maintenance/Website Maintenance Overview.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Task Management/Neovim Notes Information Swashbuckler.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Task Management/Tasks Plugin Review.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Task Management/Tasks.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Task Management/Refining Task Management.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Privacy & Security/Unauthorized Access Security Audit.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Privacy & Security/Untitled.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Privacy & Security/AeroAdmin Supervisory Software - Privacy.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Privacy & Security/Privacy Tools.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/AI-Agents/Qdrant info.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/AI-Agents/SSH key recommendations.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/AI-Agents/VibeTunnel Terminals CLI Agents in the Browser - Access from anywhere.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/AI-Agents/CLI Agents Usage.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Education/University Admissions.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Family/full-conversation-claude-the-counciler-family-counciling.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Family/Relationships.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Family/Chores Roster.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Family/Improving Communication.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Network-Administration/Unlocking Luks Disk Encryption using SSH with DropBear in boot sequence.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Network-Administration/Sirius Server Administration Resources and Architectural Report.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Network-Administration/Secrets Redaction Warp.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Network-Administration/Network Administration Overview.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/Finances.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/new note test.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/Untitled.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/Optus.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/Untitled 1.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/Budget-Expenses&Income.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/Budget-Expenses-Income(original).md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/Taxes Notea.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Backups/Rclone Cheatsheet.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Backups/Quick Rclone Guide to efficient backups.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Accomodation/Rooms for Rent - to enquire.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/PaceySpace-Finance/Finance end of August.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/2025/6 june.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/2025/7 july.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/2025/EndAug-Budget.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/2025/May.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/Cryptocurrency-Trading/Crypto Holdings - Transactions.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/Optus/Untitled.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/PaceySpace-Finance/banking/Case – Xero Central.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Finances/PaceySpace-Finance/banking/August Payments.md :: both
- /Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/2-Areas/Task Management/Todoist/CRUCA Todoist Tasks to 21-04-2025.md :: both
