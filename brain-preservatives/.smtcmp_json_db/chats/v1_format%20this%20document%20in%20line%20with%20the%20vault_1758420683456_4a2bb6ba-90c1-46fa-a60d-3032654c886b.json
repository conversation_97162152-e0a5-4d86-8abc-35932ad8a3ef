{"id": "4a2bb6ba-90c1-46fa-a60d-3032654c886b", "title": "format this document in line with the vault", "messages": [{"role": "user", "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "format this document in line with the vault", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "\n\nformat this document in line with the vault\n\n"}], "id": "902729da-80f1-4e70-b027-c1310d6abb54", "mentionables": [{"type": "current-file", "file": "2-Areas/Network-Administration/Embed Code for PaceySpace.com docs.md"}]}, {"role": "assistant", "content": "To format the document in line with the vault, I will make the following changes:\n\n1.  **Add YAML frontmatter**: Include `title` and `tags` for better organization.\n2.  **Use proper blockquotes for domains**: This visually separates the domains from the main content.\n3.  **Create a code block for the embed code**: This is the standard way to display code snippets in Markdown.\n\nHere's the updated content for `2-Areas/Network-Administration/Embed Code for PaceySpace.com docs.md`:\n\n<smtcmp_block filename=\"2-Areas/Network-Administration/Embed Code for PaceySpace.com docs.md\" language=\"markdown\">\n---\ntitle: Embed Code for PaceySpace.com Docs\ntags:\n  - network-administration\n  - embed\n---\n\nOnly these domains can embed this artifact. Separate with commas.\n\n> www.paceyspace.com\n\n### Embed Code\n\n```html\n<iframe src=\"https://claude.site/public/artifacts/a3c95ac9-078b-4305-9e72-a61aa0c19216/embed\" title=\"Claude Artifact\" width=\"100%\" height=\"600\" frameborder=\"0\" allow=\"clipboard-write\" allowfullscreen></iframe>\n```\n</smtcmp_block>", "id": "170198a5-c202-4775-aee6-ce7e65e9a74b", "metadata": {"model": {"providerType": "openrouter", "providerId": "openrouter", "id": "openrouter-g2.5flash", "model": "google/gemini-2.5-flash", "promptLevel": 1, "enable": true}, "usage": {"prompt_tokens": 641, "completion_tokens": 301, "total_tokens": 942, "cost": 0.0009448, "is_byok": false, "prompt_tokens_details": {"cached_tokens": 0, "audio_tokens": 0}, "cost_details": {"upstream_inference_cost": null, "upstream_inference_prompt_cost": 0.0001923, "upstream_inference_completions_cost": 0.0007525}, "completion_tokens_details": {"reasoning_tokens": 0, "image_tokens": 0}}}}, {"role": "user", "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "format this document as a forum posting document with indents and comment blocks", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "\n\nformat this document as a forum posting document with indents and comment blocks\n\n"}], "id": "9c390e29-844a-404f-8af7-62fda6e14193", "mentionables": [{"type": "current-file", "file": "2-Areas/Network-Administration/Aussie Broadband Enterprise -  Choosing an ISP.md"}]}, {"role": "assistant", "content": "To format this document as a forum posting, I will apply the following changes:\n\n1.  **Remove YAML frontmatter:** This is not typical for forum posts.\n2.  **Explicitly mark each user's post as a comment block:** This clearly delineates individual contributions.\n3.  **Indent replies to mimic forum conversational flow:** This visually represents nested conversations.\n4.  **Simplify author links:** Remove the detailed post counts and \"Whirlpool Forums Addict\" etc. for a cleaner look.\n5.  **Remove external reference links (whrl.pl links):** These are usually internal forum features and not part of the content itself.\n6.  **Remove `last updated` line:** Not relevant for a formatted post.\n7.  **Keep the `Archive View` and `Return to standard view` links:** These are part of the original forum context.\n8.  **Convert general text under authors to standard text, not comments:** This applies to the initial descriptions of contract options and plan comparisons, as they are not direct quotes from other users.\n\nHere's the reformatted document reflecting these changes:\n\n<smtcmp_block filename=\"Clippings/Aussie Broadband Enterprise - Choosing an ISP.md\" language=\"markdown\">\nArchive View [Return to standard view](https://forums.whirlpool.net.au/thread/30ym7769)\n\n> **User #796766 (Forum Regular - O.P.)**\n> Hey ABB,\n>\n> Any plans to offer addtional contract length options for the nbn™ Enterprise plans?\n>\n> **12 month + activation fee.\n> No lock-in contract + activation fee**\n>\n> ABB and Exetel seem to be the only ISPs that have Enterprise Ethernet pricing available via their websites (visible after filling out a contact details form)\n>\n> Exetel are much more expensive, but they do offer a few contract length options.\n> 36 month contract with no activation fee.\n> 12 or 24 month contract + activation fee.\n>\n> Also, please consider removing the requirement to provide contact details to view the the plan prices.\n\n> **User #69531 (Pirate Steve - Whirlpool Forums Addict)**\n> Have you had a look Future Broardband Enterprise plans?\n\n> **User #19447 (Moonman - Inactive Account)**\n> I see Best of all, eligible businesses can now access a $0 fibre install on a 36-month contract (subject to a site qualification check\\*) on their website too.\n\n> **User #57727 (iHome - Whirlpool Forums Addict)**\n>> **arrow-wp writes...**\n>> Also, please consider removing the requirement to provide contact details to view the the plan prices.\n> Yes, either that or don't use phone as the first point of contact with the customer....I was only keen to see what the pricing was like as consideration for moving an NFP off another plan and I had someone try to touch base with me twice in 2 days, when I clearly wasn't interested in discussing an offering.\n\n> **User #812634 (Melissa P. - Whirlpool Enthusiast)**\n> I believe the goal is to get a plan builder on the page. There's been a couple delays with this, but it is still in the works. It's a bit too difficult to just display prices as our enterprise plans are complex plans where customers can pick and choose the level of service they'd like on a multitude of items. Also, the fibre upgrade plans are contingent on zones for actual cost. For those currently seeking a price, it's best to contact our BDM's for an exact quote until the plan builder is in place.\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **Melissa P. writes...**\n>> I believe the goal is to get a plan builder on the page. There's been a couple delays with this, but it is still in the works.\n> I thought the plan builder was already on the page? I can see the pricing after filling out the contact details form.\n> [https://www.aussiebroadband.com.au/enterprise/nbn-enterprise-ethernet/](https://forums.whirlpool.net.au/go?https%3A%2F%2Fwww.aussiebroadband.com.au%2Fenterprise%2Fnbn-enterprise-ethernet%2F)\n>\n>> Also, the fibre upgrade plans are contingent on zones for actual cost.\n> Yes, that's understandable for those instances that involve a fibre build cost.\n>\n> However, it seems like the minimum contract length for the ABB TC-4 plans is also 36 months.\n>\n> **TC-4 plans**:\n> Personal 100/40: $99\n> No lock-in contract\n> No setup fee\n>\n> Business 100/40: $109\n> No lock-in contract\n> No setup fee\n> Includes a static IP address\n>\n> Enterprise 100/40: $129\n> Minimum 36 month contract\n> No setup fee\n> Includes a static IP address\n> Bronze Support Package (Best effort SAG, nbn™ 12hr eSLA 24/7, 24/7 enterprise support)\n>\n> For comparison, Future Broadband are offering their Enterprise NBN 100/40 plan (which has the same nbn™ 12hr eSLA 24/7, and 24/7 enterprise support) with the following contract length options:\n> [https://futurebroadband.com.au/ipline/](https://forums.whirlpool.net.au/go?https%3A%2F%2Ffuturebroadband.com.au%2Fipline%2F)\n> 1 month\n> 12 month\n> 24 month\n\n> **User #72704 (Grumps - Whirlpool Forums Addict)**\n> Why the hell would you want to go on a contract?\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **Grumps writes...**\n>> Why the hell would you want to go on a contract?\n> I don't want to go on a contract.\n>\n> ABB only offer the Enterprise nbn™ TC4 with a minimum 36 months contract.\n>\n> Future BB offer it with either no lock-in contract, or 12/24 months.\n\n> **User #72704 (Grumps - Whirlpool Forums Addict)**\n> This is a worry they use AAPT for both POI connectivity as well as state peering and IP transit.\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **Grumps writes...**\n>> they use AAPT for both POI connectivity as well as state peering and IP transit.\n> Yes, they're much smaller compared to ABB.\n>\n> FutureBB do have some good 'enterprise' nbn™ plans though.\n> - Transparent pricing.\n> - Decent contract lengths.\n\n> **User #796766 (Forum Regular - O.P.)**\n> On a sidenote, ABB reps can now remove the Wideband part from their tags, since that brand has been retired.\n\n> **User #53522 (Avocet Islander - Whirlpool Forums Addict)**\n> OP, there are quite a few RSPs out there so pick one that suits your needs.\n>\n> Most RPSs, including Aussie BB, run a pretty tight ship, so do not expect any of them to offer plans outside their current spread just to suit individuals. Reason, the only thing that changes RSPs plans are market forces so it will take a lot of people leaving to encourage your favourite RSP to change it's current offers.\n\n> **User #593834 (Andrew (N. Sydney) - Whirlpool Forums Addict)**\n>> **Grumps writes...**\n>> Why the hell would you want to go on a contract?\n> Stability, efficiency, risk reduction..... etc.\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **Grumps writes...**\n>> Why the hell would you want to go on a contract?\n> Given the fibre build to the premises is included on 36 months, why not?\n>\n> This is for enterprise ethernet, not a residential quality plan.\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **Zerophitus writes...**\n>> Most RPSs, including Aussie BB, run a pretty tight ship, so do not expect any of them to offer plans outside their current spread just to suit individuals.\n> Again, these are enterprise plans.\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **arrow-wp writes...**\n>> Future BB offer it with either no lock-in contract, or 12/24 months.\n> FutureBB do not appear to offer NBN EE on their website. It wouldn't surprise me if they did, but I could only see TC4, which is normal FTTP.\n\n> **User #245859 (crazyuser - Whirlpool Enthusiast)**\n> Most corporate/enterprise services (especially if you get an ISP to build Fibre to your premises are over 36 months (3yrs) or 60 months (5yrs).\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **The Bin Chicken writes...**\n>> FutureBB do not appear to offer NBN EE on their website. It wouldn't surprise me if they did, but I could only see TC4, which is normal FTTP.\n> Yes, you're right – they don't have an nbn™ EE plan on their website.\n> They do offer ['Enterprise' TC4'](https://forums.whirlpool.net.au/go?https%3A%2F%2Ffuturebroadband.com.au%2Fipline%2F) plans.\n>\n> **Future Broadband NBN100 TC4 – 12 Month IP-Line Enterprise**\n> $125p/m (Total cost $1,600 over 12 months incl $100 setup)\n>\n> Service Level Features:\n> NBN 12hour (24/7) Restoration Target (included)\n> - Upgrade to 4hour (BH) from $55pm\n> - Upgrade to 4hour (24/7) from $95pm\n>\n> Tech Features:\n> - Static IP Address\n> - BGP or Static Routing\n> - Bring your own IP Space\n> - Business-Grade IP Network\n> - Managed Hardware available\n>\n> **Aussie Broadband Enterprise TC4**\n> Enterprise 100/40: $129/month\n> Minimum 36 month contract\n> No setup fee\n> Includes a static IP address\n> Bronze Support Package (Best effort SAG, nbn™ 12hr eSLA 24/7, 24/7 enterprise support)\n>\n> Silver Support Package $100/month\n> 99% uptime SAG, nbnTM best eSLA (4 or 6 hr) 24/7, 24/7 enterprise support\n>\n> Gold Support Package $150/month\n> 99.95% uptime SAG, nbnTM best eSLA (4 or 6 hr) 24/7, 24/7 enterprise support\n>\n> Aussie Broadband Business NBN TC4 100/40 is $109/month, with no contract, and no setup fees. If a business wants an enhanced SLA then they will need to upgrade to the enterprise TC4 plan for $129/month. The price isn't an issue, but the minimum contract of 36 months is quite a jump.\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **The Bin Chicken writes...**\n>> This is for enterprise ethernet, not a residential quality plan.\n> ABB nbn™ TC4 is a 'residential quality plan.'\n>\n> Personal: 99/month, no contract\n> Business: 109/month, no contract (includes static IP)\n> Enterprise: $129/month, 36 month contract (static IP, nbn™ eSLA, 24/7 support)\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **arrow-wp writes...**\n>> Aussie Broadband Business NBN TC4 100/40 is $109/month, with no contract, and no setup fees. If a business wants an enhanced SLA then they will need to upgrade to the enterprise TC4 plan for $129/month. The price isn't an issue, but the minimum contract of 36 months is quite a jump.\n> Speak to them, see if you can get a different deal. They would be more flexible with their enterprise plans (non NBN EE) than the residential ones, especially if there are competing offers.\n>\n>> **arrow-wp writes...**\n>> nbn™ TC4 is a 'residential quality plan.'\n> What are you after? In your OP, you refer to NBN EE. Now you're talking about NBN TC4.\n>\n> You may be able to get a different deal when it comes to non-EE TC4 plans, but with NBN EE, RSPs will probably not budge too much because the contract period changes the build cost (100% discount on standard build for 36 months, 75% for 24 and no discount for 12 months or below) as well as ongoing costs:\n> [https://www.nbnco.com.au/content/dam/nbnco2/2019/documents/sell/wba/nbn-enterprise-ethernet-charges-subject-to-price-confirmation.pdf](https://forums.whirlpool.net.au/go?https%3A%2F%2Fwww.nbnco.com.au%2Fcontent%2Fdam%2Fnbnco2%2F2019%2Fdocuments%2Fsell%2Fwba%2Fnbn-enterprise-ethernet-charges-subject-to-price-confirmation.pdf)\n\n> **User #69479 (jimbeamau - Forum Regular)**\n> Almost all true business services come with a contract or setup fee for shorter period. Business don't swap and change providers so I really don't see a problem with the 3 year contact but it would be nice to have the option to bring it down for those short term or expiring leases.\n>\n> Business are use to contacts for services like phones, internet, etc. And because the service is usually of a higher quality than consumer problems rarely arise. Consumer services I am all for no contracts because these can change overnight in quality. Contention ratio's, standard vs premium, etc can all make a difference.\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **jimbeamau writes...**\n>> Almost all true business services come with a contract or setup fee for shorter period.\n> The setup fee for a shorter period is fine.\n>\n> I don't consider the TC4 plans to be a 'true business service' like nbn™ EE.\n>\n> I'd like the ability to bolt-on an enhanced SLA to the smaller business plans (for those customers that would like a better restoration timeframe from nbn co, but don't need 1:1 contention ratio)\n\n> **User #69479 (jimbeamau - Forum Regular)**\n> They are not a 1:1 contention at the price they are sold at.\n>\n> Maybe you should attempt to call a sales person at any company and ask the questions. I know with the carrier we use they are sold at a 4:1 if I remember right but never seen anything other than the max of the connection sold so far. The same can be said for the standard NBN plans over the past few years.\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **jimbeamau writes...**\n>> They are not a 1:1 contention at the price they are sold at.\n> Yes, I know that you won't get 1:1 contention ratio at that price. I'm not asking for that.\n>\n>> Maybe you should attempt to call a sales person at any company and ask the questions.\n> I think the ABB Enterprise plans are actually really good compared to the other telcos.\n>\n> I think they're the only ISP who now have pricing listed for all plans: personal, business, and enterprise TC4, TC2, EE.\n\n> **User #827652 (UserNameNotAvailable - Whirlpool Enthusiast)**\n>> **arrow-wp writes...**\n>> Exetel are much more expensive, but they do offer a few contract length options.\n> Exetel use Telstra Wholesale or AAPT\n> Aussie use NBN\n>\n> I know what I would rather use\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **J Holdman writes...**\n>> Exetel use Telstra Wholesale or AAPT\n> Exetel use NBN as well.\n>\n> [https://business.exetel.com.au/internet/nbn-enterprise-ethernet](https://forums.whirlpool.net.au/go?https%3A%2F%2Fbusiness.exetel.com.au%2Finternet%2Fnbn-enterprise-ethernet)\n\n> **User #796766 (Forum Regular - O.P.)**\n> [**Aussie Broadband nbn™ Enterprise Ethernet**](https://forums.whirlpool.net.au/go?https%3A%2F%2Fwww.aussiebroadband.com.au%2Fenterprise%2Fnbn-enterprise-ethernet%2F)\n>\n> Unlimited Data.\n> 36 month contract.\n> $0 fibre install (subject to a site qualification check)\n> Prices exclude GST.\n>\n> **Bronze Support package (included in all plans):**\n> Best effort SAG, nbn™\n> 12hr eSLA 24/7\n> 24/7 enterprise support\n>\n> **Silver Support package: $400/month**\n> 99% uptime SAG\n> nbn™ best eSLA (4 or 8 hr) 24/7\n> 24/7 enterprise support\n>\n> **Gold Support package: $600/month**\n> 99.95% uptime SAG\n> nbn™ best eSLA (4 or 8 hr) 24/7\n> 24/7 enterprise support\n>\n> **Symmetrical nbn ™ EE Low CoS – Best effort contention, Priority bandwidth:**\n> 10Mbps: $349.00/month\n> 20Mbps: $369.00/month\n> 30Mbps: $379.00/month\n> 40Mbps: $389.00/month\n> 50Mbps: $399.00/month\n> 60Mbps: $409.00/month\n> 70Mbps: $419.00/month\n> 80Mbps: $429.00/month\n> 90Mbps: $439.00/month\n> 100Mbps: $449.00/month\n> 200Mbps: $549.00/month\n> 300Mbps: $649.00/month\n> 400Mbps: $749.00/month\n> 500Mbps: $799.00/month\n> 600Mbps: $829.00/month\n> 700Mbps: $849.00/month\n> 800Mbps: $869.00/month\n> 900Mbps: $889.00/month\n> 1000Mbps: $899.00/month\n>\n> **Symmetrical nbn ™ EE High CoS – 1:1 contention, Committed bandwidth:**\n> 10Mbps: $699.00/month\n> 20Mbps: $749.00/month\n> 30Mbps: $799.00/month\n> 40Mbps: $829.00/month\n> 50Mbps: $849.00/month\n> 60Mbps: $859.00/month\n> 70Mbps: $869.00/month\n> 80Mbps: $879.00/month\n> 90Mbps: $889.00/month\n> 100Mbps: $899.00/month\n> 200Mbps: $999.00/month\n> 300Mbps: $1,099.00/month\n> 400Mbps: $1,199.00/month\n> 500Mbps: $1,249.00/month\n> 600Mbps: $1,299.00/month\n> 700Mbps: $1,349.00/month\n> 800Mbps: $1,399.00/month\n> 900Mbps: $1,449.00/month\n> 1000Mbps: $1,499.00/month\n>\n> [**Exetel nbn™ Enterprise Ethernet**](https://forums.whirlpool.net.au/go?https%3A%2F%2Fbusiness.exetel.com.au%2Finternet%2Fnbn-enterprise-ethernet)\n>\n> Unlimited Data.\n> nbn™ enhanced SLA 24/7.\n> 24/7 Support.\n> Dedicated account managers.\n>\n> **50Mbps**\n> 12 month contract: $725/month ($5,500 activation fee)\n> 24 month contract: $650/month ($2,750 activation fee)\n> 36 month contract: $600/month (no activation fee)\n>\n> **100Mbps**\n> 12 month contract: $875/month ($5,500 activation fee)\n> 24 month contract: $775/month ($2,750 activation fee)\n> 36 month contract: $750/month (no activation fee)\n>\n> **200Mbps**\n> 12 month contract: $1,225/month ($5,500 activation fee)\n> 24 month contract: $1,100/month ($2,750 activation fee)\n> 36 month contract: $1,050/month (no activation fee)\n>\n> **500Mbps**\n> 12 month contract: $3,500/month ($5,500 activation fee)\n> 24 month contract: $3,150/month ($2,750 activation fee)\n> 36 month contract: $2,975/month (no activation fee)\n>\n> **1000Mbps**\n> 12 month contract: $7,775/month ($5,500 activation fee)\n> 24 month contract: $7,000/month ($2,750 activation fee)\n> 36 month contract: $6,600/month (no activation fee)\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **arrow-wp writes...**\n>> Services above 100/40Mbps shaped to 100/100Mbps from 6PM to 12AM.\n> Only the non-EE (asymmetrical / TC4) services are shaped.\n>\n> The EE are full speed all the time.\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **The Bin Chicken writes...**\n>> Only the non-EE (asymmetrical / TC4) services are shaped.\n>> The EE are full speed all the time.\n> Sorry, copy/pasted incorrectly.\n> Will edit.\n\n> **User #827652 (UserNameNotAvailable - Whirlpool Enthusiast)**\n> You can can get 500/500 from Vocus on Vocus Fibre for $699 in Vocus enabled buildings or near net\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **J Holdman writes...**\n>> You can can get 500/500 from Vocus on Vocus Fibre for $699 in Vocus enabled buildings or near net\n> TPG (AAPT) Fibre 1000/1000 is $799/month for TPG lit buildings.\n>\n> Some areas don't have access to those services and will have to go for nbn™ EE.\n\n> **User #69479 (jimbeamau - Forum Regular)**\n>> **arrow-wp writes...**\n>> I think the ABB Enterprise plans are actually really good compared to the other telcos.\n> High CoS seems a little higher than I thought taking into consideration the TC4 pricing but then TC2 was higher well if I remember right.\n>\n> What zone is the pricing based on?\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **jimbeamau writes...**\n>> What zone is the pricing based on?\n> That'll most likely be CBD or Zone 1.\n\n> **User #6624 (Chris\\* - Whirlpool Forums Addict)**\n>> **arrow-wp writes...**\n>> TPG (AAPT) Fibre 1000/1000 is $799/month for TPG lit buildings.\n> They are also doing free builds if you're close enough to their existing coverage.\n\n> **User #117124 (Carts - Whirlpool Enthusiast)**\n> $0 fibre install on a 36-month contract\n>\n> Call me naive, but does this mean that someone who was quoted >20k for a TCP upgrade could connect on ABB’s EE 30/30 symmetrical plan for $379 a month ($13,644 over 3 years) saving >$6k in the process??? Surely not.\n\n> **User #117124 (Carts - Whirlpool Enthusiast)**\n> What’s to stop you then connecting back into a residential plan in 3 years?\n\n> **User #105247 (Dyde - Whirlpool Forums Addict)**\n>> **Carts writes...**\n>> What’s to stop you then connecting back into a residential plan in 3 years?\n> It’s a different NTU for EE, so if you wanted residential FTTP they would likely slug you an exorbitant TCP fee for the privilege of swapping the NTU.\n\n> **User #117124 (Carts - Whirlpool Enthusiast)**\n>> **Dyde writes...**\n>> they would likely slug you an exorbitant TCP fee for the privilege of swapping the NTU\n> Could they do this though? Surely you would pay for part and labour itemised on an invoice?\n\n> **User #53522 (Avocet Islander - Whirlpool Forums Addict)**\n>> **Carts writes...**\n>> Surely you would pay for part and labour itemised on an invoice?\n> As someone who has a high speed private sector fibre connection, when you get to this level of connection, invoices are not that detailed so you understandably just pay the bill.\n>\n> If you can get a symmetrical 1 or 2 Gbs or under $2500 a month in the current Aussie climate, you will be doing well. _Thank goodness for good professional overseas service providers who know how to work around the Aussie BS:p_\n\n> **User #75593 (symoo - Forum Regular)**\n> We have 1 Gbps connections at all of our sites for 699.99 including regional (which I'm pretty sure is rebadged Telstra fibre) MPLS or public doesn't make a difference in $, there has been some build costs but nothing over $5k (making it cheaper than anything else I've seen)\n\nArchive View [Return to standard view](https://forums.whirlpool.net.au/thread/30ym7769)\n</smtcmp_block>", "id": "4bd9e9de-84db-4250-8211-155dfcff3dd1", "metadata": {"model": {"providerType": "openrouter", "providerId": "openrouter", "id": "openrouter-g2.5flash", "model": "google/gemini-2.5-flash", "promptLevel": 1, "enable": true}, "usage": {"prompt_tokens": 11693, "completion_tokens": 6571, "total_tokens": 18264, "cost": 0.0199354, "is_byok": false, "prompt_tokens_details": {"cached_tokens": 0, "audio_tokens": 0}, "cost_details": {"upstream_inference_cost": null, "upstream_inference_prompt_cost": 0.0035079, "upstream_inference_completions_cost": 0.0164275}, "completion_tokens_details": {"reasoning_tokens": 0, "image_tokens": 0}}}}, {"role": "user", "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "add useful, lengthy, descriptive and comprehensive metadata", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "\n\nadd useful, lengthy, descriptive and comprehensive metadata\n\n"}], "id": "3a07acae-817e-4172-a21c-a547e3d35971", "mentionables": [{"type": "current-file", "file": "2-Areas/Network-Administration/Aussie Broadband Enterprise -  Choosing an ISP.md"}]}, {"role": "assistant", "content": "To add useful, lengthy, descriptive, and comprehensive metadata to this document, I will incorporate the following into the YAML frontmatter:\n\n*   **`title`**: A clear, comprehensive title for the document.\n*   **`aliases`**: Alternative titles or common references.\n*   **`tags`**: A broad range of relevant keywords for categorization.\n*   **`date`**: The date the clipping was created.\n*   **`source`**: The original source URL of the forum thread.\n*   **`forum`**: The name of the forum.\n*   **`category`**: The general topic category on the forum.\n*   **`topic`**: The specific discussion topic.\n*   **`participants`**: A list of key users who contributed significantly to the discussion.\n*   **`keywords`**: More specific keywords and concepts discussed.\n*   **`summary`**: A concise summary of the document's content and key takeaways.\n*   **`discussion_points`**: Key questions or issues raised and debated.\n*   **`companies_mentioned`**: ISPs and related entities discussed.\n*   **`products_services`**: Specific plans or services highlighted.\n*   **`contract_terms`**: Details about contract lengths and fees.\n*   **`technical_terms`**: Any technical jargon used in the discussion.\n\nHere's the updated content for `Clippings/Aussie Broadband Enterprise - Choosing an ISP.md`:\n\n<smtcmp_block filename=\"Clippings/Aussie Broadband Enterprise - Choosing an ISP.md\" language=\"markdown\">\n---\ntitle: \"Whirlpool Forum Discussion: Choosing an ISP - Aussie Broadband NBN Enterprise Ethernet and TC4 Plans\"\naliases:\n  - \"Aussie Broadband Enterprise Ethernet Discussion\"\n  - \"NBN Enterprise vs Business Plans Whirlpool\"\n  - \"ISP Contract Lengths NBN\"\ntags:\n  - NBN\n  - EnterpriseEthernet\n  - ISPComparison\n  - AussieBroadband\n  - Exetel\n  - FutureBroadband\n  - BusinessInternet\n  - ContractTerms\n  - ServiceLevelAgreement\n  - WhirlpoolForums\n  - Telecommunications\n  - FibreInternet\n  - TC4Plans\ndate: 2023-10-27\nsource: https://forums.whirlpool.net.au/thread/30ym7769\nforum: Whirlpool Forums\ncategory: Broadband\ntopic: Aussie Broadband Enterprise - Choosing an ISP\nparticipants:\n  - User #796766 (arrow-wp)\n  - User #812634 (Melissa P.)\n  - User #116177 (The Bin Chicken)\n  - User #69479 (jimbeamau)\nkeywords:\n  - NBN Enterprise Ethernet pricing\n  - NBN TC4 business plans\n  - Contract length options\n  - Activation fees\n  - Service Level Agreements (SLA)\n  - Static IP address\n  - Fibre build costs\n  - Plan builder\n  - 1:1 contention ratio\n  - CoS (Class of Service)\ncompanies_mentioned:\n  - Aussie Broadband (ABB)\n  - Exetel\n  - Future Broadband\n  - Vocus\n  - TPG (AAPT)\n  - NBN Co\nproducts_services:\n  - nbn™ Enterprise Ethernet (EE)\n  - nbn™ TC-4 plans (Personal, Business, Enterprise)\n  - IP-Line Enterprise (Future Broadband)\n  - Bronze Support Package\n  - Silver Support Package\n  - Gold Support Package\n  - Symmetrical nbn™ EE Low CoS\n  - Symmetrical nbn™ EE High CoS\ncontract_terms:\n  - 12-month contract\n  - 24-month contract\n  - 36-month contract\n  - No lock-in contract\n  - Activation fee\n  - $0 fibre install\ntechnical_terms:\n  - nbn™ eSLA\n  - SAG (Service Assurance Guarantee)\n  - NTU (Network Termination Unit)\n  - POI (Point of Interconnect)\n  - IP transit\n  - CoS (Class of Service)\n  - BGP (Border Gateway Protocol)\n  - Static Routing\n  - FTTP (Fibre to the Premises)\n  - TCP upgrade (Third Party Contractor)\nsummary: |\n  This document captures a Whirlpool forum discussion discussing and comparing enterprise-level NBN internet plans, specifically focusing on Aussie Broadband's (ABB) Enterprise Ethernet and TC4 offerings against competitors like Exetel and Future Broadband. The primary points of contention and discussion revolve around contract length options, activation fees, inclusion of Service Level Agreements (SLAs), pricing structures for different speed tiers and Classes of Service (CoS), and the perceived value of \"true business services\" versus enhanced residential plans. Users debate the flexibility of contract terms, the necessity of longer contracts for fibre builds, and the transparency of pricing from various ISPs, including the challenges of obtaining quotes without providing contact details. The conversation also touches on technical aspects like contention ratios, support packages, and the distinction between NBN EE and standard TC4 (FTTP) services.\ndiscussion_points:\n  - Are there plans for ABB to offer more flexible contract lengths for NBN Enterprise plans (e.g., 12 or 24 months instead of only 36 months)?\n  - Why is customer contact information required to view Enterprise plan prices, and will a plan builder be implemented?\n  - What are the differences and justifications for contract lengths, activation fees, and service inclusions between ABB, Exetel, and Future Broadband's enterprise offerings?\n  - Is ABB's Enterprise NBN TC4 plan (36-month contract) truly an \"enterprise\" service, or just an enhanced business/residential plan?\n  - What are the implications of different Service Level Agreements (SLAs) and contention ratios (e.g., Low CoS vs. High CoS 1:1 contention)?\n  - Is it possible to avoid significant fibre build costs by opting for a 36-month contract and then downgrade, and what are the technical challenges (e.g., NTU differences)?\n---\n</smtcmp_block>", "id": "c35613b9-d3fb-4a5c-8c71-275154d00250", "metadata": {"model": {"providerType": "openrouter", "providerId": "openrouter", "id": "openrouter-g2.5flash", "model": "google/gemini-2.5-flash", "promptLevel": 1, "enable": true}, "usage": {"prompt_tokens": 13713, "completion_tokens": 1345, "total_tokens": 15058, "cost": 0.0074764, "is_byok": false, "prompt_tokens_details": {"cached_tokens": 0, "audio_tokens": 0}, "cost_details": {"upstream_inference_cost": null, "upstream_inference_prompt_cost": 0.0041139, "upstream_inference_completions_cost": 0.0033625}, "completion_tokens_details": {"reasoning_tokens": 0, "image_tokens": 0}}}}, {"role": "user", "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "we need headings here so that a table of contents can be generated. we also need some wiki links to help researchers in the vault", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "\n\nwe need headings here so that a table of contents can be generated. we also need some wiki links to help researchers in the vault\n\n"}], "id": "81f6631d-9cd2-447b-b635-e4667ad3276f", "mentionables": [{"type": "current-file", "file": "2-Areas/Network-Administration/Aussie Broadband Enterprise -  Choosing an ISP.md"}]}, {"role": "assistant", "content": "To incorporate headings for a table of contents and add wiki links, I will make the following edits:\n\n1.  **Add `##` (H2) headings** for major sections: \"Introduction,\" \"User Discussions,\" and \"ISP Plan Comparisons.\"\n2.  **Add `###` (H3) headings** for sub-sections within \"User Discussions\" to group related posts, such as \"Contract Lengths and Flexibility,\" \"Pricing Transparency and Plan Builders,\" \"NBN Enterprise vs. Business/TC4 Plans,\" \"Technical Aspects & SLAs,\" and \"ISP Alternatives & Fibre Builds.\"\n3.  **Introduce Wiki Links**: Convert relevant terms, company names, and technical jargon into `[[Wiki Links]]` to connect them to potential notes within the vault. This includes terms like `[[NBN Enterprise Ethernet]]`, `[[Aussie Broadband]]`, `[[Exetel]]`, `[[Future Broadband]]`, `[[Service Level Agreement (SLA)]]`, `[[Contention Ratio]]`, `[[Static IP]]`, `[[Fibre to the Premises (FTTP)]]`, `[[Network Termination Unit (NTU)]]`, `[[Class of Service (CoS)]]`, `[[IP Transit]]`, `[[Vocus]]`, `[[TPG Telecom]]`, `[[NBN Co]]`, along with general concepts like `[[ISP Comparison]]`, `[[Contract Terms]]`, `[[Activation Fees]]`, `[[Business Internet]]`, `[[Telecommunications]]`, and `[[Whirlpool Forums]]`.\n\nHere's the updated markdown:\n\n<smtcmp_block filename=\"Clippings/Aussie Broadband Enterprise - Choosing an ISP.md\" language=\"markdown\">\n---\ntitle: \"Whirlpool Forum Discussion: Choosing an ISP - Aussie Broadband NBN Enterprise Ethernet and TC4 Plans\"\naliases:\n  - \"Aussie Broadband Enterprise Ethernet Discussion\"\n  - \"NBN Enterprise vs Business Plans Whirlpool\"\n  - \"ISP Contract Lengths NBN\"\ntags:\n  - NBN\n  - EnterpriseEthernet\n  - ISPComparison\n  - AussieBroadband\n  - Exetel\n  - FutureBroadband\n  - BusinessInternet\n  - ContractTerms\n  - ServiceLevelAgreement\n  - WhirlpoolForums\n  - Telecommunications\n  - FibreInternet\n  - TC4Plans\ndate: 2023-10-27\nsource: https://forums.whirlpool.net.au/thread/30ym7769\nforum: Whirlpool Forums\ncategory: Broadband\ntopic: Aussie Broadband Enterprise - Choosing an ISP\nparticipants:\n  - User #796766 (arrow-wp)\n  - User #812634 (Melissa P.)\n  - User #116177 (The Bin Chicken)\n  - User #69479 (jimbeamau)\nkeywords:\n  - NBN Enterprise Ethernet pricing\n  - NBN TC4 business plans\n  - Contract length options\n  - Activation fees\n  - Service Level Agreements (SLA)\n  - Static IP address\n  - Fibre build costs\n  - Plan builder\n  - 1:1 contention ratio\n  - CoS (Class of Service)\ncompanies_mentioned:\n  - Aussie Broadband (ABB)\n  - Exetel\n  - Future Broadband\n  - Vocus\n  - TPG (AAPT)\n  - NBN Co\nproducts_services:\n  - nbn™ Enterprise Ethernet (EE)\n  - nbn™ TC-4 plans (Personal, Business, Enterprise)\n  - IP-Line Enterprise (Future Broadband)\n  - Bronze Support Package\n  - Silver Support Package\n  - Gold Support Package\n  - Symmetrical nbn™ EE Low CoS\n  - Symmetrical nbn™ EE High CoS\ncontract_terms:\n  - 12-month contract\n  - 24-month contract\n  - 36-month contract\n  - No lock-in contract\n  - Activation fee\n  - $0 fibre install\ntechnical_terms:\n  - nbn™ eSLA\n  - SAG (Service Assurance Guarantee)\n  - NTU (Network Termination Unit)\n  - POI (Point of Interconnect)\n  - IP transit\n  - CoS (Class of Service)\n  - BGP (Border Gateway Protocol)\n  - Static Routing\n  - FTTP (Fibre to the Premises)\n  - TCP upgrade (Third Party Contractor)\nsummary: |\n  This document captures a [[Whirlpool Forums]] discussion discussing and comparing enterprise-level [[NBN]] internet plans, specifically focusing on [[Aussie Broadband]]'s (ABB) [[NBN Enterprise Ethernet]] and TC4 offerings against competitors like [[Exetel]] and [[Future Broadband]]. The primary points of contention and discussion revolve around [[Contract Terms]] options, [[Activation Fees]], inclusion of [[Service Level Agreement (SLA)]]s, pricing structures for different speed tiers and [[Class of Service (CoS)]]s, and the perceived value of \"true [[Business Internet]] services\" versus enhanced residential plans. Users debate the flexibility of contract terms, the necessity of longer contracts for fibre builds, and the transparency of pricing from various [[Telecommunications|ISPs]], including the challenges of obtaining quotes without providing contact details. The conversation also touches on technical aspects like [[Contention Ratio]]s, support packages, and the distinction between [[NBN Enterprise Ethernet|NBN EE]] and standard [[Fibre to the Premises (FTTP)|TC4]] services.\ndiscussion_points:\n  - Are there plans for [[Aussie Broadband|ABB]] to offer more flexible [[Contract Terms]] for [[NBN Enterprise Ethernet|NBN Enterprise plans]] (e.g., 12 or 24 months instead of only 36 months)?\n  - Why is customer contact information required to view Enterprise plan prices, and will a plan builder be implemented?\n  - What are the differences and justifications for [[Contract Terms]], [[Activation Fees]], and service inclusions between [[Aussie Broadband|ABB]], [[Exetel]], and [[Future Broadband]]'s enterprise offerings?\n  - Is [[Aussie Broadband|ABB]]'s [[Fibre to the Premises (FTTP)|Enterprise NBN TC4 plan]] (36-month contract) truly an \"enterprise\" service, or just an enhanced business/residential plan?\n  - What are the implications of different [[Service Level Agreement (SLA)]]s and [[Contention Ratio]]s (e.g., Low [[Class of Service (CoS)|CoS]] vs. High [[Class of Service (CoS)|CoS]] 1:1 contention)?\n  - Is it possible to avoid significant fibre build costs by opting for a 36-month contract and then downgrade, and what are the technical challenges (e.g., [[Network Termination Unit (NTU)]] differences)?\n---\n\n## Introduction\nArchive View [Return to standard view](https://forums.whirlpool.net.au/thread/30ym7769)\n\n## User Discussions\n\n### Contract Lengths and Flexibility\n> **User #796766 (Forum Regular - O.P.)**\n> Hey [[Aussie Broadband|ABB]],\n>\n> Any plans to offer addtional [[Contract Terms|contract length]] options for the [[NBN Enterprise Ethernet|nbn™ Enterprise plans]]?\n>\n> **12 month + [[Activation Fees|activation fee]].\n> No lock-in contract + [[Activation Fees|activation fee]]**\n>\n> [[Aussie Broadband|ABB]] and [[Exetel]] seem to be the only [[Telecommunications|ISPs]] that have [[NBN Enterprise Ethernet|Enterprise Ethernet pricing]] available via their websites (visible after filling out a contact details form)\n>\n> [[Exetel]] are much more expensive, but they do offer a few [[Contract Terms|contract length]] options.\n> 36 month contract with no [[Activation Fees|activation fee]].\n> 12 or 24 month contract + [[Activation Fees|activation fee]].\n>\n> Also, please consider removing the requirement to provide contact details to view the the plan prices.\n\n### Pricing Transparency and Plan Builders\n> **User #69531 (Pirate Steve - Whirlpool Forums Addict)**\n> Have you had a look [[Future Broadband|Future Broardband Enterprise plans]]?\n\n> **User #19447 (Moonman - Inactive Account)**\n> I see Best of all, eligible businesses can now access a $0 fibre install on a 36-month contract (subject to a site qualification check\\*) on their website too.\n\n> **User #57727 (iHome - Whirlpool Forums Addict)**\n>> **arrow-wp writes...**\n>> Also, please consider removing the requirement to provide contact details to view the the plan prices.\n> Yes, either that or don't use phone as the first point of contact with the customer....I was only keen to see what the pricing was like as consideration for moving an NFP off another plan and I had someone try to touch base with me twice in 2 days, when I clearly wasn't interested in discussing an offering.\n\n> **User #812634 (Melissa P. - Whirlpool Enthusiast)**\n> I believe the goal is to get a plan builder on the page. There's been a couple delays with this, but it is still in the works. It's a bit too difficult to just display prices as our enterprise plans are complex plans where customers can pick and choose the level of service they'd like on a multitude of items. Also, the fibre upgrade plans are contingent on zones for actual cost. For those currently seeking a price, it's best to contact our BDM's for an exact quote until the plan builder is in place.\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **Melissa P. writes...**\n>> I believe the goal is to get a plan builder on the page. There's been a couple delays with this, but it is still in the works.\n> I thought the plan builder was already on the page? I can see the pricing after filling out the contact details form.\n> [https://www.aussiebroadband.com.au/enterprise/nbn-enterprise-ethernet/](https://forums.whirlpool.net.au/go?https%3A%2F%2Fwww.aussiebroadband.com.au%2Fenterprise%2Fnbn-enterprise-ethernet%2F)\n>\n>> Also, the fibre upgrade plans are contingent on zones for actual cost.\n> Yes, that's understandable for those instances that involve a fibre build cost.\n>\n> However, it seems like the minimum [[Contract Terms|contract length]] for the [[Aussie Broadband|ABB]] [[Fibre to the Premises (FTTP)|TC-4 plans]] is also 36 months.\n>\n> **[[Fibre to the Premises (FTTP)|TC-4 plans]]**:\n> Personal 100/40: $99\n> No lock-in contract\n> No setup fee\n>\n> Business 100/40: $109\n> No lock-in contract\n> No setup fee\n> Includes a [[Static IP|static IP address]]\n>\n> Enterprise 100/40: $129\n> Minimum 36 month contract\n> No setup fee\n> Includes a [[Static IP|static IP address]]\n> Bronze Support Package (Best effort SAG, [[NBN]] 12hr [[Service Level Agreement (SLA)|eSLA]] 24/7, 24/7 enterprise support)\n>\n> For comparison, [[Future Broadband]] are offering their [[Business Internet|Enterprise NBN 100/40 plan]] (which has the same [[NBN]] 12hr [[Service Level Agreement (SLA)|eSLA]] 24/7, and 24/7 enterprise support) with the following [[Contract Terms|contract length]] options:\n> [https://futurebroadband.com.au/ipline/](https://forums.whirlpool.net.au/go?https%3A%2F%2Ffuturebroadband.com.au%2Fipline%2F)\n> 1 month\n> 12 month\n> 24 month\n\n### NBN Enterprise vs. Business/TC4 Plans\n> **User #72704 (Grumps - Whirlpool Forums Addict)**\n> Why the hell would you want to go on a contract?\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **Grumps writes...**\n>> Why the hell would you want to go on a contract?\n> I don't want to go on a contract.\n>\n> [[Aussie Broadband|ABB]] only offer the [[NBN Enterprise Ethernet|Enterprise nbn™ TC4]] with a minimum 36 months contract.\n>\n> [[Future Broadband|Future BB]] offer it with either no lock-in contract, or 12/24 months.\n\n> **User #72704 (Grumps - Whirlpool Forums Addict)**\n> This is a worry they use [[TPG (AAPT)|AAPT]] for both [[POI (Point of Interconnect)|POI]] connectivity as well as state peering and [[IP Transit|IP transit]].\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **Grumps writes...**\n>> they use [[TPG (AAPT)|AAPT]] for both [[POI (Point of Interconnect)|POI]] connectivity as well as state peering and [[IP Transit|IP transit]].\n> Yes, they're much smaller compared to [[Aussie Broadband|ABB]].\n>\n> [[Future Broadband|FutureBB]] do have some good 'enterprise' [[NBN]] plans though.\n> - Transparent pricing.\n> - Decent [[Contract Terms|contract lengths]].\n\n> **User #796766 (Forum Regular - O.P.)**\n> On a sidenote, [[Aussie Broadband|ABB]] reps can now remove the Wideband part from their tags, since that brand has been retired.\n\n> **User #53522 (Avocet Islander - Whirlpool Forums Addict)**\n> OP, there are quite a few RSPs out there so pick one that suits your needs.\n>\n> Most RPSs, including [[Aussie Broadband|Aussie BB]], run a pretty tight ship, so do not expect any of them to offer plans outside their current spread just to suit individuals. Reason, the only thing that changes RSPs plans are market forces so it will take a lot of people leaving to encourage your favourite RSP to change it's current offers.\n\n> **User #593834 (Andrew (N. Sydney) - Whirlpool Forums Addict)**\n>> **Grumps writes...**\n>> Why the hell would you want to go on a contract?\n> Stability, efficiency, risk reduction..... etc.\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **Grumps writes...**\n>> Why the hell would you want to go on a contract?\n> Given the fibre build to the premises is included on 36 months, why not?\n>\n> This is for [[NBN Enterprise Ethernet|enterprise ethernet]], not a residential quality plan.\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **Zerophitus writes...**\n>> Most RPSs, including [[Aussie Broadband|Aussie BB]], run a pretty tight ship, so do not expect any of them to offer plans outside their current spread just to suit individuals.\n> Again, these are enterprise plans.\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **arrow-wp writes...**\n>> [[Future Broadband|Future BB]] offer it with either no lock-in contract, or 12/24 months.\n> [[Future Broadband|FutureBB]] do not appear to offer [[NBN Enterprise Ethernet|NBN EE]] on their website. It wouldn't surprise me if they did, but I could only see [[Fibre to the Premises (FTTP)|TC4]], which is normal [[Fibre to the Premises (FTTP)|FTTP]].\n\n> **User #245859 (crazyuser - Whirlpool Enthusiast)**\n> Most corporate/enterprise services (especially if you get an [[Telecommunications|ISP]] to build Fibre to your premises are over 36 months (3yrs) or 60 months (5yrs).\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **The Bin Chicken writes...**\n>> [[Future Broadband|FutureBB]] do not appear to offer [[NBN Enterprise Ethernet|NBN EE]] on their website. It wouldn't surprise me if they did, but I could only see [[Fibre to the Premises (FTTP)|TC4]], which is normal [[Fibre to the Premises (FTTP)|FTTP]].\n> Yes, you're right – they don't have an [[NBN Enterprise Ethernet|nbn™ EE]] plan on their website.\n> They do offer ['[[Business Internet|Enterprise]]' [[Fibre to the Premises (FTTP)|TC4']](https://forums.whirlpool.net.au/go?https%3A%2F%2Ffuturebroadband.com.au%2Fipline%2F) plans.\n>\n> **[[Future Broadband]] [[NBN]]100 [[Fibre to the Premises (FTTP)|TC4]] – 12 Month IP-Line Enterprise**\n> $125p/m (Total cost $1,600 over 12 months incl $100 setup)\n>\n> Service Level Features:\n> [[NBN]] 12hour (24/7) Restoration Target (included)\n> - Upgrade to 4hour (BH) from $55pm\n> - Upgrade to 4hour (24/7) from $95pm\n>\n> Tech Features:\n> - [[Static IP|Static IP Address]]\n> - [[BGP (Border Gateway Protocol)|BGP]] or [[Static Routing|Static Routing]]\n> - Bring your own IP Space\n> - [[Business Internet|Business-Grade IP Network]]\n> - Managed Hardware available\n>\n> **[[Aussie Broadband]] Enterprise [[Fibre to the Premises (FTTP)|TC4]]**\n> Enterprise 100/40: $129/month\n> Minimum 36 month contract\n> No setup fee\n> Includes a [[Static IP|static IP address]]\n> Bronze Support Package (Best effort SAG, [[NBN]] 12hr [[Service Level Agreement (SLA)|eSLA]] 24/7, 24/7 enterprise support)\n>\n> Silver Support Package $100/month\n> 99% uptime SAG, [[NBN]] best [[Service Level Agreement (SLA)|eSLA]] (4 or 6 hr) 24/7, 24/7 enterprise support\n>\n> Gold Support Package $150/month\n> 99.95% uptime SAG, [[NBN]] best [[Service Level Agreement (SLA)|eSLA]] (4 or 6 hr) 24/7, 24/7 enterprise support\n>\n> [[Aussie Broadband]] [[Business Internet|Business]] [[NBN]] [[Fibre to the Premises (FTTP)|TC4]] 100/40 is $109/month, with no contract, and no setup fees. If a business wants an enhanced [[Service Level Agreement (SLA)|SLA]] then they will need to upgrade to the enterprise [[Fibre to the Premises (FTTP)|TC4]] plan for $129/month. The price isn't an issue, but the minimum contract of 36 months is quite a jump.\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **The Bin Chicken writes...**\n>> This is for [[NBN Enterprise Ethernet|enterprise ethernet]], not a residential quality plan.\n> [[Aussie Broadband|ABB]] [[NBN]] [[Fibre to the Premises (FTTP)|TC4]] is a 'residential quality plan.'\n>\n> Personal: 99/month, no contract\n> Business: 109/month, no contract (includes [[Static IP|static IP]])\n> Enterprise: $129/month, 36 month contract ([[Static IP|static IP]], [[NBN]] [[Service Level Agreement (SLA)|eSLA]], 24/7 support)\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **arrow-wp writes...**\n>> [[Aussie Broadband]] [[Business Internet|Business]] [[NBN]] [[Fibre to the Premises (FTTP)|TC4]] 100/40 is $109/month, with no contract, and no setup fees. If a business wants an enhanced [[Service Level Agreement (SLA)|SLA]] then they will need to upgrade to the enterprise [[Fibre to the Premises (FTTP)|TC4]] plan for $129/month. The price isn't an issue, but the minimum contract of 36 months is quite a jump.\n> Speak to them, see if you can get a different deal. They would be more flexible with their enterprise plans (non [[NBN Enterprise Ethernet|NBN EE]]) than the residential ones, especially if there are competing offers.\n>\n>> **arrow-wp writes...**\n>> [[NBN]] [[Fibre to the Premises (FTTP)|TC4]] is a 'residential quality plan.'\n> What are you after? In your OP, you refer to [[NBN Enterprise Ethernet|NBN EE]]. Now you're talking about [[Fibre to the Premises (FTTP)|NBN TC4]].\n>\n> You may be able to get a different deal when it comes to non-[[NBN Enterprise Ethernet|EE]] [[Fibre to the Premises (FTTP)|TC4]] plans, but with [[NBN Enterprise Ethernet|NBN EE]], RSPs will probably not budge too much because the [[Contract Terms|contract period]] changes the build cost (100% discount on standard build for 36 months, 75% for 24 and no discount for 12 months or below) as well as ongoing costs:\n> [https://www.nbnco.com.au/content/dam/nbnco2/2019/documents/sell/wba/nbn-enterprise-ethernet-charges-subject-to-price-confirmation.pdf](https://forums.whirlpool.net.au/go?https%3A%2F%2Fwww.nbnco.com.au%2Fcontent%2Fdam%2Fnbnco2%2F2019%2Fdocuments%2Fsell%2Fwba%2Fnbn-enterprise-ethernet-charges-subject-to-price-confirmation.pdf)\n\n> **User #69479 (jimbeamau - Forum Regular)**\n> Almost all true [[Business Internet|business services]] come with a contract or setup fee for shorter period. Business don't swap and change providers so I really don't see a problem with the 3 year contact but it would be nice to have the option to bring it down for those short term or expiring leases.\n>\n> Business are use to contacts for services like phones, internet, etc. And because the service is usually of a higher quality than consumer problems rarely arise. Consumer services I am all for no contracts because these can change overnight in quality. [[Contention Ratio|Contention ratio's]], standard vs premium, etc can all make a difference.\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **jimbeamau writes...**\n>> Almost all true [[Business Internet|business services]] come with a contract or setup fee for shorter period.\n> The setup fee for a shorter period is fine.\n>\n> I don't consider the [[Fibre to the Premises (FTTP)|TC4 plans]] to be a 'true [[Business Internet|business service]]' like [[NBN Enterprise Ethernet|nbn™ EE]].\n>\n> I'd like the ability to bolt-on an enhanced [[Service Level Agreement (SLA)|SLA]] to the smaller business plans (for those customers that would like a better restoration timeframe from [[NBN Co|nbn co]], but don't need 1:1 [[Contention Ratio|contention ratio]])\n\n### Technical Aspects & SLAs\n> **User #69479 (jimbeamau - Forum Regular)**\n> They are not a 1:1 [[Contention Ratio|contention]] at the price they are sold at.\n>\n> Maybe you should attempt to call a sales person at any company and ask the questions. I know with the carrier we use they are sold at a 4:1 if I remember right but never seen anything other than the max of the connection sold so far. The same can be said for the standard [[NBN]] plans over the past few years.\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **jimbeamau writes...**\n>> They are not a 1:1 [[Contention Ratio|contention]] at the price they are sold at.\n> Yes, I know that you won't get 1:1 [[Contention Ratio|contention ratio]] at that price. I'm not asking for that.\n>\n>> Maybe you should attempt to call a sales person at any company and ask the questions.\n> I think the [[Aussie Broadband|ABB Enterprise plans]] are actually really good compared to the other [[Telecommunications|telcos]].\n>\n> I think they're the only [[Telecommunications|ISP]] who now have pricing listed for all plans: personal, business, and enterprise [[Fibre to the Premises (FTTP)|TC4]], TC2, [[NBN Enterprise Ethernet|EE]].\n\n> **User #827652 (UserNameNotAvailable - Whirlpool Enthusiast)**\n>> **arrow-wp writes...**\n>> [[Exetel]] are much more expensive, but they do offer a few [[Contract Terms|contract length]] options.\n> [[Exetel]] use [[Telstra Wholesale]] or [[TPG (AAPT)|AAPT]]\n> [[Aussie Broadband|Aussie]] use [[NBN]]\n>\n> I know what I would rather use\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **J Holdman writes...**\n>> [[Exetel]] use [[Telstra Wholesale]] or [[TPG (AAPT)|AAPT]]\n> [[Exetel]] use [[NBN]] as well.\n>\n> [https://business.exetel.com.au/internet/nbn-enterprise-ethernet](https://forums.whirlpool.net.au/go?https%3A%2F%2Fbusiness.exetel.com.au%2Finternet%2Fnbn-enterprise-ethernet)\n\n## ISP Plan Comparisons\n\n### Aussie Broadband NBN Enterprise Ethernet\n> **User #796766 (Forum Regular - O.P.)**\n> [**[[Aussie Broadband]] [[NBN Enterprise Ethernet|nbn™ Enterprise Ethernet]]**](https://forums.whirlpool.net.au/go?https%3A%2F%2Fwww.aussiebroadband.com.au%2Fenterprise%2Fnbn-enterprise-ethernet%2F)\n>\n> Unlimited Data.\n> 36 month contract.\n> $0 fibre install (subject to a site qualification check)\n> Prices exclude GST.\n>\n> **Bronze Support package (included in all plans):**\n> Best effort SAG, [[NBN]]\n> 12hr [[Service Level Agreement (SLA)|eSLA]] 24/7\n> 24/7 enterprise support\n>\n> **Silver Support package: $400/month**\n> 99% uptime [[SAG (Service Assurance Guarantee)|SAG]]\n> [[NBN]] best [[Service Level Agreement (SLA)|eSLA]] (4 or 8 hr) 24/7\n> 24/7 enterprise support\n>\n> **Gold Support package: $600/month**\n> 99.95% uptime [[SAG (Service Assurance Guarantee)|SAG]]\n> [[NBN]] best [[Service Level Agreement (SLA)|eSLA]] (4 or 8 hr) 24/7\n> 24/7 enterprise support\n>\n> **Symmetrical [[NBN]] ™ [[NBN Enterprise Ethernet|EE]] Low [[Class of Service (CoS)|CoS]] – Best effort contention, Priority bandwidth:**\n> 10Mbps: $349.00/month\n> 20Mbps: $369.00/month\n> 30Mbps: $379.00/month\n> 40Mbps: $389.00/month\n> 50Mbps: $399.00/month\n> 60Mbps: $409.00/month\n> 70Mbps: $419.00/month\n> 80Mbps: $429.00/month\n> 90Mbps: $439.00/month\n> 100Mbps: $449.00/month\n> 200Mbps: $549.00/month\n> 300Mbps: $649.00/month\n> 400Mbps: $749.00/month\n> 500Mbps: $799.00/month\n> 600Mbps: $829.00/month\n> 700Mbps: $849.00/month\n> 800Mbps: $869.00/month\n> 900Mbps: $889.00/month\n> 1000Mbps: $899.00/month\n>\n> **Symmetrical [[NBN]] ™ [[NBN Enterprise Ethernet|EE]] High [[Class of Service (CoS)|CoS]] – 1:1 contention, Committed bandwidth:**\n> 10Mbps: $699.00/month\n> 20Mbps: $749.00/month\n> 30Mbps: $799.00/month\n> 40Mbps: $829.00/month\n> 50Mbps: $849.00/month\n> 60Mbps: $859.00/month\n> 70Mbps: $869.00/month\n> 80Mbps: $879.00/month\n> 90Mbps: $889.00/month\n> 100Mbps: $899.00/month\n> 200Mbps: $999.00/month\n> 300Mbps: $1,099.00/month\n> 400Mbps: $1,199.00/month\n> 500Mbps: $1,249.00/month\n> 600Mbps: $1,299.00/month\n> 700Mbps: $1,349.00/month\n> 800Mbps: $1,399.00/month\n> 900Mbps: $1,449.00/month\n> 1000Mbps: $1,499.00/month\n\n### Exetel NBN Enterprise Ethernet\n> **User #796766 (Forum Regular - O.P.)**\n> [**[[Exetel]] [[NBN Enterprise Ethernet|nbn™ Enterprise Ethernet]]**](https://forums.whirlpool.net.au/go?https%3A%2F%2Fbusiness.exetel.com.au%2Finternet%2Fnbn-enterprise-ethernet)\n>\n> Unlimited Data.\n> [[NBN]] enhanced [[Service Level Agreement (SLA)|SLA]] 24/7.\n> 24/7 Support.\n> Dedicated account managers.\n>\n> **50Mbps**\n> 12 month contract: $725/month ($5,500 [[Activation Fees|activation fee]])\n> 24 month contract: $650/month ($2,750 [[Activation Fees|activation fee]])\n> 36 month contract: $600/month (no [[Activation Fees|activation fee]])\n>\n> **100Mbps**\n> 12 month contract: $875/month ($5,500 [[Activation Fees|activation fee]])\n> 24 month contract: $775/month ($2,750 [[Activation Fees|activation fee]])\n> 36 month contract: $750/month (no [[Activation Fees|activation fee]])\n>\n> **200Mbps**\n> 12 month contract: $1,225/month ($5,500 [[Activation Fees|activation fee]])\n> 24 month contract: $1,100/month ($2,750 [[Activation Fees|activation fee]])\n> 36 month contract: $1,050/month (no [[Activation Fees|activation fee]])\n>\n> **500Mbps**\n> 12 month contract: $3,500/month ($5,500 [[Activation Fees|activation fee]])\n> 24 month contract: $3,150/month ($2,750 [[Activation Fees|activation fee]])\n> 36 month contract: $2,975/month (no [[Activation Fees|activation fee]])\n>\n> **1000Mbps**\n> 12 month contract: $7,775/month ($5,500 [[Activation Fees|activation fee]])\n> 24 month contract: $7,000/month ($2,750 [[Activation Fees|activation fee]])\n> 36 month contract: $6,600/month (no [[Activation Fees|activation fee]])\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **arrow-wp writes...**\n>> Services above 100/40Mbps shaped to 100/100Mbps from 6PM to 12AM.\n> Only the non-[[NBN Enterprise Ethernet|EE]] (asymmetrical / [[Fibre to the Premises (FTTP)|TC4]]) services are shaped.\n>\n> The [[NBN Enterprise Ethernet|EE]] are full speed all the time.\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **The Bin Chicken writes...**\n>> Only the non-[[NBN Enterprise Ethernet|EE]] (asymmetrical / [[Fibre to the Premises (FTTP)|TC4]]) services are shaped.\n>> The [[NBN Enterprise Ethernet|EE]] are full speed all the time.\n> Sorry, copy/pasted incorrectly.\n> Will edit.\n\n### ISP Alternatives & Fibre Builds\n> **User #827652 (UserNameNotAvailable - Whirlpool Enthusiast)**\n> You can can get 500/500 from [[Vocus]] on [[Vocus Fibre]] for $699 in [[Vocus]] enabled buildings or near net\n\n> **User #796766 (Forum Regular - O.P.)**\n>> **J Holdman writes...**\n>> You can can get 500/500 from [[Vocus]] on [[Vocus Fibre]] for $699 in [[Vocus]] enabled buildings or near net\n> [[TPG (AAPT)|TPG (AAPT)]] Fibre 1000/1000 is $799/month for [[TPG (AAPT)|TPG lit buildings]].\n>\n> Some areas don't have access to those services and will have to go for [[NBN Enterprise Ethernet|nbn™ EE]].\n\n> **User #69479 (jimbeamau - Forum Regular)**\n>> **arrow-wp writes...**\n>> I think the [[Aussie Broadband|ABB Enterprise plans]] are actually really good compared to the other [[Telecommunications|telcos]].\n> High [[Class of Service (CoS)|CoS]] seems a little higher than I thought taking into consideration the [[Fibre to the Premises (FTTP)|TC4]] pricing but then TC2 was higher well if I remember right.\n>\n> What zone is the pricing based on?\n\n> **User #116177 (The Bin Chicken - Whirlpool Forums Addict)**\n>> **jimbeamau writes...**\n>> What zone is the pricing based on?\n> That'll most likely be CBD or Zone 1.\n\n> **User #6624 (Chris\\* - Whirlpool Forums Addict)**\n>> **arrow-wp writes...**\n>> [[TPG (AAPT)|TPG (AAPT)]] Fibre 1000/1000 is $799/month for [[TPG (AAPT)|TPG lit buildings]].\n> They are also doing free builds if you're close enough to their existing coverage.\n\n> **User #117124 (Carts - Whirlpool Enthusiast)**\n> $0 fibre install on a 36-month contract\n>\n> Call me naive, but does this mean that someone who was quoted >20k for a [[TCP upgrade (Third Party Contractor)]] could connect on [[Aussie Broadband|ABB]]’s [[NBN Enterprise Ethernet|EE]] 30/30 symmetrical plan for $379 a month ($13,644 over 3 years) saving >$6k in the process??? Surely not.\n\n> **User #117124 (Carts - Whirlpool Enthusiast)**\n> What’s to stop you then connecting back into a residential plan in 3 years?\n\n> **User #105247 (Dyde - Whirlpool Forums Addict)**\n>> **Carts writes...**\n>> What’s to stop you then connecting back into a residential plan in 3 years?\n> It’s a different [[Network Termination Unit (NTU)|NTU]] for [[NBN Enterprise Ethernet|EE]], so if you wanted residential [[Fibre to the Premises (FTTP)|FTTP]] they would likely slug you an exorbitant [[TCP upgrade (Third Party Contractor)|TCP]] fee for the privilege of swapping the [[Network Termination Unit (NTU)|NTU]].\n\n> **User #117124 (Carts - Whirlpool Enthusiast)**\n>> **Dyde writes...**\n>> they would likely slug you an exorbitant [[TCP upgrade (Third Party Contractor)|TCP]] fee for the privilege of swapping the [[Network Termination Unit (NTU)|NTU]]\n> Could they do this though? Surely you would pay for part and labour itemised on an invoice?\n\n> **User #53522 (Avocet Islander - Whirlpool Forums Addict)**\n>> **Carts writes...**\n>> Surely you would pay for part and labour itemised on an invoice?\n> As someone who has a high speed private sector fibre connection, when you get to this level of connection, invoices are not that detailed so you understandably just pay the bill.\n>\n> If you can get a symmetrical 1 or 2 Gbs or under $2500 a month in the current Aussie climate, you will be doing well. _Thank goodness for good professional overseas service providers who know how to work around the Aussie BS:p_\n\n> **User #75593 (symoo - Forum Regular)**\n> We have 1 Gbps connections at all of our sites for 699.99 including regional (which I'm pretty sure is rebadged [[Telstra Fibre]]) MPLS or public doesn't make a difference in $, there has been some build costs but nothing over $5k (making it cheaper than anything else I've seen)\n\nArchive View [Return to standard view](https://forums.whirlpool.net.au/thread/30ym7769)\n</smtcmp_block>", "id": "5fce03e3-4057-4d30-9cd4-babd63c5b310", "metadata": {"model": {"providerType": "openrouter", "providerId": "openrouter", "id": "openrouter-g2.5flash", "model": "google/gemini-2.5-flash", "promptLevel": 1, "enable": true}, "usage": {"prompt_tokens": 16083, "completion_tokens": 8942, "total_tokens": 25025, "cost": 0.0271799, "is_byok": false, "prompt_tokens_details": {"cached_tokens": 0, "audio_tokens": 0}, "cost_details": {"upstream_inference_cost": null, "upstream_inference_prompt_cost": 0.0048249, "upstream_inference_completions_cost": 0.022355}, "completion_tokens_details": {"reasoning_tokens": 0, "image_tokens": 0}}}}], "createdAt": 1758417868078, "updatedAt": 1758420683456, "schemaVersion": 1}